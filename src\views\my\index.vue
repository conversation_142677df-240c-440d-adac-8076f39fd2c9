<!--
 * @Author: your name
 * @Date: 2024-11-28 09:28:01
 * @LastEditTime: 2025-03-19 15:39:57
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 未接入三端统一的 我的
 * @FilePath: \h5-interhosp\src\views\my\index copy.vue
-->
<template>
    <div class="mainCont">
        <div class="backImg"></div>
        <div class="ModContainer">
            <div class="top">
                <img
                    class="headimg"
                    src="@/images/img/default_head.png"
                    alt=""
                />
                <div v-if="$store.state.uInfo">
                    <div class="nameMod" v-if="$store.state.uInfo.name">
                        <p>{{ $store.state.uInfo.name | confuseName }}</p>
                        <img
                            class="realbg"
                            src="@/images/img/real_level.png"
                            alt=""
                        />
                        <!-- <p class="real">已实名</p> -->
                    </div>
                    <p class="mobile">
                        {{ $store.state.uInfo.phoneNo | confusePhone }}
                    </p>
                </div>
                <div v-else>
                    <div class="nameMod" @click="getLogin">
                        <p>登录/注册</p>
                    </div>
                </div>
                <div
                    class="setting"
                    v-if="$store.state.uInfo"
                    @click="toSetting"
                ></div>
            </div>
            <div
                class="Mod-common mt0"
                v-if="stageList && stageList.length > 0"
            >
                <p class="stage-title">{{ orderTitle }}</p>
                <div class="Mod-stage">
                    <div
                        class="stageList"
                        v-for="(item, index) in stageList"
                        :key="index"
                    >
                        <stage-item :data="item"></stage-item>
                    </div>
                </div>
            </div>
            <div class="Mod-common">
                <van-cell
                    title="我的会话"
                    @click="jumpUrl(talkUrl, '2', 'my_huihua', '我的会话')"
                >
                    <template #icon>
                        <img
                            class="leftimg"
                            src="@/images/mine/myTalk.png"
                            alt=""
                        />
                    </template>
                    <template #right-icon>
                        <div style="position: relative">
                            <div v-if="talkNum" class="numDiv">
                                {{ talkNum | maxTalkNum }}
                            </div>
                            <img
                                class="arrow"
                                src="@/images/img/arrow_dark.png"
                                alt=""
                            />
                        </div>
                    </template>
                </van-cell>
            </div>
            <div v-if="inAli" class="Mod-common">
                <van-cell title="我的服务包" @click="authJumpUrl()">
                    <template #icon>
                        <img
                            class="leftimg"
                            src="@/images/img/serBag.png"
                            alt=""
                        />
                    </template>
                    <template #right-icon>
                        <img
                            class="arrow"
                            src="@/images/img/arrow_dark.png"
                            alt=""
                        />
                    </template>
                </van-cell>
            </div>

            <div class="Mod-common">
                <van-cell
                    title="就诊人管理"
                    @click="
                        jumpUrl(
                            clinicUrl,
                            '2',
                            'my_jiuzhenrenguanli',
                            '就诊人管理'
                        )
                    "
                >
                    <template #icon>
                        <img
                            class="leftimg"
                            src="@/images/img/clinic.png"
                            alt=""
                        />
                    </template>
                    <template #right-icon>
                        <img
                            class="arrow"
                            src="@/images/img/arrow_dark.png"
                            alt=""
                        />
                    </template>
                </van-cell>
            </div>
            <div class="Mod-common">
                <van-cell
                    title="我的关注"
                    @click="
                        jumpUrl(focusUrl, '2', 'my_myAttention', '我的关注')
                    "
                >
                    <template #icon>
                        <img
                            class="leftimg"
                            src="@/images/img/mfocus.png"
                            alt=""
                        />
                    </template>
                    <template #right-icon>
                        <img
                            class="arrow"
                            src="@/images/img/arrow_dark.png"
                            alt=""
                        />
                    </template>
                </van-cell>
            </div>
            <div v-show="syInfirmaryShow" class="Mod-common">
                <van-cell
                    title="市府医务室在线复诊"
                    @click="
                        jumpUrlsyyws(
                            syInfirmaryUrl,
                            '1',
                            'my_syInfirmary',
                            '市府医务室在线复诊'
                        )
                    "
                >
                    <template #icon>
                        <img
                            class="leftimg"
                            src="@/images/img/syyws.png"
                            alt=""
                        />
                    </template>
                    <template #right-icon>
                        <img
                            class="arrow"
                            src="@/images/img/arrow_dark.png"
                            alt=""
                        />
                    </template>
                </van-cell>
            </div>
            <div class="Mod-common">
                <van-cell
                    title="我的优惠券"
                    @click="
                        jumpUrl(couponUrl, '0', 'my_feedback', '我的优惠券')
                    "
                >
                    <template #icon>
                        <img
                            class="leftimg"
                            src="@/images/img/yhq.png"
                            alt=""
                        />
                    </template>
                    <template #right-icon>
                        <img
                            class="arrow"
                            src="@/images/img/arrow_dark.png"
                            alt=""
                        />
                    </template>
                </van-cell>
            </div>
            <div v-if="showValidate" class="Mod-common">
                <van-cell
                    title="验证区"
                    @click="
                        jumpUrl(yanzhengUrl, '2', 'my_yanzhengqu', '验证区')
                    "
                >
                    <template #icon>
                        <img
                            class="leftimg"
                            src="@/images/mine/yanzhengqu.png"
                            alt=""
                        />
                    </template>
                    <template #right-icon>
                        <img
                            class="arrow"
                            src="@/images/img/arrow_dark.png"
                            alt=""
                        />
                    </template>
                </van-cell>
            </div>
        </div>
        <float-window
            v-if="showFloatW"
            active="geren"
            @jump="jumpPage"
        ></float-window>
    </div>
</template>

<script>
import Vue from "vue";
import common from "@/util/common";
import util from "@/util/util.js";
import { CellGroup, Cell } from "vant";
Vue.use(CellGroup).use(Cell);
import tools from "@/util/tools";
import StageItem from "../home/<USER>/stageItem.vue";
import FloatWindow from "@/components/floatWindow-kefu.vue";
import {
    userAuthorize,
    SyCheckCertNum,
    getUnreadMsgNum,
    isWhitelist,
} from "@/api/api";

export default {
    name: "index",
    data() {
        return {
            hospOrgCode: "",
            operateStartTime: new Date().getTime(),
            userInfo: {},
            talkUrl: location.origin + location.pathname + "/#/msgTalk",
            clinicUrl:
                "https://www.hfi-health.com:28181/appointWithDoc/#/linkUsers?origin=hlwywMini",
            focusUrl: location.origin + location.pathname + "#/myAttention",
            consultUrl: location.origin + location.pathname + "#/consultList",
            feedbackUrl:
                location.origin + `/${process.env.VUE_APP_FOLLOW}/#/question`,
            couponUrl: location.origin + location.pathname + `#/couponList`,
            yanzhengUrl:
                location.origin +
                `/${process.env.VUE_APP_SMKHEALTH}/#/validate`,
            stageList: [],
            showFloatW: false,
            unicode: "",
            hospName: "",
            hospId: "",
            // 市一医务室链接
            syInfirmaryUrl:
                "alipays://platformapi/startapp?appId=2021002193686965&page=pages%2Findex%2Findex%3Furl%3Dhttps%253A%252F%252Fmediinfo-ddjy-web.mediinfo.cn%252F%2523%252Fsubpages%252Fmiddle%252Fmiddle%253FjiGouID%253D0009%2526isAlipayMini%253D1%2526pageType%253DthirdMini%2526authNextUrl%253D%25252Fsubpages%25252FfuZhenPY%25252FFuZhenPYList%25253FisSFVIP%25253D1",
            // "alipays://platformapi/startapp?appId=2021002193686965&page=pages%2Findex%2Findex%3Furl%3Dhttps%253A%252F%252Fmediinfo-ddjy-web.mediinfo.cn%252F%2523%252Fsubpages%252FzaiXianWZ%252FYiShengLB%253FjiGouID%253D0009%2526isAlipayMini%253D1%2526pageType%253DthirdMini%2526query%253D%257B%2522leiXingDM%2522%253A%25222%2522%252C%2522leiXingMC%2522%253A%2522%25E5%2586%2585%25E7%25A7%2591%2522%252C%2522keShiDM%2522%253A%25221010400%2522%252C%2522keShiMC%2522%253A%2522%25E5%25B8%2582%25E5%25BA%259C%25E5%258C%25BB%25E5%258A%25A1%25E5%25AE%25A4%2522%257D",
            // 市一医务室显隐
            syInfirmaryShow: false,
            talkNum: "",
            showValidate: false,
            fatherName: "健康互联网医院-我的",
            inAli: false,
        };
    },
    components: {
        StageItem,
        FloatWindow,
    },
    created() {
        this.hospOrgCode = this.$route.query.hospOrgCode;
        if (
            window.localStorage.getItem("orgId") ||
            common.getUrlParam("orgId")
        ) {
            this.showFloatW = true;
        }
        if (window.localStorage.getItem("userInfo")) {
            this.userInfo = JSON.parse(window.localStorage.getItem("userInfo"));
        }
        // app跳转来源（如：萧山门户跳转进来），用于首页跳转回原来的小程序
        if (common.getUrlParam("source")) {
            window.sessionStorage.setItem(
                "appSource",
                common.getUrlParam("source")
            );
        }
        this.unicode = this.$route.query.unicode || "";
        this.hospName = this.$route.query.hospName || "";
        this.hospId = this.$route.query.orgId || "";
        if (navigator.userAgent.indexOf("smkVersion") > -1) {
            // 在市民卡，且白名单内的有验证区
            isWhitelist({
                phoneNum: JSON.parse(window.sessionStorage.getItem("userInfo"))
                    .phone,
                channel: "2",
            }).then((res) => {
                if (res.value.state === "true") {
                    this.showValidate = true;
                }
            });
        }
        if (localStorage.getItem("interHosp_origin") == "jktwxMini") {
            this.fatherName = "金投云健康-我的";
        } else if (
            sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5"
        ) {
            // 市民卡，医疗健康
            this.fatherName = "医疗健康-我的";
        }
        this.getList();
        this.getTalkNum();
    },
    mounted() {
        let userId = window.localStorage.getItem("userId");
        if (navigator.userAgent.indexOf("AliApp") > -1) {
            //
            this.inAli = true;
        }
        if (navigator.userAgent.indexOf("AliApp") > -1 && userId) {
            SyCheckCertNum(userId).then((res) => {
                console.log("获取市一白名单", res);
                this.syInfirmaryShow = res;
            });
        }
    },
    beforeDestroy() {
        tools.handleSetPoint({
            trackingContent: "我的",
            operateStartTime: this.operateStartTime,
        });
    },
    methods: {
        // 获取我的会话的未读数
        getTalkNum() {
            console.log(
                "会话纬度数用户信息",
                window.localStorage.getItem("userId")
            );
            console.log(
                "未读数用户信息",
                !window.localStorage.getItem("userId")
            );
            if (window.localStorage.getItem("userId")) {
                getUnreadMsgNum({
                    userId: window.localStorage.getItem("userId"),
                }).then((res) => {
                    console.log("会话未读数", res);
                    if (res && res.allUnreadMsgNum) {
                        this.talkNum = res.allUnreadMsgNum;
                    }
                });
            }
        },
        getList() {
            const that = this;
            this.$store.dispatch("queryAllStage", this.fatherName).then(() => {
                var homepage = {};
                console.log("----", this.$store.state.allStageList);
                this.$store.state.allStageList.forEach((item, index) => {
                    if (item.stageName == this.fatherName) {
                        homepage = item;
                    }
                });
                if (
                    !(
                        homepage &&
                        homepage.stageList &&
                        homepage.stageList.length > 0
                    )
                ) {
                    return;
                }
                homepage.stageList.forEach((item, index) => {
                    if (item.childStageName.indexOf("订单中心") > -1) {
                        // let
                        that.orderTitle = item.childStageName;
                        console.log("标题", that.orderTitle);
                        that.stageList = item.childStageList;
                        console.log(that.stageList);
                    }
                });
            });
        },
        toSetting() {
            this.$router.push("/setting");
        },
        jumpUrl(a, b, applicationCode, applicationName) {
            tools
                .handleSetPoint({
                    trackingContent: `我的|${applicationName}`,
                })
                .then(() => {
                    tools.jumpUrl(a, b);
                });
        },
        async jumpUrlsyyws(url, status, applicationCode, applicationName) {
            // 市一医务室 需要实现免登，用户信息以query传递，小程序方在app.vue的onLaunch中接收
            if (status !== 0 && !this.$store.state.uInfo) {
                if (!(await this.$store.dispatch("getLogin"))) {
                    Toast({
                        message: "用户信息获取失败",
                        className: "LTip",
                    });
                    return;
                }
            }

            let userinfo =
                sessionStorage.getItem("encrUserMini") ||
                localStorage.getItem("encrUserMini");
            let queryData = "uInfo=" + userinfo + "&isSFVIP=true";

            let item = {};

            // item.jumpUrl = `alipays://platformapi/startapp?appId=2021002193686965&query=${encodeURIComponent(
            //     queryData
            // )}&page=pages%2Findex%2Findex%3Furl%3Dhttps%253A%252F%252Fmediinfo-ddjy-web.mediinfo.cn%252F%2523%252Fsubpages%252Fmiddle%252Fmiddle%253FjiGouID%253D0009%2526isAlipayMini%253D1%2526pageType%253DthirdMini%2526authNextUrl%253D%25252Fsubpages%25252FfuZhenPY%25252FFuZhenPYList%25253FisSFVIP%25253D1`;

            // 新市府医务室地址
            item.jumpUrl = `alipays://platformapi/startapp?appId=2021002193686965&query=${encodeURIComponent(
                queryData
            )}&page=subpages%2FfuZhenPY%2FFuZhenPYList%3FisSFVIP%3D1`;
            // syInfirmaryUrl 市一医务室，需要跳转到市一的小程序
            // item.jumpUrl = url;
            item.appType = "3";
            item.miniId = "2021002193686965";
            item.status = status;
            tools.jumpUrlManage(item);
        },
        jumpPage(val) {
            console.log("jjjjjj", val);
            if (val == "jigou") {
                this.$router.replace({
                    path: "hosporg",
                    query: {
                        orgId: this.hospId,
                        unicode: this.unicode,
                        hospName: this.hospName,
                        hospOrgCode: this.hospOrgCode,
                    },
                });
            }
        },
        getLogin() {
            this.$store.dispatch("getLogin");
        },
        async authJumpUrl() {
            await tools.handleSetPoint({
                trackingContent: "我的|我的服务包",
            });
            let clientId = process.env.VUE_APP_ServiceBagClientId; // 市三服务包clientId
            tools.jumpUrlManage({
                clientId: clientId,
                status: "2",
                jumpUrl: `${process.env.VUE_APP_ServiceBagUrl}/h5/patient/myOrders/list?projectId=1g64mbep41moiorf1vksgpe1k72mjf46&orgId=1hflbdrheoj89gt2bvtplc10pn0v93h2&appId=1hflmfdpr2orh6gjcq5is92bfokaa2sp`,
            });
            return;
            let userId = window.localStorage.getItem("userId");
            // userId = "d8332783-c0f5-4cd8-88c8-3538098ec243"; // 测试userId
            // let res = {
            //   code: "cbdb2d79883f11ee93d3893705813c75",
            // };
            userAuthorize(userId, clientId).then((res) => {
                console.log("auth授权", res);
                if (res) {
                    let timeStamp = new Date().getTime();
                    let url = `${process.env.VUE_APP_ServiceBagUrl}/h5/patient/myOrders/list?projectId=1g64mbep41moiorf1vksgpe1k72mjf46&orgId=1hflbdrheoj89gt2bvtplc10pn0v93h2&appId=1hflmfdpr2orh6gjcq5is92bfokaa2sp&userCode=${res.code}&time=${timeStamp}`;
                    window.location.href = url;

                    console.log(
                        "code链接市三服务包 我的 我的服务包 url ======",
                        url
                    );
                }
            });
        },
    },
    filters: {
        confusePhone: function (val) {
            if (!val) return "";
            return (
                val.substring(0, 3) + "******" + val.substring(val.length - 2)
            );
        },
        confuseName: function (name) {
            let enName = [];
            if (name.length === 2) {
                return "*" + name[1];
            } else {
                let num = name.length - 1;
                // enName.push(name[0])
                for (let i = 0; i < num; i++) {
                    enName.push("*");
                }
                enName.push(name[name.length - 1]);
                return enName.join("");
            }
        },
        maxTalkNum(value) {
            let v = parseInt(value);
            if (v && v > 99) {
                v = "99+";
            }
            return v;
        },
    },
};
</script>

<style lang="less" scoped>
.mainCont {
    overflow: scroll;
}

.backImg {
    height: 232px;
    background: url("@/images/img/minebg.png");
    background-size: 100% auto;
}

.top {
    margin-top: -232px;
    position: relative;
    padding: 25px 16px 25px 22px;
    display: flex;
    justify-content: flex-start;
    justify-items: center;
    align-content: center;
    align-items: center;
}

.headimg {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    margin-right: 17px;
}

.nameMod {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 9px;
}

.nameMod p {
    font-size: 21px;
    font-family: "PingFang SC";
    font-weight: 500;
    color: #141f35;
    margin-right: 9px;
}

.realbg {
    width: 60px;
    height: 18px;
    background-size: 100% auto;
}

.real {
    width: 80px;
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
}

.mobile {
    font-size: 13px;
    font-family: "PingFang SC";
    font-weight: 400;
    color: #657085;
}

.setting {
    position: absolute;
    right: 30px;
    top: 35px;
    width: 21px;
    height: 22px;
    background: url("@/images/img/setting.png");
    background-size: 100% auto;
}

.ModContainer {
    background-color: transparent;
    margin-bottom: 232px;
}

.Mod-common {
    margin: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
}

.stage-title {
    margin: 15px;
    font-size: 16px;
    font-family: "PingFang SC";
    font-weight: 500;
    color: #333333;
}

.Mod-stage {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding-top: 15px;
}

.stageList {
    flex-shrink: 0;
    width: 33.333%;
    margin-bottom: 15px;
}

.leftimg {
    width: 25px;
    height: 25px;
    background-size: 100% auto;
    margin-right: 15px;
}

.arrow {
    width: 7px;
    height: 12px;
    object-fit: fill;
}

.mt0 {
    margin-top: 0;
}

::v-deep .van-cell {
    align-items: center;
    padding: 25px 16px;
}

::v-deep .van-cell__title {
    font-size: 15px;
    font-family: "PingFang SC";
    font-weight: 400;
    color: #141f35;
}

.numDiv {
    background: #ff0000;
    min-width: 0.2rem;
    height: 0.2rem;
    line-height: 0.2rem;
    text-align: center;
    border-radius: 50%;
    color: #fff;
    position: absolute;
    right: 0.2rem;
    padding: 1px;
}
</style>
