<template>
  <div>
    <div class="content">
      <van-field
        v-model="message"
        rows="8"
        autosize
        type="textarea"
        maxlength="500"
        placeholder="请输入您要反馈的问题，我们将尽快解决"
        show-word-limit
      />
    </div>
    <div class="submit" @click="handleSubmit">提交</div>
  </div>
</template>

<script>
import Vue from "vue";
import { Field } from "vant";
Vue.use(Field);

export default {
  data() {
    return {
      message: "",
    };
  },
  mounted() {},
  methods: {
    handleSubmit() {
      this.$dialogBox({
        title: "已提交",
        confirmTxt: "我知道了",
        cancelTxt: "",
        cancelCallback: function () {},
        confirmCallback: function () {},
      });
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  margin: 12px 15px;
}
.submit {
  position: absolute;
  left: 15px;
  bottom: 45px;
  right: 15px;
  height: 40px;
  line-height: 40px;
  background: #01cda7;
  border-radius: 20px;
  font-size: 18px;
  color: #ffffff;
  text-align: center;
}
::v-deep .van-cell {
  border-radius: 8px;
}
</style>
