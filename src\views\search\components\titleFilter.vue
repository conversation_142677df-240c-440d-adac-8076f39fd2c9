<!--Author: 郭元扬
ProjectDescription: 筛选组件（职称）
CreateTime: 2023-06-09
UpdateTime: 2023-06-09-->

<template>
    <div class="title-filter">
        <div>
            <p class="title" v-if="type !== 'repeatAsk'">排序</p>
            <div class="flex-box" v-if="type !== 'repeatAsk'">
                <p @click="select1(1)" :class="{ selected: choose1 === 1 }">
                    综合排序
                </p>
                <!-- <p @click="select1(2)" :class="{ selected: choose1 === 2 }">
          价格由低到高
        </p> -->
            </div>
            <p class="title">类型</p>
            <div class="flex-box">
                <p @click="select3(3)" :class="{ selected: choose3 === 3 }">
                    不限
                </p>
                <p @click="select3('0')" :class="{ selected: choose3 === '0' }">
                    个人
                </p>
                <p @click="select3('1')" :class="{ selected: choose3 === '1' }">
                    团队
                </p>
            </div>
            <p class="title">职称</p>
            <div class="flex-box">
                <p
                    @click="select2({ typeKey: 0 }, -2)"
                    :class="{ selected: choose2 === -2 }"
                >
                    不限
                </p>
                <p
                    v-for="(item, index) of types"
                    :key="index"
                    @click="select2(item, index)"
                    :class="{ selected: choose2 === index }"
                >
                    {{ item.typeValue }}
                </p>
            </div>
            <div class="footer">
                <p @click="reset">重置</p>
                <p @click="confirm">确认</p>
            </div>
        </div>
    </div>
</template>

<script>
import { getDictionary } from "@/api/api";
export default {
    props: {
        type: {
            type: String,
            default: () => "normal",
        },
    },
    data() {
        return {
            types: [],
            choose1: -1,
            choose2: -1,
            choose3: -1,
            value1: "",
            value2: "",
            value3: "",
        };
    },
    methods: {
        confirm() {
            this.$emit("sendFilter", this.value2, this.value1, this.value3);
        },
        reset() {
            this.value1 = "";
            this.value2 = "";
            this.value3 = "";
            this.choose1 = -1;
            this.choose2 = -1;
            this.choose3 = -1;
        },
        select1(val) {
            this.choose1 = val;
            this.value1 = val;
        },
        select2(val, index) {
            this.value2 = val.typeKey;
            this.choose2 = index;
        },
        select3(val) {
            if (String(val) !== "3") {
                this.value3 = val;
            } else {
                this.value3 = "";
            }
            this.choose3 = val;
        },
        handleGetFilter(id) {
            getDictionary({ typeId: id }).then((res) => {
                if (res) {
                    window.sessionStorage.setItem(
                        "doctorProfessional",
                        JSON.stringify(res)
                    );
                    res.pop();
                    this.types = res;
                }
            });
        },
    },
    created() {
        let doctorP = window.sessionStorage.getItem("doctorProfessional");
        if (doctorP) {
            this.types = JSON.parse(doctorP);
        } else {
            this.handleGetFilter("106");
        }
    },
};
</script>

<style lang="less" scoped>
.title-filter {
    padding: 0.19rem 0 0 0;
    border-radius: 0 0 0.1rem 0.1rem;
    max-height: 60vh;
    overflow: hidden;
    position: relative;
    & > div {
        max-height: calc(60vh - 0.5rem);
        overflow: scroll;
        padding-bottom: 0.5rem;
    }
    .title {
        font-size: 0.12rem;
        color: #777777;
        text-align: left;
        margin-bottom: 0.12rem;
        padding: 0 0.15rem 0 0.16rem;
    }
    .flex-box {
        display: flex;
        flex-wrap: wrap;
        padding: 0 0.15rem 0.25rem 0.16rem;
        p {
            width: 1.08rem;
            height: 0.3rem;
            background: #f6f6f6;
            border-radius: 0.04rem;
            margin-right: 0.1rem;
            margin-bottom: 0.1rem;
            text-align: center;
            line-height: 0.3rem;
            border: 1px solid #f6f6f6;
            box-sizing: border-box;
            &:nth-child(3n + 3) {
                margin-right: 0;
            }
        }
    }
    .footer {
        display: flex;
        padding: 0.15rem;
        position: absolute;
        bottom: 0;
        background: white;
        border-top: 1px solid #e8e9ec;
        p {
            font-size: 0.14rem;
            text-align: center;
            line-height: 0.35rem;
            &:nth-of-type(1) {
                width: 1.45rem;
                height: 0.35rem;
                border: 1px solid #d0d4dd;
                border-radius: 0.18rem;
                background: #ffffff;
                color: #333333;
                margin-right: 0.1rem;
            }
            &:nth-of-type(2) {
                width: 1.9rem;
                height: 0.35rem;
                color: #ffffff;
                border-radius: 0.18rem;
                background: #01cda7;
                border: 1px solid #01cda7;
            }
        }
    }
    .selected {
        color: #3ebfa0;
        border: 1px solid #3ebfa0 !important;
        background: #ebfffa !important;
    }
}
</style>
