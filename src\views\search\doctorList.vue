<!--Author: 郭元扬
ProjectDescription: 搜索页面医生列表
CreateTime: 2023-06-08
UpdateTime: 2023-06-08-->

<template>
    <div class="search-doctor-list">
        <div class="search-input">
            <form action="javascript:;" style="height: 0.55rem">
                <div class="s-div">
                    <input
                        class="s-input"
                        v-model="searchValue"
                        type="search"
                        :placeholder="placeholder"
                        autocomplete="off"
                        @keyup.13="onSearch"
                    />
                    <img src="@/images/search/sousuo.png" alt="" />
                    <img
                        @click="onCancel"
                        v-if="searchValue"
                        src="@/images/search/s-del.png"
                        alt=""
                    />
                    <span @click="goBack">取消</span>
                </div>
            </form>
        </div>
        <div class="select">
            <van-dropdown-menu>
                <!-- 医院 -->
                <van-dropdown-item
                    v-if="showHospitalSelect"
                    :title="title1"
                    v-model="value1"
                    ref="item1"
                    key="hospital"
                    @opened="opened"
                >
                    <hospitalSelector ref="treeHosp" @sendSelect="sendSelect"
                /></van-dropdown-item>
                <!-- 科室 -->
                <van-dropdown-item
                    class="deptclass"
                    :title="title2"
                    ref="item2"
                    v-model="value2"
                    key="department1"
                    @opened="openedDep"
                >
                    <selector
                        :list="newDepList"
                        @sendDept="sendDept"
                        ref="dep"
                    />
                </van-dropdown-item>
                <!-- 综合 -->
                <van-dropdown-item
                    v-if="showCompositeSelect"
                    :title="title4"
                    v-model="value4"
                    :options="option4"
                    @change="change4"
                    key="composite"
                />
                <!-- 筛选 -->
                <van-dropdown-item
                    :title="title3"
                    v-model="value3"
                    ref="item3"
                    key="filter"
                    :type="type"
                >
                    <titleFilter :type="type" @sendFilter="sendFilter" />
                </van-dropdown-item>
            </van-dropdown-menu>
        </div>
        <div class="content">
            <!-- 医生列表 -->
            <div v-if="doctorList.length">
                <van-list
                    v-model="loading"
                    :finished="finished"
                    finished-text="没有更多了"
                    :immediate-check="false"
                    @load="handleGetDoctorList"
                >
                    <doctor-list
                        :doctorList="doctorList"
                        :repeatVisit="type === 'repeatAsk'"
                        :online-visit="type === 'ask'"
                    />
                </van-list>
            </div>
            <!-- 空页面 -->
            <div
                class="no-data"
                v-else-if="canShowNoData && !doctorList.length"
            >
                <img src="@/images/search/no-data.png" alt="" />
                <p>暂无搜索结果</p>
            </div>
        </div>
    </div>
</template>

<script>
import { Search, DropdownMenu, DropdownItem } from "vant";
import {
    getDepartmentTypeTree,
    getDoctorList,
    getDoctorListForSz,
    departClassListByUnicode,
} from "@/api/api";
import hospitalSelector from "./components/hospitalSelector.vue";
import titleFilter from "./components/titleFilter.vue";
import doctorList from "@/components/doctorList.vue";
import { List } from "vant";
import common from "@/util/common";
import tools from "@/util/tools";
import selector from "./components/select-item.vue";
export default {
    data() {
        return {
            placeholder: "搜索医院、医生、病症、科室",
            newDepList: [],
            title1: "医院",
            title2: "科室",
            title3: "筛选",
            title4: "综合",
            departments: [],
            doctorList: [],
            searchValue: "",
            value1: "",
            value2: "",
            value3: "",
            value4: "1",
            option2: [
                {
                    typeValue: "全部科室",
                    typeKey: "",
                },
            ],
            option4: [
                {
                    text: "综合排序",
                    value: "1",
                },
                // {
                //     text: "价格由低到高",
                //     value: "6",
                // },
                // {
                //   text: "复诊量",
                //   value: "5",
                // },
                {
                    text: "评分",
                    value: "4",
                },
            ],
            unicode: "",
            hospName: "",
            hospLevel: "",
            deptId: "",
            deptName: "",
            orderByType: 3,
            teamFlag: "",
            levelCode: "",
            returnVisitEnable: "",
            serviceEnable: "",
            type: "normal",
            // 医院选择显示隐藏
            showHospitalSelect: true,
            // 综合选择显示隐藏
            showCompositeSelect: false,
            loading: false,
            finished: false,
            pageNum: 1,
            // 显示无数据组件（等接口请求完成后）
            canShowNoData: false,
        };
    },
    components: {
        [Search.name]: Search,
        [DropdownItem.name]: DropdownItem,
        [DropdownMenu.name]: DropdownMenu,
        [List.name]: List,
        hospitalSelector,
        titleFilter,
        doctorList,
        selector,
    },
    methods: {
        opened() {
            this.$refs.treeHosp.initH();
        },
        openedDep() {
            this.$refs.dep.initH();
        },
        // 跳转逻辑判断
        handleBeforeRouterEnter() {
            // type ask咨询 repeatAsk复诊 空值则没限制
            // from从哪里跳转过来 home首页 hospital机构 空值则没限制
            const {
                from,
                type,
                department,
                searchValue,
                deptClassCode,
                deptId,
            } = this.$route.query;
            if (searchValue) {
                this.searchValue = decodeURIComponent(searchValue);
            }
            if (deptId) {
                this.deptId = deptId;
                // for (const item of this.newDepList) {
                //   if (item.deptFirstClassCode === department) {
                //     this.title2 = item.deptFirstClassName;
                //     if (deptClassCode) {
                //       this.deptClassCode = deptClassCode;
                //       for (const it of item.deptClassList) {
                //         if (it.deptClassCode === deptClassCode) {
                //           this.title2 = it.deptClassName;
                //         }
                //       }
                //     }
                //   }
                // }
                // this.change2(department, "nocall");
            } else if (deptClassCode || department) {
                this.deptClassCode = deptClassCode || department;
            }
            if (from && from !== "home") {
                this.showHospitalSelect = false;
                this.unicode = from;
                this.serviceEnable = "";
                this.orderByType = "";
                this.handleDepartClassListByUnicode(department);
                this.placeholder = "搜索医生、病症、科室";
            }
            if (type) {
                this.type = type;
                this.serviceEnable = "";
                this.orderByType = "";
                if (type === "ask") {
                    this.serviceEnable = 1;
                    // 在线就诊进来默认按接诊量排序
                    this.orderByType = 3;
                } else if (type === "repeatAsk") {
                    this.returnVisitEnable = 1;
                    // 在线复诊进来默认按复诊量排序（2023.9.8在线复诊进来默认按接诊量排序）
                    this.orderByType = 3;
                    this.showCompositeSelect = true;
                }
            }
            this.handleGetDoctorList();
        },
        // 二三级特色科室回显
        showDeptName() {
            let secondeList = [],
                secondeFatherName,
                thirdList,
                thirdFatherName,
                deptName;
            const handleName = (item) => {
                console.log(item, "item");
                if (item.deptFirstClassCode) {
                    secondeList = item.deptClassList
                        ? item.deptClassList.concat(item.deptList || [])
                        : item.deptList || [];
                    secondeFatherName = item.deptFirstClassName;
                } else if (item.deptClassCode) {
                    thirdList = item.deptList || [];
                    thirdFatherName = item.deptClassName;
                } else {
                    deptName = item.deptName;
                }
            };

            // 如果有一级科室
            if (this.$route.query.department) {
                for (const item of this.newDepList) {
                    if (
                        item.deptFirstClassCode === this.$route.query.department
                    ) {
                        secondeFatherName = item.deptFirstClassName;
                    }
                }
            }
            // 如果有二级科室
            if (
                this.$route.query.deptClassCode ||
                (!this.$route.query.deptClassCode && this.$route.query.deptId)
            ) {
                const secondId =
                    this.$route.query.deptClassCode || this.$route.query.deptId;
                for (const item of this.newDepList) {
                    if (
                        item.deptFirstClassCode === this.$route.query.department
                    ) {
                        handleName(item);
                    }
                }
                for (const second of secondeList) {
                    if (second.deptClassCode === secondId) {
                        handleName(second);
                    }
                }
            }
            // 如果有三级科室
            if (this.$route.query.deptClassCode && this.$route.query.deptId) {
                for (const third of thirdList) {
                    if (third.deptId === this.$route.query.deptId) {
                        handleName(third);
                    }
                }
            }
            this.title2 =
                deptName || thirdFatherName || secondeFatherName || "科室";
        },
        // 根据医院查科室列表
        handleDepartClassListByUnicode(department) {
            let data = {
                unicode: this.unicode,
            };
            if (this.$route.query.type) {
                if (this.$route.query.type === "ask") {
                    data.serviceEnable = 1;
                } else if (this.$route.query.type === "repeatAsk") {
                    data.returnVisitEnable = 1;
                }
            }
            departClassListByUnicode(data).then((res) => {
                this.newDepList = res.deptClassList;
                this.showDeptName();
            });
        },
        handleGetDoctorList() {
            this.canShowNoData = false;
            const data = {
                pageNum: this.pageNum,
                pageSize: 10,
                keyword: this.searchValue,
            };
            this.unicode ? (data["unicode"] = this.unicode) : "";
            this.hospName ? (data["hospName"] = this.hospName) : "";
            this.hospLevel ? (data["hospLevel"] = this.hospLevel) : "";
            this.deptName ? (data["deptClassName"] = this.deptName) : "";
            this.deptClassCode
                ? (data["deptClassCode"] = this.deptClassCode)
                : "";
            this.deptId ? (data["deptId"] = this.deptId) : "";
            this.levelCode ? (data["levelCode"] = this.levelCode) : "";
            this.orderByType ? (data["orderByType"] = this.orderByType) : "";
            this.teamFlag ? (data["teamFlag"] = this.teamFlag) : "";
            this.serviceEnable
                ? (data["serviceEnable"] = this.serviceEnable)
                : "";
            this.returnVisitEnable
                ? (data["returnVisitEnable"] = this.returnVisitEnable)
                : "";
            sessionStorage.getItem("hlw_remoteChannel") == "xhmhMini"
                ? (data["isHospTypeOrder"] = true)
                : "";
            // if (this.unicode === "2000096") {
            //   // 为市中单独写一个查询接口
            //   getDoctorListForSz(data).then(res => {
            //     this.loading = false;
            //     if (res) {
            //       var doctorList = res.list;
            //       this.doctorList = this.doctorList.concat(doctorList);
            //       let pages = res.pages;
            //       if (this.pageNum >= pages) {
            //         this.finished = true;
            //       } else {
            //         this.pageNum++;
            //       }
            //     }
            //   });
            // } else {
            getDoctorList(data)
                .then((res) => {
                    debugger;
                    this.canShowNoData = true;
                    this.loading = false;
                    if (res) {
                        var doctorList = res.list;
                        this.doctorList = this.doctorList.concat(doctorList);
                        let pages = res.pages;
                        if (this.pageNum >= pages) {
                            this.finished = true;
                        } else {
                            this.pageNum++;
                        }
                    }
                })
                .catch(() => {
                    this.canShowNoData = true;
                });
            const source = this.$route.query.hospName
                ? this.$route.query.hospName
                : "首页";
            const hospName =
                this.hospName && this.hospName !== "不限"
                    ? "|" + this.hospName
                    : "";
            const deptName = this.title2 !== "科室" ? "|" + this.title2 : "";
            tools.handleSetPoint({
                trackingContent: source + "|搜索" + hospName + deptName,
                searchTerm: this.searchValue,
                orgId: this.unicode,
                orgName: this.hospName || (source !== "首页" ? source : ""),
            });
        },
        goBack() {
            console.log("his长度", history.length);
            let origin = common.getUrlParam("origin");
            if (origin == "wjAliMini") {
                // window.location.href = location.origin + location.pathname + "#/home";
                my.navigateBack();
            } else {
                window.history.go(-1);
            }
        },
        onCancel() {
            this.searchValue = "";
        },
        onSearch() {
            this.resetPagingData();
            this.handleGetDoctorList();
        },
        sendDept(name, val, title) {
            this[name] = val;
            this.title2 = title;
            if (name === "deptId") {
                this.deptClassCode = "";
            } else {
                this.deptId = "";
            }
            this.$refs.item2.toggle();
            this.resetPagingData();
            this.handleGetDoctorList();
        },
        // 获取全部一级科室
        handelGetDepartments() {
            getDepartmentTypeTree().then((res) => {
                for (const item of res) {
                    item.deptFirstClassCode = item.typeKey;
                    item.deptFirstClassName = item.typeValue;
                    if (item.children) {
                        item.deptClassList = item.children;
                        for (const it of item.deptClassList) {
                            it.deptClassCode = it.typeKey;
                            it.deptClassName = it.typeValue;
                        }
                    }
                }
                this.newDepList = res;
                this.showDeptName();
                console.log(this.newDepList, "newDepList");
                window.sessionStorage.setItem(
                    "departmentDictionary",
                    JSON.stringify(this.newDepList)
                );
                this.handleBeforeRouterEnter();
            });
        },
        change2(val, type) {
            for (const item of this.option2) {
                if (item.value == val) {
                    if (item.text === "全部科室") {
                        this.title2 = "科室";
                    } else {
                        this.title2 = item.text;
                    }
                    this.value2 = val;
                }
            }
            this.deptName = this.title2;
            this.deptClassCode = this.value2 ? this.value2 : val;
            if (!type) {
                this.resetPagingData();
                this.handleGetDoctorList();
            }
        },
        change4(val) {
            for (const item of this.option4) {
                if (item.value == val) {
                    if (item.text === "综合排序") {
                        this.title4 = "综合";
                    } else {
                        this.title4 = item.text;
                    }
                    this.value4 = val;
                }
            }
            this.orderByType = val;
            this.resetPagingData();
            this.handleGetDoctorList();
        },
        sendSelect(val) {
            this.$refs.item1.toggle();
            const { unicode, hospName, hospLevel } = val;
            this.unicode = unicode;
            this.hospName = hospName;
            this.hospLevel = hospLevel;
            if (hospName === "不限") {
                this.title1 = "医院";
            } else {
                this.title1 = hospName;
            }
            this.value1 = unicode;
            this.resetPagingData();
            this.handleGetDoctorList();
        },
        sendFilter(val, val2, val3) {
            this.$refs.item3.toggle();
            if (val !== undefined) {
                this.levelCode = val;
            }
            if (this.type != "repeatAsk") {
                this.orderByType = val2 ? val2 : "";
            }
            this.teamFlag = val3 ? val3 : "";
            this.resetPagingData();
            this.handleGetDoctorList();
        },
        resetPagingData() {
            this.pageNum = 1;
            this.doctorList = [];
            this.finished = false;
        },
    },
    created() {
        let DepartDict = window.sessionStorage.getItem("departmentDictionary");
        if (DepartDict) {
            this.newDepList = JSON.parse(DepartDict);
            this.showDeptName();
            this.handleBeforeRouterEnter();
        } else {
            this.handelGetDepartments();
        }
    },
};
</script>

<style lang="less" scoped>
.search-doctor-list {
    .search-input {
        width: 100%;
        height: 0.55rem;
        background: #ffffff;
        position: relative;
        .s-div {
            & > img {
                position: absolute;
                &:nth-of-type(1) {
                    width: 0.13rem;
                    height: 0.14rem;
                    top: 0.2rem;
                    left: 0.32rem;
                }
                &:nth-of-type(2) {
                    width: 0.15rem;
                    height: 0.15rem;
                    top: 0.2rem;
                    right: 0.9rem;
                }
            }
            span {
                font-size: 0.15rem;
                font-family: PingFang SC;
                font-weight: 400;
                color: #7a7a7a;
                position: absolute;
                right: 0.26rem;
                top: 50%;
                transform: translateY(-50%);
            }
        }
        .s-input {
            width: 2.75rem;
            box-sizing: border-box;
            position: absolute;
            height: 0.33rem;
            border-radius: 0.14rem;
            background-color: #f5f5f5;
            font-size: 0.12rem;
            color: #363636;
            padding-left: 0.34rem;
            padding-right: 0.34rem;
            margin-right: 0.2rem;
            margin-left: 0.2rem;
            top: 0.11rem;
            border: none;
        }
    }
    .doctor-list {
        .doctor-info {
            width: 3.45rem;
            margin: 0 auto;
            margin-top: 0.12rem;
            margin-bottom: 0.12rem;
            box-sizing: border-box;
            background: #ffffff;
            box-shadow: 0.02rem 0.02rem 0.1rem 0 rgba(186, 186, 186, 0.2);
            border-radius: 0.08rem;
            display: flex;
            padding: 0.13rem 0.16rem 0.13rem 0.11rem;
            .header {
                width: 0.47rem;
                height: 0.47rem;
                min-width: 0.47rem;
                min-height: 0.47rem;
                border-radius: 50%;
                overflow: hidden;
                margin-right: 0.13rem;
                img {
                    width: 0.47rem;
                    //   height: 0.47rem;
                }
            }
        }
        .info {
            .row1 {
                display: flex;
                align-items: center;
                margin-bottom: 0.1rem;
                p {
                    font-size: 0.13rem;
                    font-family: PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    &:nth-of-type(1) {
                        font-size: 0.15rem;
                        font-family: PingFang SC;
                        font-weight: 500;
                        color: #333333;
                    }
                    &:nth-of-type(2) {
                        margin: 0 0.1rem;
                    }
                }
            }
            .row2 {
                display: flex;
                p {
                    &:nth-of-type(1) {
                        font-size: 0.2rem;
                        font-family: PingFang SC;
                        font-weight: 400;
                        color: #3ebfa0;
                        width: 1.2rem;
                        height: 0.34rem;
                        text-align: center;
                        line-height: 0.34rem;
                        border: 1px solid #3ebfa0;
                        border-radius: 0.04rem;
                        transform: scale(0.5);
                        margin-left: -0.3rem;
                        margin-top: -0.085rem;
                    }
                    &:nth-of-type(2) {
                        font-size: 0.13rem;
                        color: #686b73;
                        margin-left: -0.2rem;
                        margin-top: 0.01rem;
                    }
                }
            }
            .row3 {
                font-size: 0.13rem;
                color: #888888;
                margin-bottom: 0.12rem;
            }
            .row4 {
                display: flex;
                margin-bottom: 0.15rem;
                p {
                    font-size: 0.11rem;
                    color: #888888;
                    span {
                        color: #3ebfa0;
                        margin-left: 0.04rem;
                    }
                    &:nth-of-type(1) {
                        margin-right: 0.25rem;
                    }
                }
            }
            .row5 {
                display: flex;
                p {
                    width: 0.92rem;
                    height: 0.28rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border: 1px solid #e6e6e6;
                    border-radius: 0.14rem;
                    font-size: 0.12rem;
                    color: #666666;
                    span {
                        font-size: 0.15rem;
                        font-weight: bold;
                        color: #3ebfa0;
                        margin-left: 0.03rem;
                    }
                    &:nth-of-type(1) {
                        margin-right: 0.1rem;
                    }
                }
            }
        }
    }
    .no-data {
        width: 1.07rem;
        height: 1.2rem;
        margin: 0 auto;
        padding-top: 1.1rem;
        img {
            width: 1.07rem;
            height: 1.2rem;
        }
        p {
            font-size: 0.14rem;
            font-family: PingFang SC;
            color: #777777;
            margin-top: 0.2rem;
        }
    }
    /deep/.select {
        .van-dropdown-menu {
            .van-dropdown-menu__bar {
                box-shadow: none !important;
            }
        }
        .van-dropdown-menu__title {
            color: #333333;
        }
        .van-dropdown-menu__title::after {
            color: #333333;
        }
        .van-dropdown-item__content {
            border-radius: 0 0 0.1rem 0.1rem;
        }
        .van-dropdown-item__option--active {
            color: #3ebfa0;
        }
        .van-dropdown-item__option--active .van-dropdown-item__icon {
            color: #3ebfa0;
        }
        .van-dropdown-menu__title--active {
            color: #3ebfa0;
        }
        .van-dropdown-menu__title--active::after {
            color: #3ebfa0;
        }
        .van-sidebar-item--select::before {
            background: none;
            width: 0;
            height: 0;
        }
        .van-sidebar-item--select {
            .van-sidebar-item__text {
                color: #3ebfa0;
            }
        }
        .van-tree-select__item--active {
            color: #3ebfa0;
        }
    }
    .content {
        height: calc(100vh - 1.05rem);
        overflow: scroll;
    }
    // IOS下移除原生样式
    -webkit-appearance: none;
    // 自定义placeholder颜色和字号
    input::-webkit-input-placeholder {
        font-size: 0.13rem;
        font-family: PingFang SC;
        font-weight: 400;
        color: #999999;
    }
    // 不显示搜索标识，自行添加搜索放大镜
    input[type="search"] {
        -webkit-appearance: none;
    }
    [type="search"]::-webkit-search-decoration {
        display: none;
    }
    // 不显示清空按钮，自行添加input后面的x清空文本
    input::-webkit-search-cancel-button {
        display: none;
    }
}
</style>
