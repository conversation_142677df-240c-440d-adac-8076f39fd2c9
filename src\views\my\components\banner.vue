<!--
 * @Author: your name
 * @Date: 2024-11-20 17:31:07
 * @LastEditTime: 2024-11-21 09:54:22
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: banner
 * @FilePath: \h5-interhosp\src\views\my\components\banner.vue
-->
<template>
    <div class="banner_con">
        <!-- <div> -->
        <van-swipe
            :autoplay="3000"
            :loop="true"
            indicator-color="#fff"
            v-if="bannerlist && bannerlist.length > 0"
        >
            <van-swipe-item v-for="(itemin, index) in bannerlist" :key="index">
                <img
                    class="banner"
                    :src="itemin.iconUrl"
                    @click="jumpUrl(itemin)"
                    alt=""
                />
            </van-swipe-item>
        </van-swipe>
        <!-- </div> -->
    </div>
</template>

<script>
import { Swipe, SwipeItem } from "vant";

export default {
    components: {
        [Swipe.name]: Swipe,
        [SwipeItem.name]: SwipeItem,
    },
    props: {
        datalists: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            bannerlist: [],
        };
    },
    mounted() {
        console.log("我的banner", this.datalists.childStageList);
        let temp = JSON.parse(JSON.stringify(this.datalists));
        this.bannerlist = temp.childStageList;
    },
};
</script>

<style lang="less" scoped>
.banner_con {
    width: 100vw;
    text-align: center;
}
.banner {
    width: 345px;
    margin: 0 auto;
    margin-top: 10px;
}
</style>