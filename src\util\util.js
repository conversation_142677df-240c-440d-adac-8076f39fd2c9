/*
 * @Author: shenpp
 * @Date: 2023-05-22
 * @LastEditTime: 2025-04-08 18:38:16
 * @Description:
 */
import CryptoJS from "crypto-js";
import { Dialog, Toast } from "vant";
const util = {
    // 纳里加密 微信端
    /* encryptNali_wx(content) {
        let aesKey = "hosjk670qEH5lm3b";
        var key = CryptoJS.enc.Utf8.parse(aesKey);
        var scontent = CryptoJS.enc.Utf8.parse(content);
        var encrypted = CryptoJS.AES.encrypt(scontent, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        return encrypted.ciphertext.toString(); //返回的是16进制格式的密文
    }, */
    // 纳里解密 微信端
    /* dencryptNali_wx(content) {
        let aesKey = "hosjk670qEH5lm3b";
        var key = CryptoJS.enc.Utf8.parse(aesKey);
        var datahex = CryptoJS.enc.Hex.parse(content);
        var dataBase64 = CryptoJS.enc.Base64.stringify(datahex);
        var decrypted = CryptoJS.AES.decrypt(dataBase64, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        console.log(decrypted.toString(CryptoJS.enc.Utf8));
        return decrypted.toString(CryptoJS.enc.Utf8);
    }, */
    // 跳转纳里  加密 支付宝端
    encryptNali(content, aesKey) {
        if (typeof content == "object") {
            content = JSON.stringify(content);
        }
        // let aesKey = "hfdjk670qEH5lm3b";
        var key = CryptoJS.enc.Utf8.parse(aesKey);
        var scontent = CryptoJS.enc.Utf8.parse(content);
        var encrypted = CryptoJS.AES.encrypt(scontent, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        return encrypted.ciphertext.toString(); //返回的是16进制格式的密文
    },
    // 用户信息 解密
    dencrypt(content, aesKey) {
        // let aesKey = "hfdjk670qEH5lm3b";
        var key = CryptoJS.enc.Utf8.parse(aesKey);
        var datahex = CryptoJS.enc.Hex.parse(content);
        var dataBase64 = CryptoJS.enc.Base64.stringify(datahex);
        var decrypted = CryptoJS.AES.decrypt(dataBase64, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        return decrypted.toString(CryptoJS.enc.Utf8);
    },

    /* aesCode(str, key_) {
        if (str) {
            if (typeof str == "object") {
                str = JSON.stringify(str);
            }
            console.log("str", str);
            // 预约挂号秘钥
            // key_ = 'jfdjk670gEH5lm3a'
            let key = CryptoJS.enc.Utf8.parse(key_);
            let srcs = CryptoJS.enc.Utf8.parse(str);
            let encrypted = CryptoJS.AES.encrypt(srcs, key, {
                mode: CryptoJS.mode.ECB,
                padding: CryptoJS.pad.Pkcs7,
            });
            return encrypted.toString();
        } else {
            return "";
        }
    }, */
    // 获取传参
    getQueryStringHash(name) {
        let result = "";
        if (window.location.hash.includes("?")) {
            window.location.hash
                .split("?")[1]
                .split("&")
                .forEach((val) => {
                    if (val.includes(name)) {
                        result = val.substring(name.length + 1);
                    }
                });
        }
        // console.log('result', result)
        return result;
    },
    showToast(
        message = "网络连接超时，请稍后再试~",
        duration = 1500,
        forbidClick = true
    ) {
        Toast({
            duration,
            forbidClick,
            message,
        });
    },
    showLoading({
        message = "努力加载中，请稍后",
        forbidClick = true,
        loadingType = "spinner",
        duration = 0,
    } = {}) {
        Toast.loading({
            message,
            forbidClick,
            loadingType,
            duration,
        });
    },
    hideLoading() {
        Toast.clear();
    },
    openDialogAlert(title, content, fun, text, className) {
        return Dialog.alert({
            title,
            className,
            message: content,
            confirmButtonText: text || "确认",
            confirmButtonColor: "#00CCA6",
        })
            .then(() => {
                if (fun) {
                    Dialog.close();
                    fun();
                }
            })
            .catch(() => {
                // this.showToast('取消')
            });
    },
    generateUUID() {
        var d = new Date().getTime();
        if (window.performance && typeof window.performance.now === "function") {
            d += performance.now(); //use high-precision timer if available
        }
        var uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
            /[xy]/g,
            function (c) {
                var r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c == "x" ? r : (r & 0x3) | 0x8).toString(16);
            }
        );
        return uuid;
    },
    loginOut() {
        console.log("退出登录");

        // 退出登录
        window.localStorage.clear();
        window.sessionStorage.clear();
        if (
            navigator.userAgent.indexOf("AliApp") > -1 &&
            window.localStorage.getItem("interHosp_origin") != "szMini"
        ) {
            my.postMessage({
                action: "clearStorage",
            });
            my.navigateTo({
                url: "/pages/index/index",
            });
            // window.location.replace("#/home");
            return;
        }
        if (navigator.userAgent.toLowerCase().indexOf("toutiaomicroapp") > -1) {
            // 清除后，需跳转到小程序页面，小程序页面缓存也要清除
            // let setTime = setInterval(() => {
            //     console.log("跳转到首页");
            //     clearInterval(setTime);
            //     tt.miniProgram.navigateTo({
            //         url: "/pages/wxLogOut/wxLogOut",
            //     });
            // }, 500);
            return
        }

        // 清除后，需跳转到小程序页面，小程序页面缓存也要清除
        let setTime = setInterval(() => {
            console.log("跳转到首页");
            clearInterval(setTime);
            wx.miniProgram.navigateTo({
                url: "/pages/wxLogOut/wxLogOut",
            });
        }, 500);
    }
};

export default util;
