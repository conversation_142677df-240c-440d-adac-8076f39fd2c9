/*
 * @Author: Auto generated
 * @Date: 2025-04-12
 * @Description: Token管理工具
 */

const TOKEN_KEY = 'access_token'
const USER_ID_KEY = 'userId'

const auth = {
  /**
   * 获取Token
   * @returns {string} token
   */
  getToken() {
    return window.sessionStorage.getItem(TOKEN_KEY) || window.localStorage.getItem(TOKEN_KEY)
  },

  /**
   * 设置Token
   * @param {string} token - token值
   * @param {boolean} remember - 是否记住（存入localStorage）
   */
  setToken(token, remember = false) {
    if (remember) {
      window.localStorage.setItem(TOKEN_KEY, token)
    } else {
      window.sessionStorage.setItem(TOKEN_KEY, token)
    }
  },

  /**
   * 移除Token
   */
  removeToken() {
    window.sessionStorage.removeItem(TOKEN_KEY)
    window.localStorage.removeItem(TOKEN_KEY)
  },

  /**
   * 获取用户ID
   * @returns {string} userId
   */
  getUserId() {
    return window.localStorage.getItem(USER_ID_KEY)
  },

  /**
   * 设置用户ID
   * @param {string} userId - 用户ID
   */
  setUserId(userId) {
    window.localStorage.setItem(USER_ID_KEY, userId)
  },

  /**
   * 移除用户ID
   */
  removeUserId() {
    window.localStorage.removeItem(USER_ID_KEY)
  }
}

export default auth