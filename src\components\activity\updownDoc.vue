<!--
 * @Author: your name
 * @Date: 2025-05-27 17:25:36
 * @LastEditTime: 2025-06-10 10:23:57
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 上下结构的专家医生子展台--展台类别：专家医生上下滑动
 * @FilePath: \h5-interhosp\src\components\activity\updownDoc.vue
-->
<template>
  <div v-show="isShow">
    <div class="title">
      <div class="titleImg"></div>
      <div class="text">{{ childStageName }}</div>
      <div class="titleImg"></div>
    </div>
    <ul>
      <li
        v-for="(it, index) in commonlist"
        :key="index"
        @click="num = index"
        :class="{ active: index == num }"
      >
        {{ it.tabName }}
      </li>
    </ul>
    <div
      class="docItem_"
      v-for="(item1, i1) in commonlist[num].docList"
      :key="i1"
      @click="jumpUrl(item1)"
    >
      <img
        class="touxiang"
        v-lazy="
          item1.avatorUrl
            ? item1.avatorUrl
            : require('@/images/expertAvator.png')
        "
        :key="item1.avatorUrl"
      />
      <div class="docDetail">
        <div style="margin-bottom: 0.1rem">
          <span class="inline-b docName">{{ item1.docName }}</span>
          <span class="inline-b docLevel">{{ item1.levelName }}</span>
          <span class="inline-b dept">{{ item1.deptName }}</span>
        </div>
        <div
          style="margin-bottom: 0.1rem; display: flex; align-items: baseline"
        >
          <span class="inline-b shenglue hospLevel" v-show="item1.hospLevel">
            {{ item1.hospLevel }}</span
          >
          <span class="inline-b shenglue">{{ item1.orgName }}</span>
          
        </div>
        <div class="specialDescription" v-show="item1.specialDescription">
          擅长：{{ item1.specialDescription || "无" }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import tools from "@/util/tools";
export default {
  props: {
    datalists: {
      type: Object,
    },
    stageName: {
      type: String,
    },
    stageId: {
      type: String,
    },
  },
  data() {
    return {
      commonlist: "",
      elementId: "",
      isShow: false,
      childStageId: "",
      childStageName: "",
      num: "0",
    };
  },

  mounted() {
    let temp = JSON.parse(JSON.stringify(this.datalists));
    console.log("活动页专家医生上下滑动", temp);
    if (
      temp.stageTypeName === "专家医生上下滑动" &&
      temp.tabList.length !== 0
    ) {
      this.isShow = true;
      this.elementId = temp.elementId;
      this.commonlist = temp.tabList;
      console.log("当前tablist", this.commonlist);
      this.childStageId = temp.childStageId;
      this.childStageName = temp.childStageName;
    }
  },

  methods: {
    jumpUrl(item, id, name) {
      console.log("跳转参数", item);
      item.elementId = this.elementId;
      // 没有jumpurl，
      item.jumpUrl = item.scheduleUrl;
      item.status = "2";
      if (item.jumpUrl) {
        tools
          .handleSetPoint({
            stageId: this.stageId,
            childStageId: this.childStageId,
            stageAppId: item.applicationId,
            trackingContent: `${this.stageName}-${this.childStageName}-${item.docName}`,
            businessName: "activity",
          })
          .then(() => {
            tools.jumpUrlManage(item);
          });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  display: flex;
  align-items: center;
  width: 345px;
  height: 46px;
  justify-content: center;
  background-color: #fff;
  margin: 0 auto;
  border-radius: 8px;
  .titleImg {
    background-image: url("@/images/titleImg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 21px;
    height: 14px;
  }
  .text {
    margin-left: 10px;
    margin-right: 10px;
    color: 000;
    font-size: 18px;
    font-weight: bold;
  }
}
ul {
  display: flex;
  width: 345px;
  margin: 15px auto;
  li {
    background-color: #fff;
    border-radius: 15px;
    margin-right: 8px;
    padding: 8px 13px;
    font-size: 12px;
    color: #555555;
  }
}
.active {
  background-color: #01cda7;
  color: #fff;
}
.docItem_ {
  display: flex;
  padding: 13px 15px 13px 11px;
  margin-bottom: 10px;
  background-color: #fff;
  width: 315px;
  border-radius: 8px;
  margin: 0 auto;
  margin-bottom: 12px;
  .touxiang {
    width: 47px;
    height: 47px;
    border-radius: 50%;
    margin-right: 13px;
  }
  .docDetail {
    color: #686b73;
    font-size: 13px;
    width: 89%;
    .shenglue {
      // width: 48%;
      text-overflow: ellipsis;
      display: -webkit-inline-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1; /* 超出几行省略 */
      overflow: hidden;
    }
    .docName {
      color: #333333;
      font-size: 15px;
      margin-right: 10px;
      font-weight: bold;
    }
    .docLevel {
      color: #333333;
      font-size: 13px;
    }
    .hospLevel {
      border: 1px solid #3ebfa0;
      border-radius: 2px;
      color: #3ebfa0;
      font-size: 10px;
      padding: 3px;
      margin-right: 5px;
    }
    .dept {
      border-left: 1px solid #e8e9ec;
      padding-left: 5px;
      margin-left: 5px;
    }
    .specialDescription {
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 超出几行省略 */
      overflow: hidden;
    }
  }
}
</style>
