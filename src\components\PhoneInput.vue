<template>
  <div class="phone-input-wrapper">
    <div class="input-container">
      <span class="country-code">+86</span>
      <input
        v-model="localPhoneNumber"
        type="tel"
        class="phone-input"
        placeholder="请输入手机号码"
        @focus="onFocus"
        readonly
      />
      <button
        class="get-code-btn"
        :class="codeButtonClass"
        :disabled="!canGetCode"
        @click="handleGetCode"
      >
        {{ codeButtonText }}
      </button>
    </div>
  </div>
</template>

<script>
import common from '@/util/util';

export default {
  name: 'PhoneInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    countdown: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      localPhoneNumber: this.value,
      hasSentCode: false // 跟踪是否已经发送过验证码
    };
  },
  computed: {
    cleanPhoneNumber() {
      return this.localPhoneNumber.replace(/\s/g, '');
    },
    canGetCode() {
      return this.cleanPhoneNumber.length === 11 && this.countdown === 0;
    },
    codeButtonText() {
      if (this.countdown > 0) {
        return `${this.countdown}s重新发送`;
      }
      // 只有在已经发送过验证码且倒计时为0时才显示"重新发送"
      return this.cleanPhoneNumber.length === 11 && this.countdown === 0 && this.hasSentCode ? '重新发送' : '获取验证码';
    },
    
    // 获取验证码按钮的样式类
    codeButtonClass() {
      if (this.countdown > 0) {
        return 'countdown';
      }
      if (this.cleanPhoneNumber.length === 11 && this.countdown === 0 && this.hasSentCode) {
        return 'resend';
      }
      return 'default';
    }
  },
  watch: {
    value(newVal) {
      this.localPhoneNumber = newVal;
    },
    localPhoneNumber(newVal) {
      this.formatPhoneNumber(newVal);
    }
  },
  methods: {
    // 格式化手机号显示（3-3-4格式）
    formatPhoneNumber(value) {
      let formattedValue = value.replace(/\D/g, '');
      if (formattedValue.length > 11) {
        formattedValue = formattedValue.slice(0, 11);
      }
      
      if (formattedValue.length > 3 && formattedValue.length <= 7) {
        formattedValue = formattedValue.replace(/(\d{3})(\d+)/, '$1 $2');
      } else if (formattedValue.length > 7) {
        formattedValue = formattedValue.replace(/(\d{3})(\d{4})(\d+)/, '$1 $2 $3');
      }
      
      if (formattedValue !== this.localPhoneNumber) {
        this.localPhoneNumber = formattedValue;
        this.$emit('input', formattedValue);
      }
    },
    
    // 手机号正则校验
    validatePhone(phone) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(phone);
    },
    
    // 处理获取验证码
    handleGetCode() {
      if (!this.canGetCode) return;
      
      const phone = this.cleanPhoneNumber;
      
      // 手机号校验
      if (!this.validatePhone(phone)) {
        common.showToast('请输入正确的手机号码');
        return;
      }
      
      // 标记已经发送过验证码
      this.hasSentCode = true;
      
      this.$emit('get-code', phone);
    },
    
    // 输入框获得焦点
    onFocus() {
      this.$emit('focus');
    },
    
    // 清空手机号
    clearPhone() {
      this.localPhoneNumber = '';
      this.$emit('input', '');
    }
  }
};
</script>

<style scoped>
.phone-input-wrapper {
  margin-bottom: 16px;
}

.input-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 318px;
  height: 50.5px;
  background-color: #F4F4F4;
  border-radius: 27.5px;
  padding: 0 20px;
  box-sizing: border-box;
}

.country-code {
  color: #4C4C4C;
  font-size: 15px;
  margin-right: 10px;
  margin-left: 5px;
}

.phone-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #9B9B9B;
  background: transparent;
}

.phone-input::placeholder {
  color: #999;
}

.get-code-btn {
  background: transparent;
  border: none;
  font-size: 14px;
  font-family: 'PingFangSC', 'Helvetica Neue', Arial, sans-serif;
  padding: 0;
  cursor: pointer;
  text-align: left;
  opacity: 1.0;
  margin-right: -8px;
}

.get-code-btn.default {
  color: #9B9B9B;
  width: 70px;
}

.get-code-btn.active {
  color: #1083FF;
  width: 70px;
}

.get-code-btn.resend {
  color: #1083FF;
  width: 56px;
  text-align: center;
}

.get-code-btn.countdown {
  color: #9B9B9B;
  width: 80.5px;
  text-align: center;
}

.get-code-btn:disabled {
  opacity: 1;
  cursor: default;
}
</style>