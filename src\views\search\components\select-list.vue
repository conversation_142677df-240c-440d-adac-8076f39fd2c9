<template>
  <div class="select-list">
    <div v-for="item of list" :key="item.name"></div>
  </div>
</template>

<script>
import selectItem from "./select-item.vue";
export default {
  data() {
    return {
      list: [
        {
          name: "1",
          children: [
            {
              name: "1-1",
            },
            {
              name: "1-2",
              children: [
                {
                  name: "1-2-1",
                },
              ],
            },
          ],
        },
      ],
    };
  },
  components: {
    selectItem,
  },
};
</script>

<style></style>
