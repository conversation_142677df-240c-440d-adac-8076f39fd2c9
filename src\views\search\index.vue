<!--Author: 郭元扬
ProjectDescription: 搜索页面
CreateTime: 2023-06-08
UpdateTime: 2023-06-08-->

<template>
  <div class="search-center">
    <div v-show="showPage">
      <div class="search-input">
        <form action="javascript:;" style="height: 0.55rem">
          <div class="s-div">
            <input
              class="s-input"
              v-model="searchValue"
              type="search"
              placeholder="搜索医院、医生、病症"
              autocomplete="off"
              @keyup.13="onSearch"
              ref="sInput"
            />
            <img src="@/images/search/sousuo.png" alt="" />
            <img
              @click="onCancel"
              v-if="searchValue"
              src="@/images/search/s-del.png"
              alt=""
            />
            <span @click="goBack">取消</span>
          </div>
        </form>
      </div>
      <!-- 历史搜索 -->
      <div class="search-history">
        <div v-if="history.length">
          <div class="history">
            <p class="title">历史搜索</p>
            <img @click="delHistory" src="@/images/search/del.png" alt="" />
          </div>
          <div class="row3">
            <div class="tags">
              <p
                class="tag"
                v-for="(item, index) of history"
                @click="searchHistory(item)"
                :key="index"
              >
                {{ item | formData }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <!-- 热门搜索
        <div class="hot-search" v-if="hot.length">
          <div class="hot">
            <p class="title">热门搜索</p>
          </div>
          <div class="tags">
            <div @click="goHotSearch(item)" class="tag" v-for="(item, index) of hot" :key="index">
              {{ item.applicationName | formData }}
              <img v-if="item.angleIconUrl" :src="item.angleIconUrl" alt="" />
            </div>
          </div>
        </div> -->
      <!-- <div :title="item.title" :is="item.component" v-for="(item, index) in componentList" :key="index"></div> -->
      <!-- <hotSearch /> -->
    </div>
    <!-- <floatWindow /> -->
  </div>
</template>

<script>
import { Search, Dialog } from "vant";
import common from "@/util/common";
//   import hotSearch from '@/components/medical/hotSearch.vue'
// import floatWindow from "@/components/floatWindow.vue";
export default {
  components: {
    //   hotSearch,
    [Search.name]: Search,
    // floatWindow,
  },
  data() {
    return {
      searchValue: "",
      history: [],
      hot: [],
      menu: null,
      showPage: true,
      // componentList: []
    };
  },
  filters: {
    formData(val) {
      if (val.length > 12) {
        return `${val.substring(0, 12)}...`;
      } else {
        return val;
      }
    },
    filterComponent(value) {
      let method = "";
      switch (value) {
        case "100248":
          method = "hotSearch";
          break;
      }
      return method;
    },
  },
  created() {
    this.$nextTick(() => {
      this.$refs.sInput.focus();
    });
    //   this.$store.dispatch('rendNAv').then(() => {
    //     const menus = this.$store.state.medical.managelist.filter(val => val.stageName === '搜索')
    //     for (const item of menus[0].stageList) {
    //       this.componentList.push({
    //         component: this.$options.filters.filterComponent(item.childStageId)
    //       })
    //     }
    //   })
  },
  mounted() {
    if (window.localStorage.getItem("searchContent")) {
      this.history =
        window.localStorage.getItem("searchContent").split(",") || [];
    }
  },
  methods: {
    goBack() {
      console.log("his长度", history.length);
      console.log(this.$store.state.isWjAliMini);
      if (this.$store.state.isWjAliMini) {
        my.navigateBack();
      }
      window.history.go(-1);
    },
    onSearch() {
      if (this.searchValue) {
        if (this.history.indexOf(this.searchValue) !== -1) {
          this.history.splice(this.history.indexOf(this.searchValue), 1);
        }
        this.history.unshift(this.searchValue);
        this.showPage = false;
      }
      if (this.history.length > 12) {
        this.history.pop();
      }
      window.localStorage.setItem("searchContent", this.history);
      const cont = window.encodeURIComponent(this.searchValue);
      this.$router.push({
        path: "searchDoctorList",
        query: {
          searchValue: cont,
        },
      });
    },
    searchHistory(val) {
      const cont = window.encodeURIComponent(val);
      this.$router.push({
        path: "searchDoctorList",
        query: {
          searchValue: cont,
        },
      });
      //   window.location.href =
      //     "https://www.hfi-health.com:28181/appointWithDoc/#/search?cont=" + cont;
    },
    onCancel() {
      this.searchValue = "";
    },
    delHistory() {
      Dialog.confirm({
        message: "是否确认清除历史搜索？",
        confirmButtonColor: "#00CCA6",
        cancelButtonColor: "#999999",
      })
        .then(() => {
          window.localStorage.removeItem("searchContent");
          this.history = [];
        })
        .catch(() => {
          // on cancel
        });
    },
    goHotSearch(val) {
      // common.jumpurl(val)
    },
  },
};
</script>

<style lang="less" scoped>
.search-center {
  .search-history {
    padding-left: 0.15rem;
  }
  .history {
    display: flex;
    align-items: center;
    padding: 0.31rem 0.25rem 0.2rem 0rem;
    justify-content: space-between;
    & > img {
      width: 0.16rem;
      height: 0.18rem;
    }
  }
  .title {
    font-size: 0.16rem;
    color: #333;
    font-weight: 500;
  }
  .row3 {
    max-height: 1.25rem;
    width: 100%;
    overflow: hidden;
  }
  .tags {
    display: flex;
    flex-wrap: wrap;
    .tag {
      font-size: 0.14rem;
      color: #333;
      padding: 0.06rem 0.17rem;
      background: #ffffff;
      border: 1px solid #e4e4e4;
      border-radius: 0.16rem;
      margin-right: 0.1rem;
      margin-bottom: 0.1rem;
      display: flex;
      align-items: center;
      & > img {
        width: 0.12rem;
        height: 0.14rem;
        margin-left: 0.06rem;
      }
    }
  }
  .search-input {
    width: 100%;
    height: 0.55rem;
    background: #ffffff;
    position: relative;
    .s-div {
      & > img {
        position: absolute;
        &:nth-of-type(1) {
          width: 0.13rem;
          height: 0.14rem;
          top: 0.2rem;
          left: 0.32rem;
        }
        &:nth-of-type(2) {
          width: 0.15rem;
          height: 0.15rem;
          top: 0.2rem;
          right: 0.9rem;
        }
      }
      span {
        font-size: 0.15rem;
        font-family: PingFang SC;
        font-weight: 400;
        color: #7a7a7a;
        position: absolute;
        right: 0.26rem;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .s-input {
      width: 2.75rem;
      box-sizing: border-box;
      position: absolute;
      height: 0.33rem;
      border-radius: 0.14rem;
      background-color: #f5f5f5;
      font-size: 0.12rem;
      color: #363636;
      padding-left: 0.34rem;
      padding-right: 0.34rem;
      margin-right: 0.2rem;
      margin-left: 0.2rem;
      top: 0.11rem;
      border: none;
    }
  }

  // IOS下移除原生样式
  -webkit-appearance: none;
  // 自定义placeholder颜色和字号
  input::-webkit-input-placeholder {
    font-size: 0.13rem;
    font-family: PingFang SC;
    font-weight: 400;
    color: #999999;
  }
  // 不显示搜索标识，自行添加搜索放大镜
  input[type="search"] {
    -webkit-appearance: none;
  }
  [type="search"]::-webkit-search-decoration {
    display: none;
  }
  // 不显示清空按钮，自行添加input后面的x清空文本
  input::-webkit-search-cancel-button {
    display: none;
  }
}
</style>
