<template>
  <van-popup
    v-model="furTipShow"
    round
    position="bottom"
    close-on-popstate
    :close-on-click-overlay="false"
    z-index="10000"
  >
    <div class="fur-tip-box">
      <div class="fur-tip-content">
        <div class="fur-tip-title">{{ title }}</div>
        <div class="fur-tip-text ql-editor" v-html="moduleData"></div>

        <div class="fur-tip-foot">
          <div class="cancel-tip-btn" @click="closeTip">不同意并退出</div>
          <div class="confirm-tip-btn" @click="confirmHandle">同意并继续</div>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
export default {
  props: {
    furTipShow: {
      type: Boolean,
      default: false,
    },
    moduleData: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "患者须知",
    },
  },
  data() {
    return {};
  },
  methods: {
    confirmHandle() {
      this.$emit("confirm-tip");
    },
    closeTip() {
      this.$emit("cancel-tip");
    },
  },
};
</script>

<style scoped lang="less">
.fur-tip-box {
  width: 100%;
  max-height: 60vh;
  background: url("../images/fur_tip_bg.jpg") no-repeat 0 0;
  background-size: 100% 329px;
  // min-height: 1034px;
  .fur-tip-content {
    width: 100%;
    padding: 42px 12px 41px;
    box-sizing: border-box;
    .fur-tip-title {
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      line-height: 25px;
      text-align: center;
      margin-bottom: 30px;
    }
    .fur-tip-text {
      width: 100%;
      font-size: 13px;
      word-wrap: break-word;
    }
  }
  .fur-tip-foot {
    width: 100%;
    margin-top: 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding: 0 24px;
    // box-sizing: border-box;
    .cancel-tip-btn {
      width: 169px;
      height: 49px;
      background: #ffffff;
      border-radius: 24px;
      border: 1px solid #e5e5e5;
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      text-align: center;
      line-height: 49px;
    }
    .confirm-tip-btn {
      width: 169px;
      height: 49px;
      background: #3ebfa0;
      border-radius: 24px;
      font-size: 18px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      text-align: center;
      line-height: 49px;
    }
  }
}
</style>
