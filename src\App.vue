<template>
    <div id="app" :class="[uiStyle]">
        <keep-alive
            ><router-view v-if="$route.meta.keepAlive"></router-view
        ></keep-alive>
        <router-view
            v-if="!$route.meta.keepAlive"
            :key="$route.fullPath"
        ></router-view>
    </div>
</template>

<script>
import common from "@/util/common";
import { md_ } from "@/api/api";
export default {
    name: "App",
    data() {
        return {
            // 模式
            uiStyle: "",
            orgId: "",
        };
    },
    created() {
        // this.orgId = common.getUrlParam("orgId");
        // window.sessionStorage.setItem("orgId", this.orgId);
        // this.$store.dispatch('getOrgId',this.orgId)
        document.documentElement.style.setProperty(
            "--primary-color",
            this.$store.state.primaryColor
        );
        this.md();
    },
    methods: {
        // 调用自己的接口进行埋点
        md() {
            // hzAppMS/jumpStatistics/applySmk
            // 链接上有 purpose from
            let channel = common.getUrlParam("from");
            let purpose = common.getUrlParam("purpose");
            if (purpose && channel) {
                md_(channel, purpose);
            }
        },
    },
};
</script>

<style>
#app {
    height: 100%;
    background: #f5f5f5;
    overflow-y: auto;
}
</style>
