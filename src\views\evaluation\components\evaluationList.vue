<!--
 * @Author: shenpp
 * @Date: 2023-06-14
 * @LastEditTime: 2023-09-22
 * @Description: 患者评价列表
-->
<template>
  <div class="list_con">
    <div v-for="(item, i) in evaluation_list" :key="i">
      <div class="top_">
        <img src="@/images/evaluationUser.png" />
        <span class="name">{{ item.patientName }}</span>
        <!-- type为 1 显示在线咨询，为 2 显示在线复诊 -->
        <div class="type">
          {{
            item.businessType == "1"
              ? "在线咨询"
              : item.businessType == "2"
              ? "在线复诊"
              : ""
          }}
        </div>
      </div>
      <!-- v-model="item.scoring" -->
      <van-rate
        v-model="item.score / 2"
        :size="10"
        color="#FE963A"
        void-icon="star"
        void-color="#E0E0E0"
        readonly
        allow-half
        class="rate_"
      />
      <div class="desc">{{ item.content }}</div>
    </div>
  </div>
</template>

<script>
import { Rate } from "vant";
export default {
  components: {
    [Rate.name]: Rate,
  },
  props: {
    evaluation_list: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      list: [
        {
          name: "**波",
          type: "电话咨询",
          level: "10",
          description: "服务态度非常好，讲解到位，非常满意。",
        },
        {
          name: "**波",
          type: "电话咨询",
          level: "10",
          description:
            "医生态度是真的好，很耐心，去医院也是很难启齿 这样避免了尴尬还得到了很好的解决。",
        },
      ],
      rateValue: 3,
    };
  },

  mounted() {
    console.log("列表", this.$props);
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.list_con {
  margin-bottom: 0.1rem;
}
.list_con > div {
  border-bottom: 0.01rem solid #e8e9ec;
}
.list_con > div:last-child {
  border-bottom: none;
}
.top_ {
  display: flex;
  margin-top: 0.19rem;
  img {
    display: inline-block;
    width: 0.165rem;
    height: 0.18rem;
  }
  .name {
    color: #333;
    font-size: 0.12rem;
    flex: 1;
    margin-left: 0.07rem;
  }
  .type {
    color: #999999;
    font-size: 0.12rem;
  }
}
.rate_ {
  margin-top: 0.1rem;
}
.desc {
  margin-top: 0.16rem;
  color: #333333;
  font-size: 0.14rem;
  margin-bottom: 0.14rem;
  width: 3.07rem;
  word-wrap: break-word;
}
</style>
