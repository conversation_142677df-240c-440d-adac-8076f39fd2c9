const optionArea = [
    {
        text: "杭州市",
        value: ""
    },
    {
        text: "西湖区",
        value: "西湖区"
    },
    {
        text: "滨江区",
        value: "滨江区"
    },
    {
        text: "上城区",
        value: "上城区"
    },
    {
        text: "拱墅区",
        value: "拱墅区"
    },
    {
        text: "余杭区",
        value: "余杭区"
    },
    {
        text: "临平区",
        value: "临平区"
    },
    {
        text: "临安区",
        value: "临安区"
    },
    {
        text: "富阳区",
        value: "富阳区"
    },
    {
        text: "萧山区",
        value: "萧山区"
    },
    {
        text: "钱塘区",
        value: "钱塘区"
    },
    {
        text: "桐庐县",
        value: "桐庐县"
    },
    {
        text: "淳安县",
        value: "淳安县"
    },
    {
        text: "建德市",
        value: "建德市"
    },

]
const optionHospitalLevel = [{
    text: "全部",
    value: ""
},
{
    text: "三级",
    value: "三级"
},
{
    text: "二级",
    value: "二级"
},
{
    text: "一级",
    value: "一级"
},
{
    text: "未定级",
    value: "未定级"
},

    // {
    //     text: "二级乙等",
    //     value: "二级乙等"
    // },
    /*  {
         text: "其他",
         value: 5
     }, */
]
const optionHospitalType = [{
    text: "全部",
    value: ""
},
{
    text: "市属公立",
    value: "3"
},
{
    text: "区县",
    value: "6"
},
{
    text: "社区",
    value: "5"
},
{
    text: "离我最近",
    value: "00"
},

]
// "门诊病历",
// "检验报告",
// "检查报告",
// "体检报告",
// "处方",
// "治疗记录",
// "住院病历",
// "医嘱",
// "医学影像",
// "病患部位",
// "出院小结",
// "其他",
const optionConsultPicType = [{
    text: "门诊病历",
    value: "21"
},
{
    text: "检验报告",
    value: "22"
},
{
    text: "检查报告",
    value: "23"
}, {
    text: "体检报告",
    value: "24"
}, {
    text: "处方",
    value: "25"
}, {
    text: "治疗记录",
    value: "26"
}, {
    text: "住院病历",
    value: "27"
}, {
    text: "医嘱",
    value: "28"
}, {
    text: "医学影像",
    value: "29"
}, {
    text: "病患部位",
    value: "210"
}, {
    text: "出院小结",
    value: "211"
}, {
    text: "其他",
    value: "212"
},

]
export {
    optionArea,
    optionHospitalLevel,
    optionHospitalType,
    optionConsultPicType
}