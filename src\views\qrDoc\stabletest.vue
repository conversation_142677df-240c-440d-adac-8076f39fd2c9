<!-- 打开首页 -->
<template>
    <div class="bg">
        <div
            style="
                position: absolute;
                width: 100%;
                bottom: 0.2rem;
                min-height: 2.9rem;
            "
        >
            <div class="mb"></div>
            <!-- <div class="btn"></div> -->
            <!-- v-show="inWechat && btnShow" -->
            <div class="btn" v-show="inWechat && btnShow">
                <!-- <wx-open-launch-weapp
                    class="btn_"
                    id="launch-btn"
                    appid="wx7f3513f8c7d02674"
                    username="gh_d1d29732fd17"
                >
                    <script type="text/wxtag-template">
                        <style>
                            .btn{
                                width: 100%;
                                height: 107px;
                                border:none;
                                background: transparent;
                            }

                        </style>
                        <button class="btn"></button>
                    </script>
                </wx-open-launch-weapp> -->
            </div>
            <div v-if="inAli" class="btn" @click="goMini"></div>
        </div>

        <div class="btn_" v-show="inWechat && btnShow">
            <wx-open-launch-weapp
                class="btn_"
                id="launch-btn"
                appid="wx7f3513f8c7d02674"
                username="gh_d1d29732fd17"
                :env-version="env"
            >
                <script type="text/wxtag-template">
                    <style>
                        .btn{
                            width: 100%;
                            height: 2000px;
                            border:none;
                            background: transparent;
                        }
                    </style>
                    <button class="btn"></button>
                </script>
            </wx-open-launch-weapp>
        </div>
    </div>
</template>

<script>
import common from "@/util/common.js";
import util from "@/util/util.js";
import { wxInit } from "@/api/api";
export default {
    components: {},
    data() {
        return {
            inWechat: false,
            inAli: false,
            btnShow: false,
            url: "",
            env: process.env.VUE_APP_WECHART_VERSION || "release",
        };
    },

    mounted() {
        if (
            navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 ||
            navigator.userAgent.indexOf("AliApp") > -1
        ) {
            // 微信、支付宝中
            this.getUrl();
        } else {
            // 暂时不做
            util.openDialogAlert("", "请使用微信扫码");
        }
    },

    methods: {
        initRrlData() {
            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                this.url =
                    "/pages/wxIndex/wxIndex?authStatus=2&returnURL=https%3A%2F%2Fwww.hfi-health.com%3A28181%2FiHospStaRel%2F%23%2Fhome%3Forigin%3DwjwxMini";
                // 微信中
                this.inWechat = true;
                this.initWechat();
            } else if (navigator.userAgent.indexOf("AliApp") > -1) {
                this.url =
                    "alipays://platformapi/startapp?appId=2021002138635948&page=pages/index/index?authStatus%3D2%26returnURL%3Dhttps%253A%252F%252Fwww.hfi-health.com%253A28181%252FiHospStaRel%252F%2523%252Fhome";
                this.inAli = true;
                // 支付宝  直接跳转
                window.location.href = this.url;
                // "alipays://platformapi/startapp?appId=2021003100614413&page=pages/webView/jinTou?type%3DuserLogin%26src%3Dhttps%253A%252F%252Fwww.hfi-health.com%253A28181%252FinterHosp%252F%2523%252FalipayIndex%253Forg%253Dsz%2526doctorId%253D189474%2526deptId%253D21466&authStatus=0";
            } else {
                // 暂时不做
                util.openDialogAlert("", "请使用微信扫码");
            }
        },
        getUrl() {
            this.initRrlData();
        },
        goMini() {
            window.location.href = this.url;
            // "alipays://platformapi/startapp?appId=2021003100614413&page=pages/webView/jinTou?type%3DuserLogin%26src%3Dhttps%253A%252F%252Fwww.hfi-health.com%253A28181%252FinterHosp%252F%2523%252FalipayIndex%253Forg%253Dsz%2526doctorId%253D189474%2526deptId%253D21466&authStatus=0";
        },
        async initWechat() {
            await wxInit();
            let that = this;
            wx.ready(function () {
                console.log("ready");
                wx.checkJsApi({
                    jsApiList: ["wx-open-launch-app"], // 需要检测的JS接口列表，所有JS接口列表见附录2,
                    success: (res) => {
                        console.log("可用");
                        var btn = document.querySelector("#launch-btn");
                        btn.setAttribute(
                            "path",
                            that.url
                            // "/pages/wxIndex/wxIndex?authStatus=1&returnURL=https%3A%2F%2Fzjshlwyy.zjjgpt.com%2Fweixin%2Fthird%2FjinTouApp2%2Fauth%3Fmodule%3DsingleDoctIndex%26did%3D182680%26organId%3D2000006%26source%3DjinTou-zfb-sy%26uInfo%3D"
                        );
                        btn.addEventListener("launch", function (e) {
                            console.log("success");
                            // openTip(1000);
                        });
                        btn.addEventListener("error", function (e) {
                            console.log("fail", e.detail);
                            // openTip(100);
                        });
                        that.btnShow = true;
                    },
                    fail: (err) => {
                        console.log(err, "不可用");
                        // openTip(100);
                        // document.querySelector("#btn-other").style.display = "";
                    },
                });
            });
            wx.error(function (err) {
                console.log(err);
            });
        },
    },
};
</script>

<style lang="less" scoped>
.bg {
    background: url("./../../images/qrDocBg.png") no-repeat top center;
    background-size: cover;
    width: 100%;
    height: 100%;
    min-height: 6.67rem;
    float: left;
    position: relative;
}
.mb {
    background: url("./../../images/aksu/wa.png") no-repeat top center;
    background-size: 100%;
    width: 3.38rem;
    height: 1.9rem;
    // position: fixed;
    // top: 44%;
    // left: 50%;
    // margin-left: -1.69rem;
    margin: 0 auto;
}
.btn {
    background: url("./../../images/aksu/btn.png") no-repeat top center;
    background-size: 100%;
    width: 3.58rem;
    height: 1.07rem;
    margin: 0.2rem auto 0;
}
.btn_ {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 111;
}
</style>
