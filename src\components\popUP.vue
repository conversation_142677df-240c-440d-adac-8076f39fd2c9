<template>
    <!-- 1 弹窗只弹一次/多次 2 弹窗的位置 -->
    <div class="main" v-show="isshow">
        <div class="sec">
            <div class="img" @click="btnlcik">
                <img :src="dialogInfo.iconUrl" alt="" />
            </div>
            <div class="dele" @click="btnHide">
                <img src="@/images/img/close.png" alt="" />
            </div>
        </div>
    </div>
</template>
<script>
import tools from "@/util/tools.js";
import { userAuthorize, getToken } from "@/api/api";

export default {
    props: {
        dialogInfo: {
            type: Object,
            default: () => {},
        },
        position: {
            type: String,
            default: () => "",
        },
    },
    data() {
        return {
            isshow: true,
            gettime: "",
        };
    },

    created() {
        if (!this.$cookies.get(`ad${this.dialogInfo.bannerId}`)) {
            // 没有cookie且一天一次的频率直接存进去
            this.setcookie(this.dialogInfo.bannerId, this.dialogInfo.frequency);
            this.isshow = true;
            this.handlePoint();
        } else if (this.dialogInfo.frequency === 2) {
            this.handlePoint();
            this.isshow = true;
            if (this.$cookies.get(`ad${this.dialogInfo.bannerId}`)) {
                this.$cookies.remove(`ad${this.dialogInfo.bannerId}`);
            }
        } else {
            this.isshow = false;
        }
    },
    methods: {
        // 埋点
        handlePoint() {
            tools.handleSetPoint({
                trackingContent: `${this.position}|弹窗`,
            });
            console.log(this.dialogInfo, "this.dialogInfo");
        },
        async btnlcik() {
            let name = this.dialogInfo.applicationName;
            if (
                name.indexOf("市三服务包") > -1 &&
                (this.dialogInfo.unicode == "123301004701166305" ||
                    this.dialogInfo.unicode == "2000140")
            ) {
                let arr = name.split("$");
                let clientId =
                    arr && arr.length >= 2 ? arr[1] : "DFE1FCD95AD04478"; // "DFE1FCD95AD04478" 市三服务包clientId
                this.dialogInfo.clientId = clientId;
                this.dialogInfo.status = "2";
                tools.jumpUrlManage(this.dialogInfo);
                return;
                let userId = window.localStorage.getItem("userId") || "";
                if (!userId) {
                    if (!window.localStorage.getItem("userInfo")) {
                        await this.$store.dispatch("getLogin");
                    }
                    let user = JSON.parse(
                        window.localStorage.getItem("userInfo")
                    );
                    await getToken(user).then((res) => {
                        userId = res.userId;
                    });
                    console.log("----====----get login aaaaa");
                }
                let openId = window.localStorage.getItem("alipayUserId");
                if (
                    navigator.userAgent
                        .toLowerCase()
                        .indexOf("micromessenger") > -1
                ) {
                    openId = window.localStorage.getItem("wxOpenid");
                }
                userAuthorize(userId, clientId).then((res) => {
                    console.log("auth授权", res);
                    if (res) {
                        let jumpUrl = this.dialogInfo.jumpUrl;
                        let timeStamp = new Date().getTime();
                        let url =
                            jumpUrl.indexOf("?") > -1
                                ? `${jumpUrl}&openId=${openId}&userCode=${res.code}&time=${timeStamp}`
                                : `${jumpUrl}?openId=${openId}&userCode=${res.code}&time=${timeStamp}`;
                        window.location.href = url;

                        console.log(
                            "code链接市三服务包 popup popup url ======",
                            url
                        );
                    }
                });
            } else {
                tools.jumpUrlManage(this.dialogInfo);
            }
        },
        btnHide() {
            this.isshow = false;
        },
        setcookie(id, frequency) {
            var myDate = new Date();
            var year = myDate.getFullYear(); //获取当前年
            var mon = myDate.getMonth() + 1; //获取当前月
            var date = myDate.getDate(); //获取当前日

            // let day = new Date(`${year}-${mon}-${date}`).getTime() + 1000 * 3600 * 24
            // if (frequency === 1) {
            //   d.setTime(day)
            // } else {
            //   d.setTime(d.getTime() + 1)
            // }
            // document.cookie = `ad${id}=${name};expires=` + d.toGMTString()
            // let result = document.cookie
            // return result
            let day;
            if (frequency === 1) {
                day = new Date(
                    new Date(`${year}-${mon}-${date}`).getTime() +
                        1000 * 3600 * 24
                );
            } else {
                day = 1;
            }
            this.$cookies.set(`ad${id}`, id, day);
        },
    },
};
</script>
<style lang="less" scoped>
.main {
    width: 100%;
    height: 100vh !important;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.5);

    .sec {
        width: 80%;
        height: 5rem;
        margin: 0.9rem auto;

        img {
            width: 100%;
            height: 4rem;
        }

        .dele {
            width: 0.2rem;
            height: 0.2rem;
            margin: 30px auto;

            img {
                width: 100%;
                height: 100%;
            }
        }
    }
}
</style>
