<!--
 * @Author: your name
 * @Date: 2025-05-27 17:28:23
 * @LastEditTime: 2025-06-06 09:46:44
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description:新瀑布流样式(标题居中)-展台类别：标题居中瀑布流
 * @FilePath: \h5-interhosp\src\components\activity\newwall.vue
-->
<template>
    <div v-show="isShow">
        <div class="title1">
            <div class="titleImg"></div>
            <div class="text">{{ childStageName }}</div>
            <div class="titleImg"></div>
        </div>
        <div class="waterfall" v-show="isShow">
            <div class="leftItem">
                <div
                    class="itemCon"
                    v-for="(item, index) in leftList"
                    :key="index"
                    @click="go(item, index)"
                    :data-exposure-id="elementId"
                    :data-exposure-content="item.elementContent"
                    :data-exposure-sn="index + 1"
                >
                    <!-- 应用图标 -->
                    <img
                        class="iconUrl"
                        v-show="item.iconUrl"
                        v-lazy="item.iconUrl"
                        :key="item.iconUrl"
                        alt=""
                    />
                    <!-- 角标 -->
                    <img
                        class="angleIconUrl"
                        v-show="item.angleIconUrl"
                        v-lazy="item.angleIconUrl"
                        :key="item.angleIconUrl"
                        alt=""
                    />
                    <div class="title m07l07">
                        <!-- &符隔开 -->
                        <!-- 名称 -->
                        <div class="name">
                            {{ item.applicationName.split("&")[0] }}
                        </div>
                        <!-- 职称；例主任医生 -->
                        <div
                            class="level"
                            v-show="item.applicationName.split('&')[1]"
                        >
                            {{ item.applicationName.split("&")[1] }}
                        </div>
                    </div>
                    <!-- 所属医院 -->
                    <div class="remark m07l07" v-show="item.remark">
                        {{ item.remark }}
                    </div>
                    <div
                        v-show="item.applicationSubName || item.functionId"
                        class="bottom m07l07"
                    >
                        <!-- 价格 -->
                        <div class="subName" v-show="item.applicationSubName">
                            {{ item.applicationSubName }}
                        </div>
                        <!-- 销量 -->
                        <div class="function" v-show="item.functionId">
                            {{ item.functionId }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="rightItem">
                <div
                    class="itemCon"
                    v-for="(item, index) in rightList"
                    :key="index"
                    @click="jumpUrl(item, index)"
                    :data-exposure-id="elementId"
                    :data-exposure-content="item.elementContent"
                    :data-exposure-sn="index + 1"
                >
                    <!-- 应用图标 -->
                    <img
                        class="iconUrl"
                        v-show="item.iconUrl"
                        v-lazy="item.iconUrl"
                        :key="item.iconUrl"
                        alt=""
                    />
                    <!-- 角标 -->
                    <img
                        class="angleIconUrl"
                        v-show="item.angleIconUrl"
                        v-lazy="item.angleIconUrl"
                        :key="item.angleIconUrl"
                        alt=""
                    />
                    <div class="title m07l07">
                        <!-- &符隔开 -->
                        <!-- 名称 -->
                        <div class="name">
                            {{ item.applicationName.split("&")[0] }}
                        </div>
                        <!-- 职称；例主任医生 -->
                        <div
                            class="level"
                            v-show="item.applicationName.split('&')[1]"
                        >
                            {{ item.applicationName.split("&")[1] }}
                        </div>
                    </div>
                    <!-- 所属医院 -->
                    <div class="remark m07l07" v-show="item.remark">
                        {{ item.remark }}
                    </div>
                    <div
                        v-show="item.applicationSubName || item.functionId"
                        class="bottom m07l07"
                    >
                        <!-- 价格 -->
                        <div class="subName" v-show="item.applicationSubName">
                            {{ item.applicationSubName }}
                        </div>
                        <!-- 销量 -->
                        <div class="function" v-show="item.functionId">
                            {{ item.functionId }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
  <script>
import tools from "@/util/tools.js";
export default {
    props: {
        datalists: {
            type: Object,
        },
    },
    data() {
        return {
            commonlist: "",
            elementId: "",
            isShow: false,
            //   奇数位数组 左侧
            leftList: [],
            //   偶数位数组，右侧
            rightList: [],
            childStageId: "",
            childStageName: "",
        };
    },
    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("瀑布流", temp);
        if (temp.childStageList.length !== 0) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.commonlist = temp.childStageList;
            this.childStageId = temp.childStageId;
            this.childStageName = temp.childStageName;
            temp.childStageList.forEach((element, i) => {
                if (i % 2 !== 0) {
                    // 奇数位，奇数位必须先判断，否则顺序会变化
                    this.rightList.push(element);
                } else {
                    // 偶数位
                    this.leftList.push(element);
                }
            });
        }
    },
    methods: {
        go(item, index) {
            item.elementId = this.elementId;
            item.index = index;
            tools.jumpUrlManage(item);
        },
        jumpUrl(item, id, name) {
            console.log("跳转参数", item);
            if (item.jumpUrl) {
                tools
                    .handleSetPoint({
                        stageId: this.stageId,
                        childStageId: this.childStageId,
                        stageAppId: item.applicationId,
                        trackingContent: `${this.stageName}-${this.childStageName}-${item.elementContent}`,
                        businessName: "activity",
                    })
                    .then(() => {
                        tools.jumpUrlManage(item);
                    });
            }
        },
    },
};
</script>
  <style lang="less" scoped>
.title1 {
    display: flex;
    align-items: center;
    width: 345px;
    height: 46px;
    justify-content: center;
    background-color: #fff;
    margin: 0 auto;
    border-radius: 8px;
    margin-bottom: 10px;
    .titleImg {
        background-image: url("@/images/titleImg.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 21px;
        height: 14px;
    }
    .text {
        margin-left: 10px;
        margin-right: 10px;
        color: 000;
        font-size: 18px;
        font-weight: bold;
    }
}
img[lazy="loading"] {
    display: block;
    width: 20%;
    line-height: 100%;
    margin: auto;
}
.waterfall {
    display: flex;
    padding: 0.15rem;
    padding-top: 0;
}
.leftItem {
    margin-right: 0.15rem;
}
.itemCon {
    background-color: #fff;
    margin-bottom: 0.1rem;
    width: 1.65rem;
    border-radius: 0.08rem;
    padding-bottom: 0.15rem;
    position: relative;
    .iconUrl {
        width: 100%;
        border-top-left-radius: 0.08rem;
        border-top-right-radius: 0.08rem;
        display: inline-block;
    }
    .angleIconUrl {
        height: 0.19rem;
        position: absolute;
        z-index: 9999;
        left: 0;
    }

    .title {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 0.13rem;
        .name {
            font-size: 0.14rem;
            color: #333333;
            font-weight: bold;
            margin-right: 0.1rem;
            overflow: hidden;
            max-width: 100%;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .level {
            color: #a77d2a;
            font-size: 0.1rem;
            background-color: #fdf3e0;
            border-radius: 0.04rem;
            display: inline-block;
            padding: 0.04rem;
        }
    }
    .remark {
        font-size: 0.13rem;
        color: #999999;
        max-width: 100%;
        word-break: break-all;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .bottom {
        display: flex;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 0.1rem;
        .subName {
            color: #fc5619;
            font-size: 0.17rem;
        }
        .function {
            color: #999999;
            font-size: 0.11rem;
        }
    }
}
.m07l07 {
    margin-left: 0.07rem;
    margin-right: 0.07rem;
}
</style>  