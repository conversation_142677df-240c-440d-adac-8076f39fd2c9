<!--
 * @Author: your name
 * @Date: 2025-04-02 09:40:22
 * @LastEditTime: 2025-05-15 15:49:23
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 新版机构主页的服务配置
 * @FilePath: \h5-interhosp\src\views\home\components\newHospService.vue
-->
 <template>
    <div class="block" v-if="newServiceList.applicationList.length != 0">
        <p class="title m15">{{ newServiceList.serviceName }}</p>
        <div class="zhuanqu flex ml15">
            <img
                class="img"
                :src="item.iconUrl"
                v-for="(item, i) in visionType1"
                @click="ServesJumpUrl(item)"
                :key="i"
                alt=""
            />
        </div>
        <div class="gongneng flex">
            <div
                v-for="(item2, i) in visionType2"
                :key="i"
                @click="ServesJumpUrl(item2)"
                class="mt15 con"
            >
                <img :src="item2.iconUrl" class="img" />
                <div class="item2Text">{{ item2.applicationName }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import tools from "@/util/tools.js";

export default {
    name: "H5InterhospNewHospService",
    props: {
        newServiceList: {
            type: Object,
            default: () => {},
        },
        hospName: {
            type: String,
            default: "",
        },
        unicode: {
            type: String,
            default: "",
        },
    },
    data() {
        return {
            visionType1: [],
            visionType2: [],
        };
    },

    mounted() {
        this.$props.newServiceList.applicationList.forEach((element) => {
            if (element.visionType == 1) {
                // 专区入口
                this.visionType1.push(element);
            } else if (element.visionType == 2) {
                this.visionType2.push(element);
            }
        });
    },

    methods: {
        // 服务内容接入后管配置 跳转
        async ServesJumpUrl(item) {
            // 服务包 有授权商户号 备注：白皮书授权
            console.log("服务包后管配置跳转", item);
            debugger;
            item.status = item.certificateLevel;
            if (item.clientId && item.remark == "白皮书授权") {
                // 跳转服务包
                tools.jumpUrlManage(item);
                return;
            }
            // 在线建档 目前handleCreateRecord内仅判断了市中和市一
            if (item.remark == "在线建档") {
                this.$emit("handleCreateRecord");
                // this.handleCreateRecord();
                return;
            }
            if (item.remark == "自费建档") {
                this.$emit("handleCreateRecordZF");
                // this.handleCreateRecordZF();
                return;
            }
            if (
                item.appType &&
                item.appType == "2" &&
                navigator.userAgent.indexOf("AliApp") > -1
            ) {
                //  打开openURI，支付宝上，apptype为2的时候配置的是H5APP
                my.postMessage({
                    action: "thirdPage",
                    returnURL: item.jumpUrl,
                });
                return;
            }
            await tools.handleSetPoint({
                trackingContent: item.applicationName,
                orgId: this.$props.unicode,
                orgName: this.$props.hospName,
            });
            tools.jumpUrlManage(item);
        },
    },
};
</script>

<style lang="less" scoped>
.block {
    margin: 12px 15px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
}
.flex {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
}
.ml15 {
    margin-left: 15px;
}
.m15 {
    margin: 15px;
}
.mr27 {
    margin-right: 27px;
}
.mt15 {
    margin-top: 12px;
}
.title {
    font-size: 18px;
    color: #000;
    font-weight: bold;
    margin-bottom: 10px;
}
.zhuanqu {
    .img {
        width: 152px;
        height: 75px;
        margin-top: 5px;
    }
    .img:nth-child(2n + 1) {
        margin-right: 11px;
    }
}
.gongneng {
    margin-left: 5px;
    margin-bottom: 15px;
    .img {
        width: 45px;
        height: 45px;
    }
    .con {
        // margin-top: 15px;
        text-align: center;
        font-size: 14px;
        color: #333;
        width: 25%;
    }
    .con:nth-child(4n) {
        margin-right: 0;
    }
    .item2Text {
        font-size: 13px;
        margin-top: 5px;
        width: 80px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1; /* 超出几行省略 */
        overflow: hidden;
    }
}
</style>