<template>
    <div class="mainCont">
        <van-cell-group v-show="MoreShow" class="Mod-common" :border="false">
            <!-- <van-cell title="查看更多会话" is-link @click="jumpPage(1)">
                <template #right-icon>
                    <van-icon name="arrow" color="#999999" size="12" />
                </template>
            </van-cell> -->
        </van-cell-group>
        <div class="msgCenter" @click="jumpPage(2)">
            <img class="head" src="@/images/img/talk_zxconsult.png" alt="" />
            <div class="content">
                <div class="flexbtwn">
                    <p class="title">消息中心</p>
                    <!-- <p class="time">{{ item.pushTime }}</p> -->
                </div>
                <div class="flexbtwn">
                    <!-- <p>{{ item.newMessage }}</p> -->
                    <p class="unread" v-if="item.messageCount">
                        {{ item.messageCount }}
                    </p>
                </div>
            </div>
        </div>

        <div class="iframe">
            <iframe
                v-if="refresh"
                :src="imTalkUrl"
                style="
                    height: 100%;
                    width: 100%;
                    margin: 0;
                    border: 0;
                    display: block;
                "
            >
            </iframe>
        </div>
    </div>
</template>

<script>
import { getPatientImUserID } from "@/api/consult.js";
import { queryUnReadMessageCount } from "@/api/message.js";
import Vue from "vue";
import { CellGroup, Cell } from "vant";
Vue.use(CellGroup).use(Cell);
import tools from "@/util/tools.js";

export default {
    data() {
        return {
            imUserId: "123",
            imTalkUrl: "",
            refresh: true,
            item: {},
            // 市民卡app中，隐藏纳里相关 查看更多 入口
            MoreShow: true,
        };
    },
    created() {
        this.getImTalkUrl();
        tools.handleSetPoint({
            trackingContent: "会话",
        });
        if (
            sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5" ||
            localStorage.getItem("interHosp_origin") == "smk"
        ) {
            // s市民卡中，隐藏纳里的查看更多 入口
            this.MoreShow = false;
        }
    },
    mounted() {},
    watch: {
        imTalkUrl() {
            this.refresh = false;
            this.$nextTick(() => {
                this.refresh = true;
            });
        },
    },
    methods: {
        async getImTalkUrl(type) {
            // 先获取用户信息
            if (
                !(
                    this.$store.state.uInfo.userId ||
                    localStorage.getItem("userId")
                )
            ) {
                await this.$store.dispatch("getLogin");
            }
            let id =
                this.$store.state.uInfo.userId ||
                localStorage.getItem("userId");
            let uInfo =
                JSON.parse(window.localStorage.getItem("userInfo")) || "";
            let token = uInfo.token;
            let originStr =
                location.hostname.indexOf("10.100.10") > -1
                    ? "https://jsbceshi.hfi-health.com:18188"
                    : location.origin;
            // id = "d8332783-c0f5-4cd8-88c8-3538098ec243";
            if (localStorage.getItem("imUserId")) {
                this.imUserId = localStorage.getItem("imUserId");
                this.imTalkUrl = `${originStr}/${
                    process.env.VUE_APP_TALK
                }/#/chat-list?id=${localStorage.getItem(
                    "imUserId"
                )}&userType=2&token=${token}`; //id 用户的imid
                this.getMsgData();
            } else {
                // 缓存里没有imUserId
                getPatientImUserID(id).then((res) => {
                    console.log("talk talk talk talk 00000", res);
                    if (res) {
                        let imUserId = res;
                        this.imUserId = imUserId;
                        localStorage.setItem("imUserId", imUserId);
                        this.getMsgData();
                        this.imTalkUrl = `${originStr}/${process.env.VUE_APP_TALK}/#/chat-list?id=${imUserId}&userType=2&token=${token}`; //id 用户的imid
                    }
                });
            }
        },
        getMsgData() {
            let data = {
                userId: this.imUserId,
            };
            queryUnReadMessageCount(data).then((res) => {
                console.log(res);
                if (res && res.length) {
                    let list = res.filter((item) => {
                        if (item.displayLocationType == 0) {
                            return item;
                        }
                    });
                    console.log("====", list);
                    this.item = list ? list[0] : {};
                }
            });
        },
        async jumpPage(type) {
            // if (!(this.$store.state.uInfo.userId || localStorage.getItem("userId"))) {
            //   await this.$store.dispatch("getLogin");
            // }
            if (type == 1) {
                if (
                    navigator.userAgent
                        .toLowerCase()
                        .indexOf("micromessenger") > -1
                ) {
                    // 微信环境
                    window.location.href = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=Messagelist&uInfo=${window.localStorage.getItem(
                        "naliWxEncrUser"
                    )}`;
                } else {
                    // 其余支付宝小程序环境
                    window.location.href = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=Messagelist&source=jinTou-zfb-hlw&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`;
                }
            } else {
                // 消息中心
                // let url =
                //   location.origin + location.pathname + `#/messageCenter?type=0`;
                // tools.jumpUrl(url, "2");
                this.$router.push({
                    path: "/messageCenter",
                    query: {
                        type: 0,
                    },
                });
            }
        },
    },
};
</script>

<style lang="less" scoped>
.mainCont {
    background: #f6f6f6;
    height: 100vh;
    padding-top: 12px;
}
.Mod-common {
    margin-bottom: 12px;
}
.lImg {
    width: 45px;
    height: 45px;
    margin-right: 12px;
}

.msgCenter {
    display: flex;
    align-items: center;
    padding: 10px 0 10px 15px;
    background: #fff;
    position: relative;
    &:after {
        content: "";
        position: absolute;
        height: 1px;
        background: #f1f1f1;
        left: 72px;
        bottom: 0;
        right: 0;
    }

    .head {
        width: 45px;
        height: 45px;
        border-radius: 8px;
        margin-right: 12px;
    }
    .content {
        // height: 45px;
        // border-bottom: 1px solid #f1f1f1;
        width: calc(100% - 57px);
        // padding-bottom: 12px;
        display: flex;
        // flex-direction: column;
        justify-content: space-between;
        padding-right: 15px;
    }
    .flexbtwn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
        color: #999999;
    }
    .title {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
    }
    .time {
        font-size: 12px;
        color: #bdbdbd;
    }
    .unread {
        min-width: 10px;
        width: fit-content;
        height: 15px;
        background: #ff5a5a;
        border-radius: 7.5px;
        padding: 0 2.5px;
        color: #fff;
        font-size: 12px;
        text-align: center;
        line-height: 15px;
        flex-shrink: 0;
    }
}
::v-deep .van-cell {
    align-items: center;
    padding: 11px 15px;
    max-height: 65px;
}
::v-deep .van-cell::after {
    right: 0;
    border-color: #eeeeee;
}
::v-deep .van-cell__title {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
}
.iframe {
    height: calc(100% - 236px);
    // margin-top: 12px;
    margin-bottom: 5px;
}
</style>
