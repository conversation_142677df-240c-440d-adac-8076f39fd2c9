<template>
    <div class="listContainer">
        <van-tabs
            v-model="activeTab"
            title-active-color="#333333"
            @change="changeTab"
            v-if="$route.query.from != 'my'"
        >
            <van-tab
                v-for="(item, index) in tabList"
                :key="index"
                :title="item"
            >
                <!-- <div v-show="MoreShow" class="channel" @click="otherChannel">
                    <div>查看更多咨询单</div>
                    <van-icon name="arrow" />
                </div> -->
                <div
                    class="listData"
                    :class="consultList.length == 0 ? 'columnFlex' : ''"
                >
                    <div v-if="consultList.length == 0" class="noList">
                        <!-- <img :src="require('@/images/img/noConsultData.png')" alt="" />
            <div class="tip">暂无咨询记录</div>
            <div class="turnBtn" @click="toDoctorList">
              找医生咨询
            </div> -->
                    </div>
                    <div class="consultList" v-else>
                        <van-list
                            v-model="listLoading"
                            :finished="finished"
                            finished-text="没有更多了"
                            :immediate-check="false"
                            @load="queryList"
                        >
                            <consult-item
                                v-for="(item, index) in consultList"
                                :key="index"
                                :data="item"
                                @refreshData="refreshData"
                                @click.native="toRefundDetail(item)"
                            ></consult-item>
                        </van-list>
                    </div>
                </div>
            </van-tab>
        </van-tabs>
        <div class="consultList" v-if="$route.query.from == 'my'">
            <van-list
                v-model="listLoading"
                :finished="finished"
                finished-text="没有更多了"
                :immediate-check="false"
                @load="queryList"
            >
                <consult-item
                    v-for="(item, index) in consultList"
                    :key="index"
                    :data="item"
                    @click.native="toRefundDetail(item)"
                ></consult-item>
            </van-list>
        </div>
    </div>
</template>
<script>
import consultItem from "./components/consultItem.vue";
import { Tab, Tabs, List } from "vant";
import { queryList } from "@/api/consult.js";
export default {
    components: {
        [Tab.name]: Tab,
        [Tabs.name]: Tabs,
        [List.name]: List,
        consultItem,
    },
    data() {
        return {
            activeTab: 0,
            listLoading: false,
            finished: false,
            pageNum: 1,
            tabList: ["全部", "待支付", "已支付", "待评价", "已结束", "已退款"],
            consultList: [],
            MoreShow: true,
        };
    },
    created() {
        if (
            sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5" ||
            localStorage.getItem("interHosp_origin") == "smk"
        ) {
            // s市民卡中，隐藏纳里的查看更多 入口
            this.MoreShow = false;
        }
        this.queryList();
    },
    methods: {
        changeTab(name, title) {
            this.consultList = [];
            this.finished = false;
            this.pageNum = 1;
            this.queryList();
        },
        getOrderStatus(name) {
            let status = [];
            switch (name) {
                case 0:
                    status = []; // 全部
                    break;
                case 1:
                    status = ["0"]; // 待支付
                    break;
                case 2:
                    status = ["1", "2"]; // 已支付
                    break;
                case 3:
                    status = ["3"]; // 待评价
                    break;
                case 4:
                    status = ["4"]; // 已结束
                    break;
                case 5:
                    status = ["51", "52", "53", "54", "6", "7"]; // 已退款
                    break;
                default:
                    status = [];
                    break;
            }
            return status;
        },
        queryList() {
            if (this.$route.query.from == "my") {
                const data = {
                    orderStatus: ["51", "52", "53", "54", "6", "7"],
                    userId: window.localStorage.getItem("userId"),
                    pageNum: this.pageNum,
                    pageSize: 10,
                };
                queryList(data).then((res) => {
                    this.listLoading = false;
                    this.consultList = this.consultList.concat(res.list);
                    if (this.pageNum == res.pages) {
                        this.finished = true;
                    } else {
                        this.pageNum++;
                    }
                });
            } else {
                const data = {
                    userId: window.localStorage.getItem("userId"),
                    orderStatus: this.getOrderStatus(this.activeTab),
                    pageNum: this.pageNum,
                    pageSize: 10,
                };
                queryList(data).then((res) => {
                    this.listLoading = false;
                    this.consultList = this.consultList.concat(res.list);
                    if (this.pageNum >= res.pages) {
                        this.finished = true;
                    } else {
                        this.pageNum++;
                    }
                });
            }
        },
        toDoctorList() {
            this.$router.push({
                path: "/searchDoctorList",
                query: {},
            });
        },
        toRefundDetail(item) {
            // orderSource=1时才是我们系统的订单第三方订单没有订单详情
            if (item.orderSource != 1) return;
            this.$router.push({
                path: "/myConsult",
                query: {
                    orderNo: item.orderNo,
                },
            });
        },
        refreshData() {
            this.consultList = [];
            this.finished = false;
            this.pageNum = 1;
            this.queryList();
        },
        otherChannel() {
            let url = "";
            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                // 微信环境
                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=userConsultList&uInfo=${window.localStorage.getItem(
                    "naliWxEncrUser"
                )}`;
            } else {
                // 其余支付宝小程序环境
                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=userConsultList&source=jinTou-zfb-hlw&uInfo=${window.localStorage.getItem(
                    "encrUser"
                )}`;
            }
            window.location.href = url;
        },
    },
};
</script>
<style lang="less" scoped>
::v-deep .van-tabs__line {
    background: linear-gradient(90deg, #26c8b3 0%, #36eaab 100%);
    width: 0.27rem;
    bottom: 0.22rem;
}

.listContainer {
    font-family: PingFang SC;

    .listData {
        height: calc(100vh - 0.44rem);

        .noList {
            img {
                display: block;
                width: 1.07rem;
                height: 1.2rem;
                margin: auto;
            }

            .tip {
                font-size: 0.14rem;
                font-weight: 400;
                color: #777777;
                text-align: center;
                margin-top: 0.195rem;
            }

            .turnBtn {
                width: 1.85rem;
                height: 0.4rem;
                background: #01cda7;
                border-radius: 0.2rem;
                font-size: 0.15rem;
                color: #fff;
                margin: auto;
                line-height: 0.4rem;
                text-align: center;
                margin-top: 0.3rem;
            }
        }
    }
    .channel {
        display: flex;
        justify-content: space-between;
        margin-top: 0.12rem;
        background-color: #fff;
        padding: 0.165rem 0.165rem 0.165rem 0.15rem;
    }
}
</style>
