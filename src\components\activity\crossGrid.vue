<!--
 * @Author: your name
 * @Date: 2025-05-28 09:45:21
 * @LastEditTime: 2025-06-05 14:43:15
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 十字格展台？
 * @FilePath: \h5-interhosp\src\components\activity\crossGrid.vue
-->
<template>
    <!-- 十字格？？ -->
    <div
        v-show="isShow"
        style="
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            justify-content: space-evenly;
        "
        class="conStyle"
    >
        <div
            class="stageList"
            v-for="(innerEle, index) in commonlist"
            :key="index"
            style="
                width: 1.58rem;
                height: 0.64rem;
                margin-top: 0.1rem;
                margin-bottom: 0.1rem;
                overflow: hidden;
            "
        >
            <img
                style="width: 100%"
                v-lazy="innerEle.iconUrl"
                @click="jumpUrl(innerEle, childStageId, childStageName)"
                alt=""
            />
        </div>
    </div>
</template>

<script>
import tools from "@/util/tools";
export default {
    props: {
        datalists: {
            type: Object,
        },
        stageName: {
            type: String,
        },
        stageId: {
            type: String,
        },
    },
    data() {
        return {
            commonlist: "",
            elementId: "",
            isShow: false,
            childStageId: "",
            childStageName: "",
        };
    },

    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("活动页十字格", temp);
        if (
            temp.stageTypeName === "十字格" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.commonlist = temp.childStageList;
            this.childStageId = temp.childStageId;
            this.childStageName = temp.childStageName;
        }
    },

    methods: {
        jumpUrl(item, id, name) {
            console.log("跳转参数", item);
            if (item.jumpUrl) {
                tools
                    .handleSetPoint({
                        stageId: this.stageId,
                        childStageId: id,
                        stageAppId: item.applicationId,
                        trackingContent: `${this.stageName}-${name}-${item.elementContent}`,
                        businessName: "activity",
                    })
                    .then(() => {
                        tools.jumpUrlManage(item);
                    });
            }
        },
    },
};
</script>
<style scoped>
.stageList {
    flex-shrink: 0;
    width: 20%;
    margin-bottom: 15px;
}
.conStyle {
    background-color: #fff;
    width: 345px;
    border-radius: 8px;
    margin: 0 auto;
    margin-bottom: 10px;
}
</style>