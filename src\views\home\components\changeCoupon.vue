<!--
 * @Author: shenpp
 * @Date: 2024-03-19
 * @LastEditTime: 2024-04-23 10:10:42
 * @Description: 确认订单时 选择优惠券列表
-->
<template>
    <div>
        <ul>
            <li
                :class="item.disable == 0 ? 'noUse' : ''"
                v-for="(item, index) in couponList"
                :key="index"
                @click="changeItem(item, index)"
            >
                <div class="left" v-if="item.couponType == 1">
                    <div class="top"><span>¥</span>{{ item.discount }}</div>
                    <div class="bot">满{{ item.minThreshold }}可用</div>
                </div>
                <div class="left" v-if="item.couponType == 3">
                    <div class="top"><span>¥</span>{{ item.discount }}</div>
                    <div class="bot">无门槛</div>
                </div>
                <div class="left" v-if="item.couponType == 2">
                    <div class="zhekou">{{ item.discount }}<span>折</span></div>
                </div>
                <div class="left" v-if="item.couponType == 0">
                    <div class="jianmian">全额减免</div>
                </div>
                <div class="middle">
                    <div class="title van-multi-ellipsis--l2">
                        <span class="tImg">图文咨询</span>
                        <span class="tspan"> {{ item.couponName }} </span>
                    </div>
                    <div class="time">
                        {{ item.startValidTime }}~{{ item.endValidTime }}
                    </div>
                </div>
                <div v-if="item.disable == 0" class="right">
                    <img
                        class="noUseimg"
                        src="./../../../images/confirmOrder/nouse.png"
                    />
                </div>
                <div v-else class="right">
                    <!-- <img src="./../../../images/confirmOrder/radio.png" /> -->
                    <div
                        class="use_"
                        :class="item.used == 1 ? 'use_has' : ''"
                    ></div>
                </div>
            </li>
        </ul>
    </div>
</template>

<script>
import Vue from "vue";
import { RadioGroup, Radio } from "vant";

Vue.use(Radio);
Vue.use(RadioGroup);
export default {
    props: {
        couponList: {
            type: Array,
        },
    },
    data() {
        return {};
    },

    mounted() {},

    methods: {
        changeItem(item, index) {
            if (item.disable == 0) return;
            this.$emit("beChanged", item);
        },
    },
};
</script>
<style lang="less" scoped>
ul {
    width: 3.45rem;
    margin: 0 auto;
    margin-top: 0.15rem;
}
.noUse {
    background-image: url("./../../../images/confirmOrder/couponGray.png");
    .left {
        color: #999999;
    }
    .middle .title .tImg {
        background: #c2c2c2;
    }
}
li {
    background-image: url("./../../../images/confirmOrder/couponBJ.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 3.45rem;
    height: 0.965rem;
    margin-bottom: 0.1rem;
    display: flex;
    align-items: center;
    .left {
        color: #3ebfa0;
        font-size: 0.2rem;
        width: 1rem;
        //   margin-left: 0.22rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .top {
            font-size: 0.33rem;
            span {
                font-size: 0.2rem;
            }
        }
        .bot {
            font-size: 0.12rem;
        }
        .zhekou {
            font-size: 0.33rem;
            font-weight: bold;
            span {
                font-size: 0.2rem;
            }
        }
        .jianmian {
            width: 50%;
            font-weight: bold;
        }
    }
    .middle {
        width: 2.1rem;
        align-self: baseline;
        height: 0.965rem;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .title {
            margin: 0 0 0 0.15rem;
            position: relative;
            color: #333333;
            vertical-align: middle;
            width: 90%;
            .tImg {
                position: absolute;
                left: 0;
                top: 0;
                margin-top: 0.02rem;
                display: inline-block;
                width: 0.5rem;
                line-height: 0.16rem;
                background: linear-gradient(90deg, #3fe5cf 0%, #26c8b3 100%);
                border-radius: 0.02rem;
                font-size: 0.1rem;
                font-family: PingFang SC;
                font-weight: 400;
                color: #ffffff;
                text-align: center;
            }
            .tspan {
                width: 70%;
                font-size: 0.15rem;
                font-weight: bold;
                margin-left: 0.6rem;
                line-height: 1.4;
                letter-spacing: 0.005rem;
                text-align: left;
                display: -webkit-box;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
        }
        .time {
            color: #888888;
            font-size: 0.11rem;
            margin-left: 0.15rem;
        }
    }
    .right {
        .use_ {
            background-image: url("./../../../images/confirmOrder/round.png");
            background-size: 100% 100%;
            background-repeat: norepeat;
            width: 0.18rem;
            height: 0.18rem;
        }
        .use_has {
            background-image: url("./../../../images/confirmOrder/radio.png");
        }
        .noUseimg {
            width: 0.66rem;
            height: 0.66rem;
            margin-right: 0.08rem;
            margin-top: 0.1rem;
        }
    }
}
</style>
