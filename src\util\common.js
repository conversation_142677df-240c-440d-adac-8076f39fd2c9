/*
 * @Descripttion:
 * @version:
 * @Author: changqing
 * @Date: 2019-04-02 14:29:27
 * @LastEditors: DESKTOP-TJTC9EU
 * @LastEditTime: 2023-08-03
 */
import { sha256 } from "js-sha256";
import CryptoJS from "crypto-js";
const common = {
    evn: "build", //dev开发模式，模拟登录，不加载cordova    build生产模式
    tips: {
        noResp: "网络连接超时，请稍后再试~",
        wait: "当前预约人数过多，请稍后再试~",
        hf: '<p style="line-height:1.3;">亲爱的，您操作太过频繁，<br>请稍后再试~</p>',
        noNet: "没有网络，请看下是否连上网络了~",
    },
    paperObj: {
        "01": "居民身份证",
        "03": "护照",
    },

    /* encrypt(content) {
        let aesKey = "hfdjk670qEH5lm3b";
        var key = CryptoJS.enc.Utf8.parse(aesKey);
        var scontent = CryptoJS.enc.Utf8.parse(content);
        var encrypted = CryptoJS.AES.encrypt(scontent, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        return encrypted.ciphertext.toString(); //返回的是16进制格式的密文
    },

    dencrypt(content) {
        let aesKey = "hfdjk670qEH5lm3b";
        var key = CryptoJS.enc.Utf8.parse(aesKey);
        var datahex = CryptoJS.enc.Hex.parse(content);
        var dataBase64 = CryptoJS.enc.Base64.stringify(datahex);
        var decrypted = CryptoJS.AES.decrypt(dataBase64, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        return decrypted.toString(CryptoJS.enc.Utf8);
    }, */

    dencryptHeader(content) {
        let aesKey = "jfdjk670qEH5lm3b";
        var key = CryptoJS.enc.Utf8.parse(aesKey);
        var datahex = CryptoJS.enc.Base64.parse(content);
        var dataBase64 = CryptoJS.enc.Base64.stringify(datahex);
        var decrypted = CryptoJS.AES.decrypt(dataBase64, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
        });
        return decrypted.toString(CryptoJS.enc.Utf8);
    },

    sha256_(timeStamp, nonce) {
        // nonce = "22c533cc-fd58-4b58-bdb6-24091e57313e";
        // timeStamp = "1555925625711";
        let signingKey = "JlolWcxSD3fTdISQkEURIQ==";

        let salt_ = timeStamp % 10;
        let salt = nonce.substring(salt_);
        debugger;
        let stringSrc = signingKey + timeStamp + nonce + salt;
        return sha256(stringSrc);
    },

    getUrlParam: function (name) {
        var result = "";
        var url = window.location.href;
        // var url = (window.location.href);
        if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            // name += "=";
            let arr = param.split('&')
                .reduce(function (acc, param) {
                    const [key, value] = param.split('=');
                    acc[key] = decodeURIComponent(value);
                    return acc;
                }, {});
            /* if (param.indexOf(name) > -1) {
                var r = param.substr(param.indexOf(name) + name.length);
                if (r.indexOf("&") != -1) {
                    r = r.substring(0, r.indexOf("&"));
                }
                result = r;
            } */

            result = arr[name]

        }
        return result || "";
    },
    getUrlParam_(url, name) {
        var result = "";
        // var url = (window.location.href);
        /* if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            name += "=";
            if (param.indexOf(name) > -1) {
                var r = param.substr(param.indexOf(name) + name.length);
                if (r.indexOf("&") != -1) {
                    r = r.substring(0, r.indexOf("&"));
                }
                result = r;
            }
        }
        return result; */
        if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            let arr = param.split('&')
                .reduce(function (acc, param) {
                    const [key, value] = param.split('=');
                    acc[key] = decodeURIComponent(value);
                    return acc;
                }, {});
            result = arr[name]

        }
        return result;
    },

    updateUrlParam(url, name, value_) {
        debugger

        if (!url) {
            url = window.location.href;
        }

        if (url.indexOf("?") > -1) {
            var param = url.substring(url.indexOf("?") + 1);
            let arr = param.split('&');
            let obj = {};
            arr.forEach(element => {
                const [key, value] = element.split('=');
                if (key && value) {
                    if (key == name) {
                        obj[key] = value_;
                    } else {
                        obj[key] = decodeURIComponent(value);
                    }

                }
            });
            // obj[name] = value
            let str = "";
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    str = str + key + "=" + obj[key] + "&";
                }
            }
            return url.substring(0, url.indexOf("?") + 1) + str;
        }

        return "";




    },

    toJSON(str) {
        return new Function("", "return " + str)();
    },


};

export default common;
