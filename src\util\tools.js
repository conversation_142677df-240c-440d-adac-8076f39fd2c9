/*
 * @Author: shenpp
 * @Date: 2023-05-25
 * @LastEditTime: 2025-05-08 18:22:42
 * @Description: 获取token的方法
 */
import store from "@/store";
import { getToken, setPoint, userAuthorize, sankeyCollect } from "@/api/api";
import { Toast } from "vant";
import dayjs from "dayjs";
import router from "@/router";

const tools = {
    // 生成随机数
    generateRandomString(length) {
        let result = "";
        const characters =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

        for (let i = 0; i < length; i++) {
            result += characters.charAt(
                Math.floor(Math.random() * characters.length)
            );
        }
        return result;
    },
    // 转化率埋点--桑基图
    handleConversionPoint(url) {
        // return
        // 初始化id供用户转化率分析使用
        if (!window.localStorage.getItem("localId")) {
            window.localStorage.setItem("localId", new Date().getTime() + "-" + this.generateRandomString(8)
            );
        }
        var platform = "";
        if (
            sessionStorage.getItem("hlw_remoteChannel") == "health_zheliban_H5"
        ) {
            platform = "zlb";
        } else if (
            sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5"
        ) {
            platform = "smk";
        } else if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
            platform = "wechat";
        } else if (navigator.userAgent.indexOf("AliApp") > -1) {
            platform = "alipay";
        } else {
            platform = "H5";
        }
        const data = {
            localId: window.localStorage.getItem("localId"),
            platform: platform,
            userId: store.state.uInfo.userId,
            url: url,
            actionTime: dayjs(new Date().getTime()).format("YYYY-MM-DD HH:mm:ss"),
        };
        sankeyCollect(data);
    },

    async tbUinfo(url) {
        let uinfo = {
            ...store.state.uInfo,
            name: store.state.uInfo.name,
            paperNum: store.state.uInfo.paperNum,
            phone: store.state.uInfo.phone,
            token: store.state.uInfo.token,
            userId: store.state.uInfo.userId,
        };
        if (window.sessionStorage.getItem("token")) {
            store.dispatch("getUInfo", this.mergeUser(uinfo));
        } else {
            debugger;
            await getToken().then(res => {
                // uinfo.token = res.token;
                // uinfo.userId = res.userId;
                store.dispatch("getUInfo", this.mergeUser(res));
                console.log("this.uinfo", store.state.uInfo);
                debugger;
                if (url) {
                    window.location.href = url;
                }

            });
        }
    },

    // 第一版本的跳转， 普通版本 非展台   医生主页、医生服务、医院主页（第一版）
    async jumpUrl(url, sign = 1, isRepalce) {
        console.log("url,sign", url, isRepalce);
        console.log("isRepalce", isRepalce);
        if (!url) {
            Toast({ message: "敬请期待", className: "LTip" });
            return;
        }
        // 外部地址跳转
        /**
         * 1.判断是否需要登录，需要调取一键登录
         * 2.判断是否是预约挂号，预约挂号需要获取token
         * 3.判断是否是纳里链接，是的话就要传加密用户信息
         */
        console.log("url,sign", store.state.uInfo);
        console.log("url,sign", url, sign);
        debugger;
        // alert("store.state.uInfo:" + store.state.uInfo)

        let isZfb = navigator.userAgent.indexOf("AliApp") > -1;
        // 浙里办
        let isZLB =
            sessionStorage.getItem("hlw_remoteChannel") == "health_zheliban_H5" ||
            sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5";
        if (
            (isZfb &&
                !store.state.uInfo &&
                //   && window.localStorage.getItem("interHosp_origin") != "szMini"
                // main.js 中 增加对mine的路由进入判断，统一在进入mine之前获取userid  2023/10/07
                !window.localStorage.getItem("userInfo")) ||
            (!isZfb && !window.localStorage.getItem("naliWxEncrUser") && !isZLB)
        ) {
            if (!(await store.dispatch("getLogin"))) {
                Toast({ message: "用户信息获取失败", className: "LTip" });
                return;
            }
        }
        console.log("jumpurl", url);
        // 非金投 且没有clientId的直接拼接encrUser
        if (
            url.indexOf("hfi-health.com") == -1 &&
            url.indexOf("10.100.") == -1 &&
            url.indexOf("clientId") == -1
        ) {
            console.log("url", url + window.localStorage.getItem("encrUser"));
            debugger;
            // 非金投链接地址，拼接用户信息后跳转
            // 例如纳里地址
            //   url += window.sessionStorage.getItem("encrUser");
            if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
                if (url.indexOf("zjshlwyy.zjjgpt.com") > -1) {
                    // 纳里的链接，在微信中拼接
                    url =
                        decodeURIComponent(url) +
                        window.localStorage.getItem("naliWxEncrUser");
                }
                //   微信需要两次解码，如数智陪诊等，需要拼接支付宝的那套加密
                else {
                    url =
                        decodeURIComponent(url) + window.localStorage.getItem("encrUser");
                }
            } else {
                url += window.localStorage.getItem("encrUser");
            }
        } else {
            // 使用的后管中应用权限字段，当为弱实名或强实名状态时，需要token,即sign=2/3
            if (sign == "2" || sign == "3") {
                debugger;
                await this.tbUinfo();
            } /* else {
                debugger
                window.location.href = url;
            } */
        }
        if (isRepalce) {
            console.log("2234");
            window.location.replace(url);
        } else {
            window.location.href = url;
        }
    },
    gotoMini(item) {
        debugger;
        // todo ????? 如果是展台应用 出参没有 miniIdWx jumpUrlWx  取 miniId/ jumpUrl
        if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
            // 微信小程序
            debugger;
            let it = {
                miniIdWx: item.miniIdWx || item.miniId,
                jumpUrlWx: item.jumpUrlWx || item.jumpUrl,
            };
            wx.miniProgram.navigateTo({
                url: "/pages/jump/jump?item=" + encodeURIComponent(JSON.stringify(it)),
            });
        } else {
            // 支付宝小程序
            my.postMessage({
                action: "gotoMini",
                appId: item.miniId,
                url: item.jumpUrl,
                authStatus: item.status,
            });
        }
    },
    isInHlwMini() {
        if (sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5" ||
            sessionStorage.getItem("hlw_remoteChannel") == "health_zheliban_H5" ||
            window.localStorage.getItem("interHosp_origin") == "smk" || window.localStorage.getItem("interHosp_origin") == "xhmhMini") {
            return false
        }
        return true
    },
    // 接入展台的跳转 isRepalce有则关闭当前并跳转  isApplication 表示是展台应用 没有微信配置字段
    async jumpUrlManage(item, isRepalce) {
        debugger
        // 首页埋点处理（在home组件埋点无法有效监听页面离开）
        if (router.currentRoute.name === "home") {
            await this.handleSetPoint({
                trackingContent: "首页",
                operateStartTime: Number(
                    window.sessionStorage.getItem("operateStartTime")
                ),
            });
        }
        console.log("jumpUrlManage的item", item);
        console.log("isRepalce", isRepalce);
        // 在市民卡app中需要进行市民卡埋点
        if (navigator.userAgent.indexOf("smkVersion") > -1) {
            if (item.elementId !== undefined || item.eventType !== '' || item.eventType == '1,2' || item.eventType == 1) {
                // 点击跳转
                try {
                    window.bury.send('click', {
                        _element_id: item.elementId,
                        _element_content: item.elementContent,
                        _element_sn: item.index + 1
                    })
                } catch (error) {
                    console.log(error)
                }
            }
        }
        if (
            !(
                item.jumpUrl ||
                (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 &&
                    item.jumpUrlWx)
            )
        ) {
            Toast({ message: "敬请期待", className: "LTip" });
            return;
        }
        // alert(JSON.stringify(item))
        // 当配置成支付宝小程序时，需要postmessage到小程序
        // 微信小程序中 appTypeWx为3  或者 支付宝小程序

        if ("appTypeWx" in item) {
            // 医生或医院的入口
            if (
                (item.appTypeWx == "3" &&
                    navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) ||
                (item.appType == "3" && navigator.userAgent.indexOf("AliApp") > -1)
            ) {
                tools.gotoMini(item);
                return;
            }
        } else {
            // 应用的入口
            if (item.appType == "3") {
                tools.gotoMini(item);
                return;
            }
        }

        console.log("跳转");
        // 当配置为游客时,不需要用户信息
        if (item.status == "0") {
        } else {
            // 其他非游客
            // 外部地址跳转
            /**
             * 1.判断是否需要登录，需要调取一键登录
             * 2.判断是否是预约挂号，预约挂号需要获取token
             * 3.判断是否是纳里链接，是的话就要传加密用户信息
             */
            console.log("url,sign0", store.state.uInfo);
            console.log("url,sign1", item.jumpUrl, item.status);
            console.log("用户信息11", store.state.uInfo);
            console.log("用户信息11", this.isInHlwMini());
            debugger;
            if ((this.isInHlwMini() && !window.localStorage.getItem("encrUserMini")) || !store.state.uInfo) {
                if (!(await store.dispatch("getLogin"))) {
                    Toast({ message: "用户信息获取失败", className: "LTip" });
                    return;
                }
            }
            console.log("jumpurl", item.jumpUrl);
            if (
                item.jumpUrl.indexOf("hfi-health.com") == -1 &&
                item.jumpUrl.indexOf("clientId") == -1 &&
                !item.clientId &&
                item.jumpUrl.indexOf("10.100.") == -1
            ) {
                debugger;
                // 非金投链接地址，拼接用户信息后跳转 外链市儿童不需要拼接encrUser
                // 例如纳里地址
                // item.jumpUrl += window.sessionStorage.getItem("encrUser");
                // item.jumpUrl += window.localStorage.getItem("encrUser");
                if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
                    // 微信小程序内 需要两次解码 jumpurl
                    if (item.jumpUrl.indexOf("zjshlwyy.zjjgpt.com") > -1) {
                        // 纳里的链接，在微信中拼接
                        item.jumpUrl =
                            decodeURIComponent(item.jumpUrl) +
                            window.localStorage.getItem("naliWxEncrUser");
                    }
                    //   微信需要两次解码，如数智陪诊等，需要拼接支付宝的那套加密
                    else {
                        item.jumpUrl =
                            decodeURIComponent(item.jumpUrl) +
                            window.localStorage.getItem("encrUser");
                    }
                } else {
                    item.jumpUrl += window.localStorage.getItem("encrUser");
                }
            }
            // 使用的后管中应用权限字段，当为弱实名或强实名状态时，需要token
            if (item.status == "2" || item.status == "3") {
                debugger;
                await this.tbUinfo();
                if (item.clientId) {
                    await userAuthorize(
                        window.localStorage.getItem("userId"),
                        item.clientId
                    ).then(res => {
                        console.log("auth授权", res);
                        debugger;
                        /* item.jumpUrl =
                                                                                        item.jumpUrl.indexOf("?") > -1
                                                                                            ? `${item.jumpUrl}&code=${res.code}`
                                                                                            : `${item.jumpUrl}?code=${res.code}`;
                                                                                    console.log("code链接", item.jumpUrl); */
                        let openId;
                        if (
                            navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
                        ) {
                            openId = window.localStorage.getItem("wxOpenid");
                        } else {
                            openId = window.localStorage.getItem("alipayUserId");
                        }
                        item.jumpUrl =
                            item.jumpUrl.indexOf("?") > -1
                                ? `${item.jumpUrl}&code=${res.code
                                }&openId=${openId}&time=${new Date().getTime()}`
                                : `${item.jumpUrl}?code=${res.code
                                }&openId=${openId}&time=${new Date().getTime()}`;
                        console.log("code链接", item.jumpUrl);
                    });
                }
            }
        }
        // auth页已经判断微信解码两次了--这里可以去除了
        /* if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
            // 微信小程序内 需要两次解码 jumpurl
            item.jumpUrl = decodeURIComponent(item.jumpUrl);
        } */
        console.log("跳转链接", item.jumpUrl);
        // alert(item.jumpUrl)
        if (isRepalce) {
            console.log("2234");
            window.location.replace(item.jumpUrl);
        } else {
            window.location.href = item.jumpUrl;
        }
    },
    gotoWX(data, hospUrl) {
        if (!data.miniIdWx) {
            // 没有小程序id，区分卫健和自建平台
            // 卫健

            // 没有小程序id  自建平台
            if (!data.clientId) {
                // 1.没有miniid，无clientid  跳转我们自己页面
                data.jumpUrl = hospUrl;
            } else {
                // 2.没有clientid  有clientid 一定有unicode  如市儿童H5  授权后跳对应H5
                if (data.jumpUrlWx) {
                    data.jumpUrl = data.jumpUrlWx;
                }
                data.jumpUrl =
                    data.jumpUrl.indexOf("?") > -1
                        ? `${data.jumpUrl}&clientId=${data.clientId}&source=miniWechat&unicode=${data.unicode}`
                        : `${data.jumpUrl}?clientId=${data.clientId}&source=miniWechat&unicode=${data.unicode}`;
                data.status = "2";
            }
        } else {
            // 有小程序id 卫健与自建平台逻辑一致的情况
            // 3.有小程序id  有unicode  拼接unicode
            data.jumpUrlWx =
                data.jumpUrlWx.indexOf("?") > -1
                    ? `${data.jumpUrlWx}&unicode=${data.unicode}&source=miniWechat`
                    : `${data.jumpUrlWx}?unicode=${data.unicode}&source=miniWechat`;
            // 4.有小程序id  无unicode  无特殊处理  直接跳转对方小程序
        }
        tools.jumpUrlHosporg(data);
    },
    //  跳转到机构主页 0821
    async gotoOrg(data_) {
        // 首页埋点处理（在home组件埋点无法有效监听页面离开）
        if (router.currentRoute.name === "home") {
            await this.handleSetPoint({
                trackingContent: "首页",
                operateStartTime: Number(
                    window.sessionStorage.getItem("operateStartTime")
                ),
            });
        }
        let data = { ...data_ };
        let jumpUrl = data.jumpUrl;
        let hospUrl =
            location.origin +
            location.pathname +
            `#/hosporg?unicode=${data.unicode}&hospName=${data.hospName}&hospId=${data.hospId}&hospOrgCode=${data.hospOrgCode}`;
        if (
            sessionStorage.getItem("hlw_remoteChannel") == "health_zheliban_H5" ||
            sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5"
        ) {
            // 浙里办跳转
            window.location.href = hospUrl;
            return;
        }
        if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
            // 微信小程序
            tools.gotoWX(data, hospUrl);
            return;
        }

        if (!data.miniId) {
            // 没有小程序id，区分卫健和自建平台
            // 卫健
            if (store.state.isWjAliMini) {
                // 1.没有miniid，无clientid  跳转我们自己的
                // 增加orgId传参
                if (!data.clientId) {
                    jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                        hospUrl
                    )}`;

                    // jumpUrl = `/pages/index/index?orgId=${data.hospId}`;
                } else {
                    // 2.没有miniid，有clientid 一定有unicode，如市儿童H5  先跳自建平台小程序
                    let tempUrl =
                        data.jumpUrl.indexOf("?") > -1
                            ? `${data.jumpUrl}&clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`
                            : `${data.jumpUrl}?clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`;
                    // 小程序跳转链接拼接
                    jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                        tempUrl
                    )}`;
                    // 有clientid的需要userid authStatus传2  不能用authStatus  后面用到的是status
                    // data.authStatus = "2";
                    data.status = "2";
                }
                data.jumpUrl = jumpUrl;
                data.appType = "3";
            } else {
                // 没有小程序id  自建平台
                if (!data.clientId) {
                    // 1.没有miniid，无clientid  跳转我们自己页面
                    data.jumpUrl = hospUrl;
                } else {
                    // 2.没有clientid  有clientid 一定有unicode  如市儿童H5  授权后跳对应H5
                    data.jumpUrl =
                        data.jumpUrl.indexOf("?") > -1
                            ? `${data.jumpUrl}&clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`
                            : `${data.jumpUrl}?clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`;
                    data.status = "2";
                }
            }
        } else {
            // 有小程序id 卫健与自建平台逻辑一致的情况
            // 3.有小程序id  有unicode  拼接unicode
            data.jumpUrl =
                data.jumpUrl.indexOf("?") > -1
                    ? `${data.jumpUrl}&unicode=${data.unicode}&source=miniAlipay`
                    : `${data.jumpUrl}?unicode=${data.unicode}&source=miniAlipay`;
            // 4.有小程序id  无unicode  无特殊处理  直接跳转对方小程序
        }
        tools.jumpUrlHosporg(data);
        debugger;
    },

    // 卫健小程序，医院机构跳转
    async jumpUrlHosporg(item) {
        console.log("医院列表跳转", item);
        if (
            !(
                item.jumpUrl ||
                (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 &&
                    item.jumpUrlWx)
            )
        ) {
            // Toast({ message: "功能持续开发中，敬请期待", className: "LTip" });
            return;
        }
        debugger;
        // 友盟埋点 + 跳转
        // applicationCode

        if (
            (item.appTypeWx == "3" &&
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) ||
            (item.appType == "3" && navigator.userAgent.indexOf("AliApp") > -1)
        ) {
            // 当配置成支付宝小程序时，需要postmessage到小程序
            /* my.postMessage({
                                                                                            action: "gotoMini",
                                                                                            appId: item.miniId ? item.miniId : "2021002138635948",
                                                                                            url: item.jumpUrl,
                                                                                            authStatus: item.authStatus ? item.authStatus : "0",
                                                                                          }); */
            item.miniId = item.miniId ? item.miniId : "2021002138635948";
            // item.authStatus = item.authStatus ? item.authStatus : "0";
            item.status = item.status ? item.status : "0";
            tools.gotoMini(item);
            return;
        } else if (item.clientId) {
            // 有clientId就先拿userid再授权auth、
            console.log("store.state.uInfo", store.state.uInfo);
            if ((this.isInHlwMini() && !window.localStorage.getItem("encrUserMini")) || !store.state.uInfo) {
                if (!(await store.dispatch("getLogin"))) {
                    Toast({ message: "用户信息获取失败", className: "LTip" });
                    return;
                }
            }
            await this.tbUinfo();
            await userAuthorize(
                window.localStorage.getItem("userId"),
                item.clientId
            ).then(res => {
                console.log("auth授权", res);

                let openId;
                if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
                    openId = window.localStorage.getItem("wxOpenid");
                } else {
                    openId = window.localStorage.getItem("alipayUserId");
                }
                let url =
                    item.jumpUrl.indexOf("?") > -1
                        ? `${item.jumpUrl}&code=${res.code}&openId=${openId}`
                        : `${item.jumpUrl}?code=${res.code}&openId=${openId}`;
                // alert(url)
                window.location.href = url;
            });
        } else {
            debugger;
            window.location.href = item.jumpUrl;
        }
    },
    // 从小程序得到的用户信息无userId及token，在跳转预约挂号时需要请求接口得到userId及token
    mergeUser(userInfo, noToken) {
        console.log("mergeUser用户信息", userInfo);
        userInfo = { ...userInfo };
        console.log("mergeUser用户信息12", userInfo);
        if (sessionStorage.getItem("smkUId")) {
            userInfo.smkUId = sessionStorage.getItem("smkUId")
        }
        userInfo.paperId = "01";
        userInfo.paperType = "1";
        userInfo.paperName = "居民身份证";
        userInfo.verifyRealName = "true";
        userInfo.name = userInfo.name;
        userInfo.userName = userInfo.name;
        userInfo.maskName = userInfo.name;
        userInfo.paperNo = userInfo.paperNum || userInfo.paperNumber;
        userInfo.paperNum = userInfo.paperNum || userInfo.paperNumber;
        userInfo.maskCertNo = userInfo.paperNum || userInfo.paperNumber;
        userInfo.phone = userInfo.phone || userInfo.phoneNo;
        userInfo.phoneNo = userInfo.phone || userInfo.phoneNo;
        userInfo.maskMobile = userInfo.phone || userInfo.phoneNo;
        userInfo.userId =
            userInfo.userId || window.localStorage.getItem("userId") || "";
        if (noToken && !window.sessionStorage.getItem("token")) {
            // localStorage中取的用户信息  不能插入token  有可能已经失效了 
            userInfo.token = "";
        } else {
            userInfo.token = window.sessionStorage.getItem("token") || userInfo.token || "";
            if (userInfo.token || window.sessionStorage.getItem("token")) {
                window.sessionStorage.setItem(
                    "token",
                    userInfo.token || window.sessionStorage.getItem("token")
                );
                window.localStorage.setItem(
                    "token",
                    userInfo.token || window.sessionStorage.getItem("token")
                );
            }
        }

        // window.sessionStorage.getItem("token")  local中的token有可能失效
        debugger;
        // 存储使用者信息
        /* if (userInfo.token || window.sessionStorage.getItem("token")) {
          window.sessionStorage.setItem(
            "token",
            userInfo.token || window.sessionStorage.getItem("token")
          );
          window.localStorage.setItem(
            "token",
            userInfo.token || window.sessionStorage.getItem("token")
          );
        } */
        /** 因与会话中的userid冲突，删除
                                                            if (userInfo.userId || window.sessionStorage.getItem("userId")) {
                                                              window.sessionStorage.setItem(
                                                                "userId",
                                                                userInfo.userId || window.sessionStorage.getItem("userId")
                                                              );
                                                              window.localStorage.setItem(
                                                                "userId",
                                                                userInfo.userId || window.sessionStorage.getItem("userId")
                                                              );
                                                            }*/
        if (userInfo.userId) {
            window.localStorage.setItem(
                "userId",
                userInfo.userId || window.sessionStorage.getItem("userId")
            );
        }
        console.log("mergeUser用户信息set前", userInfo);
        window.sessionStorage.setItem("userInfo", JSON.stringify(userInfo));
        window.localStorage.setItem("userInfo", JSON.stringify(userInfo));
        window.sessionStorage.setItem("myInfo", JSON.stringify(userInfo));
        window.localStorage.setItem("myInfo", JSON.stringify(userInfo));
        return userInfo;
    },
    getQueryStringHash(name) {
        let result = "";
        if (window.location.href.includes("?")) {
            console.log(window.location.href
                .split("?")[1]
                .split("&"))
            console.log(window.location.href)
            window.location.href
                .split("?")[1]
                .split("&")
                .forEach(val => {
                    if (val.includes(name)) {
                        result = val.substring(name.length + 1);
                    }
                });
        }
        return result;
    },
    // 自建埋点
    handleSetPoint(data) {
        return new Promise((r, j) => {
            if (!window.localStorage.getItem("localId")) {
                window.localStorage.setItem("localId", new Date().getTime() + "-" + this.generateRandomString(8)
                );
            }
            const {
                stageId = "",
                childStageId = "",
                stageAppId = "",
                trackingContent,
                operateStartTime = new Date().getTime(),
                operateEndTime = new Date().getTime(),
                businessName = "",
                orgId = "",
                orgName = "",
                searchTerm = "",
                triggerType = "1",
                localId = window.localStorage.getItem("localId") || "",
                operationScenarioId = window.sessionStorage.getItem("sceneId") || "",
                operationScenarioName = window.sessionStorage.getItem("sceneName") || "",
                currentUrl = "",
                pushTaskId = window.sessionStorage.getItem("relateCode") || ""
            } = data;
            // 支付宝: 1,
            // 微信: 2,
            // 浙里办: 3,
            // 市民卡：4
            var channel;
            if (navigator.userAgent.indexOf("AliApp") > -1) {
                channel = 1;
            } else if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                channel = 2;
            } else if (window.navigator.userAgent.indexOf("smkVersion") > -1) {
                channel = 4;
            } else if (window.navigator.userAgent.toLowerCase().indexOf("toutiaomicroapp") > -1) {
                // 抖音,产品要求抖音的channel用remotechannel
                channel = sessionStorage.getItem("hlw_remoteChannel")
            } else {
                channel = 3;
            }
            const getQueryStringHash = name => {
                let result = "";
                if (window.location.href.includes("?")) {
                    window.location.href
                        .split("?")[1]
                        .split("&")
                        .forEach(val => {
                            if (val.includes(name)) {
                                result = val.substring(name.length + 1);
                            }
                        });
                }
                return result;
            };
            // 健康互联网医院H5
            let appKey = "2";
            if (getQueryStringHash("origin") == "wjAliMini") {
                // 卫健互联网医院
                appKey = "1";
            }
            var uInfo = JSON.parse(window.localStorage.getItem("userInfo")) || "";
            // if (uInfo.userId) {
            setPoint({
                identifyId: window.sessionStorage.getItem("identifyId"),
                appid: appKey,
                channelId: channel,
                userId: uInfo.userId,
                stageId,
                childStageId,
                stageAppId,
                trackingContent,
                operateStartTime: dayjs(operateStartTime).format("YYYYMMDDHHmmss"),
                operateEndTime: dayjs(operateEndTime).format("YYYYMMDDHHmmss"),
                businessName,
                orgId,
                orgName,
                searchTerm,
                triggerType,
                localId,
                operationScenarioId,
                operationScenarioName,
                currentUrl,
                pushTaskId
            }).then(() => {
                r();
            });
        });

        // }
    },
    isWeChat() {
        var ua = window.navigator.userAgent.toLowerCase();
        if (ua.indexOf("micromessenger") == -1) {
            return false;
        } else {
            return true;
        }
    },
    getRemoteChannel() {
        debugger;
        // 智能客服
        if (this.getQueryStringHash("AIOrigin")) {
            const AIOrigin = this.getQueryStringHash("AIOrigin")
            window.sessionStorage.setItem("hlw_remoteChannel", AIOrigin);
            return AIOrigin
        }
        let origin = this.getQueryStringHash("origin");
        // if (window.sessionStorage.getItem('hlw_remoteChannel')) {
        //     return window.sessionStorage.getItem('hlw_remoteChannel')
        // }
        console.log("originoriginoriginoriginoriginorigin", origin);
        if (origin == 'jktwxMini') {
            window.sessionStorage.setItem("hlw_remoteChannel", "health_wechat_hzjkt");
            return "health_wechat_hzjkt"
        } else if (origin == "xhmhMini") {
            // 西湖门户单独
            window.sessionStorage.setItem("hlw_remoteChannel", "xhmhMini");
            return "xhmhMini"
        } else if (origin == "XsmhMin") {
            if (window.navigator.userAgent.toLowerCase().indexOf("alipay") > -1) {
                window.sessionStorage.setItem("hlw_remoteChannel", "XsmhMin_alipay");
                return "XsmhMin_alipay"
            } else {
                window.sessionStorage.setItem("hlw_remoteChannel", "XsmhMin");
                return "XsmhMin"
            }

        } else if (origin == "aksu") {
            window.sessionStorage.setItem("hlw_remoteChannel", "health_hospital_aksu_mini");
            console.log(
                "aksuaksuaksuaksuaksualipayalipayalipayalipayalipayalipayalipay"
            );
            return "health_hospital_aksu_mini";
        } else if (origin == "zlb") {
            window.sessionStorage.setItem("hlw_remoteChannel", "health_zheliban_H5");
            return "health_zheliban_H5";
        } else if (
            origin == "smk" ||
            window.navigator.userAgent.indexOf("smkVersion") > -1
        ) {
            window.sessionStorage.setItem("hlw_remoteChannel", "health_smk_H5");
            return "health_smk_H5";
        } else if (this.isWeChat()) {
            window.sessionStorage.setItem(
                "hlw_remoteChannel",
                "health_wechat_hospital"
            );
            return "health_wechat_hospital";
        } else if (
            window.navigator.userAgent.toLowerCase().indexOf("alipay") > -1
        ) {
            console.log("-------alipayalipayalipayalipayalipayalipayalipay");
            if (origin == "wjAliMini") {
                window.sessionStorage.setItem("hlw_remoteChannel", "health_alipay_hospital_h5");
                return "health_alipay_hospital_h5";
            } else {
                window.sessionStorage.setItem("hlw_remoteChannel", "health_alipay_hospital_MINI");
                return "health_alipay_hospital_MINI";
            }
        } else if (origin) {
            window.sessionStorage.setItem("hlw_remoteChannel", origin);
            return origin;
        } else {
            return "all";
        }
    },
};

export default tools;
