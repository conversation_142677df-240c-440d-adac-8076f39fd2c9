<template>
    <div class="fwb block" v-if="fwbList && fwbList.length > 0">
        <p class="title">
            服务包套餐
            <!-- 只有一条的时候不显示更多 -->
            <span class="more" v-if="fwbList.length > 1" @click="showMore"
                >更多<i class="arr"></i
            ></span>
        </p>
        <div class="fwb-list">
            <!-- 只有一条的时候 宽度设为100% -->
            <div
                class="fwb-item"
                :style="{ width: fwbList.length == 1 ? '100%' : '' }"
                v-for="(it, index) in fwbList"
                :key="index"
            >
                <fwbItem
                    :item="it"
                    :unicode="unicode"
                    :deptId="deptId"
                    :doctorId="doctorId"
                />
            </div>
        </div>
    </div>
</template>
<script>
import common from "@/util/common";
import tools from "@/util/tools";
import { getfwbList } from "@/api/api";
import fwbItem from "./fwbItem.vue";
export default {
    components: { fwbItem },
    data() {
        return {
            fwbList: [],
        };
    },
    props: ["unicode", "deptId", "doctorId"],
    created() {},
    mounted() {},
    methods: {
        dencryptHeader: common.dencryptHeader,
        getData(hospOrgCode, unicode, deptId, doctorId) {
            let data = {
                hospOrgCode,
                unicode: unicode,
                deptId: deptId,
                doctorId: doctorId,
            };
            const that = this;
            // 获取服务包列表
            // api/v1/servicePack/list
            getfwbList(data).then((res) => {
                if (res && res.length) {
                    this.fwbList = res;
                }
            });
        },
        showMore() {
            this.$router.push({
                path: "/healthMallFwb",
                query: {
                    unicode: this.unicode,
                    deptId: this.deptId,
                    doctorId: this.doctorId,
                },
            });
        },
        jumpToPage(item) {
            console.log(item);
            let url =
                location.origin +
                location.pathname +
                `#/doctor?staffId=${item.staffId}&deptId=${item.deptId}&unicode=${item.unicode}`;
            item.status = "2";
            item.jumpUrl = url;
            tools.jumpUrlManage(item, 1);
        },
    },
};
</script>
<style lang="less" scoped>
.fwb {
    padding: 15px;

    .title {
        font-size: 18px;
        font-weight: 500;
        color: #000000;
        line-height: 1;
        margin-bottom: 15px;
        .more {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 13px;
            color: #999999;
            float: right;
            .arr {
                display: inline-block;
                background: url("@/images/img/arrow_gray.png") no-repeat top;
                width: 5px;
                height: 10px;
                background-size: 100%;
                margin-left: 4px;
            }
        }
    }

    &-list {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        flex-direction: row;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;

        .fwb-item {
            width: 289px;
            // height: 134px;
            flex-shrink: 0;
            margin-right: 0.1rem;
            margin-bottom: 0.05rem;
            background: linear-gradient(0deg, #e5fcfa 0%, #f0fbf5 100%);
            // url("@/images/fwb/bg.png") no-repeat top;
            border-radius: 8px;
            box-sizing: border-box;
            padding: 10px;
            position: relative;

            .bg {
                display: block;
                background: url("@/images/fwb/bg.png") no-repeat top;
                width: 34px;
                height: 28px;
                position: absolute;
                background-size: 100%;
                right: 40px;
                top: 6px;
            }
            .name {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #106450;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .cont {
                width: 100%;
                // height: 91px;
                background: #ffffff;
                border-radius: 8px;
                margin-top: 6px;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                .detail {
                    display: flex;
                    flex-wrap: nowrap;
                    width: 100%;
                    overflow: hidden;
                    .item {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                        // display: flex;
                        flex-direction: row;
                        align-items: center;
                        margin-right: 10px;
                        flex-shrink: 0;
                        margin: 8px 8px 0px 8px;
                        width: 98%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;

                        .dg {
                            display: inline-block;
                            background: url("@/images/fwb/dg.png") no-repeat
                                center;
                            width: 11px;
                            height: 11px;
                            background-size: 100%;
                            margin-right: 3px;
                        }
                        .it {
                            margin-right: 10px;
                            :last-child {
                                margin-right: 0;
                            }
                        }
                    }
                    :last-child {
                        margin-right: 0;
                    }
                }
                .gift {
                    width: 249px;
                    height: 19px;
                    line-height: 19px;
                    background: linear-gradient(
                        90deg,
                        #e5fcfa 0%,
                        #f4fffe 100%
                    );
                    border-radius: 2px;
                    margin-left: 8px;
                    width: 234px;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #3ebfa0;
                    padding: 0 3px;
                }
                .bt {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 8px;
                    // margin-bottom: 10px;

                    .price {
                        font-weight: 600;
                        font-size: 12px;
                        color: #fa541c;
                        line-height: 7px;
                    }
                    .xl {
                        font-weight: 400;
                        font-size: 12px;
                        color: #999999;
                        line-height: 5px;
                    }
                    .qg {
                        width: 73px;
                        height: 22px;
                        background: linear-gradient(
                            90deg,
                            #01cda7 0%,
                            #0fc2aa 99%
                        );
                        border-radius: 11px;
                        font-size: 13px;
                        color: #ffffff;
                    }
                }
            }
        }

        :last-child {
            margin-right: 0;
        }
    }
}
</style>