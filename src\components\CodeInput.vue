<template>
  <div class="code-input-wrapper">
    <div class="input-container">
      <input
        v-model="localVerifyCode"
        type="number"
        class="code-input"
        placeholder="请输入短信验证码"
        maxlength="6"
        @focus="onFocus"
        readonly
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'CodeInput',
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      localVerifyCode: this.value
    };
  },
  watch: {
    value(newVal) {
      this.localVerifyCode = newVal;
    },
    localVerifyCode(newVal) {
      // 确保验证码不超过6位
      if (newVal.length > 6) {
        this.localVerifyCode = newVal.slice(0, 6);
      } else {
        this.$emit('input', newVal);
      }
    }
  },
  methods: {
    // 输入框获得焦点
    onFocus() {
      this.$emit('focus');
    },
    
    // 清空验证码
    clearCode() {
      this.localVerifyCode = '';
      this.$emit('input', '');
    }
  }
};
</script>

<style scoped>
.code-input-wrapper {
  margin-bottom: 24px;
}

.input-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 318px;
  height: 50.5px;
  background-color: #F4F4F4;
  border-radius: 27.5px;
  padding: 0 20px;
  box-sizing: border-box;
}

.code-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  color: #333;
  background: transparent;
}

.code-input::placeholder {
  color: #999;
}
</style>