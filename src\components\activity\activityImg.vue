<!--
 * @Author: your name
 * @Date: 2025-05-28 09:30:57
 * @LastEditTime: 2025-05-28 14:56:10
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 活动页图片
 * @FilePath: \h5-interhosp\src\components\activity\activityImg.vue
-->
<template>
    <!-- 活动页图片 -->
    <!-- 子展台类别名称：活动页图片 -->
    <div v-show="isShow">
        <div
            class="Mod-item hdItem"
            v-for="(item, index) in commonlist"
            :key="index"
            @click="jumpUrl(item, childStageId, childStageName)"
        >
            <img v-lazy="item.iconUrl" alt="" />
        </div>
    </div>
</template>

<script>
import tools from "@/util/tools";
export default {
    name: "H5InterhospActivityImg",
    props: {
        datalists: {
            type: Object,
        },
        stageName: {
            type: String,
        },
        stageId: {
            type: String,
        },
    },
    data() {
        return {
            commonlist: "",
            elementId: "",
            isShow: false,
            childStageId: "",
            childStageName: "",
        };
    },

    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("活动页图片", temp);
        if (
            temp.stageTypeName === "活动页图片" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.commonlist = temp.childStageList;
            this.childStageId = temp.childStageId;
            this.childStageName = temp.childStageName;
        }
    },

    methods: {
        jumpUrl(item, id, name) {
            console.log("跳转参数", item);
            if (item.jumpUrl) {
                tools
                    .handleSetPoint({
                        stageId: this.stageId,
                        childStageId: id,
                        stageAppId: item.applicationId,
                        trackingContent: `${this.stageName}-${name}-${item.elementContent}`,
                        businessName: "activity",
                    })
                    .then(() => {
                        tools.jumpUrlManage(item);
                    });
            }
        },
    },
};
</script>
<style scoped lang="less">
.Mod-item {
    font-size: 0;
    text-align: center;
    position: relative;
    flex: 1;
    flex-shrink: 0;
}
.hdItem {
    width: 100%;
    img {
        width: 100%;
    }
}
</style>