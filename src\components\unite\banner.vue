<!--
 * @Author: your name
 * @Date: 2024-12-05 16:06:38
 * @LastEditTime: 2024-12-20 16:18:33
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: banner 无特殊联动，仅显示banner图
 * @FilePath: \h5-interhosp\src\components\unite\banner.vue
-->
<template>
    <div class="miyt" v-if="bannerlist.length !== 0">
        <div class="swiper">
            <van-swipe :autoplay="3000" indicator-color="#01CDA7">
                <van-swipe-item
                    v-for="(item, index) in bannerlist"
                    :key="index"
                >
                    <img
                        v-lazy="item.iconUrl"
                        :key="item.iconUrl"
                        alt=""
                        @click="go(item, index)"
                        :data-exposure-id="elementId"
                        :data-exposure-content="item.elementContent"
                        :data-exposure-sn="index + 1"
                    />
                </van-swipe-item>
            </van-swipe>
        </div>
    </div>
</template>
  <script>
import tools from "@/util/tools.js";
import Vue from "vue";
import { Swipe, SwipeItem } from "vant";
Vue.use(Swipe).use(SwipeItem);
export default {
    components: {
        "mt-swipe": Swipe,
        "mt-swipe-item": SwipeItem,
    },
    props: {
        datalists: {
            type: Object,
        },
    },
    data() {
        return {
            elementId: "",
            bannerlist: "",
        };
    },
    created() {},
    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        if (temp.stageTypeName === "banner") {
            this.elementId = temp.elementId;
            this.bannerlist = temp.childStageList;
        }
    },
    methods: {
        go(item, index) {
            // 将所属子展台的市民卡埋点id，赋值给点击的某项，在jumurl跳转方法里判断是否触发市民卡点击埋点
            item.elementId = this.elementId;
            item.index = index;
            tools.jumpUrlManage(item);
        },
    },
};
</script>
  <style scoped lang="less">
.miyt {
    width: 3.45rem;
    height: 0.875rem;
    margin: 0 auto;
    margin-bottom: 0.1rem;
    /deep/.swiper {
        width: 100%;
        height: 100%;
        border-radius: 0.08rem;
        overflow: hidden;
        // position: relative;
        // z-index: 1;
        transform: rotate(
            0deg
        ); // 上面的position和z-index的效果和这个一样，为了解决overflow:hidden失效问题
        img {
            width: 100%;
            height: 100%;
        }
        img[lazy="loading"] {
            display: block;
            width: 20%;
            line-height: 100%;
            margin: auto;
        }
        // .mint-swipe {
        //   border-radius: 0.08rem !important;
        // }
        .mint-swipe-indicators {
            bottom: 0;
        }
        .mint-swipe-indicator.is-active {
            background-color: #01cda7;
        }
        .van-swipe__indicator {
            opacity: 1;
            background-color: #e5e5e4;
            width: 0.06rem;
            height: 0.06rem;
        }
    }
}
</style>
  