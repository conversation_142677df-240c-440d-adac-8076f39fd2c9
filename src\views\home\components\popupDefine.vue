


<template>
    <div class="popup">
        <van-popup
            v-model="show"
            round
            :close-on-click-overlay="false"
            position="bottom"
            :style="{ height: '60%' }"
        >
            <div class="top">
                {{ data.title }}
                <img
                    @click="closePopup"
                    class="close"
                    src="@/images/img/popup_close.png"
                    alt=""
                />
            </div>
            <div class="content">
                <p class="subtitle" v-if="data.subTitle">
                    <span></span>{{ data.subTitle }}
                </p>
                <p class="intro">{{ data.intro }}</p>
            </div>
        </van-popup>
    </div>
</template>

<script>
import { Popup } from "vant";

export default {
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        data: {
            type: Object,
            default: () => {},
        },
    },
    components: {
        [Popup.name]: Popup,
    },
    data() {
        return {};
    },
    mounted() {},
    methods: {
        closePopup() {
            this.$emit("update:show", false);
        },
    },
};
</script>

<style lang='less' scoped>
.popup {
    .top {
        height: 46px;
        background-image: url("@/images/img/popup_top.png");
        background-size: 100% 100%;
        line-height: 46px;
        padding: 0 36px;
        text-align: center;
        font-size: 19px;
        font-family: "PingFang SC";
        font-weight: 500;
        color: #333333;
        position: relative;

        .close {
            width: 14px;
            height: 14px;
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    .content {
        height: calc(100% - 46px);
        overflow-y: scroll;
    }
    p {
        padding: 0 16px;
        font-family: "PingFang SC";
    }
    .subtitle {
        margin-top: 14px;
        margin-bottom: 16px;
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        display: flex;
        line-height: 1;
        span {
            width: 3px;
            height: 15px;
            background: #01cda7;
            border-radius: 1.5px;
            margin-right: 6px;
        }
    }
    .intro {
        font-size: 15px;
        font-weight: 400;
        color: #777777;
        line-height: 25px;
        margin-bottom: 30px;
    }
    ::v-deep .van-popup--bottom.van-popup--round {
        border-radius: 8px 8px 0 0;
    }
}
</style>
