<!--
 * @Author: your name
 * @Date: 2024-12-05 09:50:40
 * @LastEditTime: 2024-12-31 16:43:37
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 顶部四大金刚
 * @FilePath: \h5-interhosp\src\components\unite\topFour.vue
-->
<template>
    <div class="common_" v-show="isShow">
        <ul class="ul">
            <li v-for="(item, index) in commonlist" :key="index">
                <img
                    class="item_img_com"
                    @click="go(item, index)"
                    v-lazy="item.iconUrl"
                    :key="item.iconUrl"
                    :data-exposure-id="elementId"
                    :data-exposure-content="item.elementContent"
                    :data-exposure-sn="index"
                />
                <div>{{ item.applicationName }}</div>
            </li>
        </ul>
    </div>
</template>
    <script>
import tools from "@/util/tools.js";
export default {
    props: {
        datalists: {
            type: Object,
        },
    },
    data() {
        return {
            commonlist: "",
            elementId: "",
            isShow: false,
        };
    },
    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("四大金刚", temp);
        if (
            temp.stageTypeName === "四大金刚" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.commonlist = temp.childStageList;
        }
    },
    methods: {
        go(item, index) {
            item.elementId = this.elementId;
            item.index = index;
            tools.jumpUrlManage(item);
        },
    },
};
</script>
    <style scoped>
img[lazy="loading"] {
    display: block;
    width: 20%;
    line-height: 100%;
    margin: auto;
}
.common_ {
    width: 3.45rem;
    margin: 0 auto;
    /* background-color: white; */
    border-radius: 0.08rem 0.08rem 0 0;
}
.ul {
    margin: 0 auto;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-left: 0.1rem;
    margin-right: 0.1rem;
}
.ul li {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0.1rem 0 0.11rem 0;
    width: 0.8rem;
    color: #fff;
    font-size: 0.15rem;
}
.ul li img {
    margin-bottom: 0.04rem;
}
.item_img_com {
    width: 0.3rem;
    height: 0.27rem;
}
</style>
  
  