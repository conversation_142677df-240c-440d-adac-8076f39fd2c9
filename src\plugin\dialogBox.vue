<template>
  <div class="msgBoxWrap" v-if="visible">
    <div class="mask"></div>
    <div class="msgBox">
      <div class="msgBoxMain">
        <div class="msgBoxBody">
          <div class="msgBoxTitle" v-if="optionsData.title">
            <p>
              <span>{{ optionsData.title }}</span>
            </p>
          </div>
          <div class="msgBoxCont">
            <p>{{ optionsData.content }}</p>
          </div>
        </div>
        <div class="msgBoxBtn">
          <button
            type="button"
            v-if="optionsData.confirmTxt"
            class="confirm"
            @click="confirmFn"
          >
            {{ optionsData.confirmTxt }}
          </button>
          <button
            type="button"
            v-if="optionsData.cancelTxt"
            class="cancel"
            @click="cancelFn"
          >
            {{ optionsData.cancelTxt }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      optionsData: {
        title: "",
        content: "",
        confirmTxt: "确定",
        cancelTxt: "取消",
        confirmCallback: function () {
          console.log("plugin confirm");
        },
        cancelCallback: function () {
          console.log("plugin cancel");
        },
      },
    };
  },
  methods: {
    open() {
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    confirmFn() {
      this.optionsData.confirmCallback();
      this.close();
    },
    cancelFn() {
      this.optionsData.cancelCallback();
      this.close();
    },
    setData(data) {
      this.optionsData = Object.assign({}, this.optionsData, data);
    },
  },
};
</script>
<style lang="less" scoped>
.msgBoxWrap {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: transparent;
  z-index: 1000;
  .mask {
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.6;
  }
  .msgBox {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    width: calc(100% - 85px);
    min-height: 160px;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
  }

  .msgBoxMain {
    padding: 29px 0;
  }

  .msgBoxTitle {
    width: 100%;
    margin-bottom: 9px;

    p {
      text-align: center;
      font-size: 16px;
      color: #333;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .msgBoxCont {
    font-size: 13px;
    color: #666666;
    text-align: center;
    padding: 0 24px;
    p {
      span {
        // padding: 0 24px;
        display: inline-block;
        white-space: break-spaces;
        word-break: break-word;
      }
    }
  }

  .msgBoxBtn {
    margin-top: 30px;
    button {
      height: 40px;
      line-height: 40px;
      font-size: 15px;
      text-align: center;
      width: 185px;
      display: block;
      margin: 0 auto;
      background: #fff;
    }
    .confirm {
      font-weight: 500;
      color: #01cda7;
      border: 2px solid #01cda7;
      border-radius: 20px;
      margin-bottom: 8px;
    }
    .cancel {
      color: #333333;
    }
  }
}
</style>
