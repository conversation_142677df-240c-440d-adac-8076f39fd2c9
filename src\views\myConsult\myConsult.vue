<!--
 * @Author: shenpp
 * @Date: 2023-08-16
 * @LastEditTime: 2024-03-28 14:20:38
 * @Description: 我的咨询   区分电话咨询还是图文咨询
-->
<template>
    <div class="myConsult">
        <div
            :class="
                ['0', '1', '2', '3', '4'].indexOf(list.orderStatus) > -1
                    ? 'top_content'
                    : 'top_content defaultHeight'
            "
        >
            <div class="cont_">
                <div class="top_state">
                    <img
                        v-if="list.orderStatus == '0'"
                        class="img_"
                        src="./../../images/img/daizhifu.png"
                    />
                    <img
                        v-if="list.orderStatus == '1'"
                        class="img_"
                        src="./../../images/img/daijiedan.png"
                    />
                    <img
                        v-if="list.orderStatus == '2'"
                        class="img_"
                        src="./../../images/img/zixunzhong.png"
                    />
                    <img
                        v-if="list.orderStatus == '3'"
                        class="img_"
                        src="./../../images/img/daipingjia.png"
                    />
                    <img
                        v-if="list.orderStatus == '4'"
                        class="img_"
                        src="./../../images/img/yijieshu.png"
                    />
                    <img
                        v-if="
                            list.orderStatus == '51' ||
                            list.orderStatus == '52' ||
                            list.orderStatus == '53' ||
                            list.orderStatus == '54'
                        "
                        class="img_"
                        src="./../../images/img/yiquxiao.png"
                    />
                    <img
                        v-if="list.orderStatus == '6'"
                        class="img_"
                        src="./../../images/img/yijujue.png"
                    />
                    <img
                        v-if="list.orderStatus == '7'"
                        class="img_"
                        src="./../../images/img/yituihui.png"
                    />
                    <img
                        v-if="list.orderStatus == '8'"
                        class="img_"
                        src="./../../images/img/tuikuan.png"
                    />
                    <div class="state_">
                        <p class="state">
                            {{ list.orderStatus | orderStatus }}
                        </p>
                        <p v-if="list.orderStatus == '0'">
                            请您在15分钟内完成支付，超时将自动取消
                        </p>
                        <p
                            v-if="
                                list.orderStatus == '1' && list.busiType == 21
                            "
                        >
                            医生还未接收您的咨询订单，请耐心等待
                        </p>
                        <p
                            v-if="
                                list.orderStatus == '1' && list.busiType == 22
                            "
                        >
                            医生将在预约时间内给您回电，请注意接收
                        </p>
                        <p v-if="list.orderStatus == '2'">
                            医生是利用休息时间为您解答的，请耐心等待~
                        </p>
                        <p v-if="list.orderStatus == '6'">{{ list.reason }}</p>
                        <p v-if="list.orderStatus == '7'">{{ list.reason }}</p>
                    </div>
                </div>
                <div class="hospContnet">
                    <img src="./../../images/img/hosp.png" alt="" />
                    <div class="hospName">{{ list.hospname }}</div>
                </div>
                <div class="docInfo" @click="goDoctor">
                    <div class="docImg">
                        <img
                            :src="
                                list.photo
                                    ? list.photo
                                    : require('@/images/img/default_head.png')
                            "
                            alt=""
                        />
                    </div>
                    <div class="rightContent">
                        <div class="docName">{{ list.doctorName }}</div>
                        <div class="deptInfo">
                            <div v-if="list.isTeamOrder != 1">
                                {{ list.doctorTitleName }}
                            </div>
                            <div>{{ list.deptName }}</div>
                        </div>
                    </div>
                </div>
                <patient-desc :data.sync="list"></patient-desc>
                <div
                    class="firstPhoneTime"
                    v-if="
                        ['3', '4'].indexOf(list.orderStatus) > -1 &&
                        list.busiType == 22
                    "
                >
                    <div class="left">来电时间</div>
                    <div class="right">{{ list.firstPhoneTime }}</div>
                </div>
                <order-list :data.sync="list"></order-list>
            </div>
        </div>
        <div
            v-if="
                list.orderStatus == '0' ||
                list.orderStatus == '1' ||
                list.orderStatus == '2' ||
                list.orderStatus == '3' ||
                (list.orderStatus == '4' && list.busiType != 22)
            "
            class="bottom_btn"
        >
            <div
                @click="finishCancel()"
                v-if="list.orderStatus == '2' && list.busiType != 22"
            >
                结束咨询
            </div>
            <div @click.stop="toEvaluation" v-if="list.orderStatus == '3'">
                去评价
            </div>
            <div
                @click="finishCancel()"
                v-if="list.orderStatus == '0' || list.orderStatus == '1'"
            >
                取消咨询
            </div>
            <div
                style="border: 1px solid #e8e9ec; margin-left: 0.1rem"
                @click.stop="reConsultation"
                v-if="
                    (list.orderStatus == '4' || list.orderStatus == '3') &&
                    list.busiType != 22 &&
                    list.isTeamOrder == 0
                "
            >
                再次咨询
            </div>
            <div
                @click="toConsult"
                v-if="
                    (list.orderStatus == '1' &&
                        list.isTeamOrder == 0 &&
                        list.busiType != 22) ||
                    (list.orderStatus == '2' && list.busiType == 21) ||
                    (list.orderStatus == '3' && list.busiType != 22)
                "
            >
                进入会话
            </div>
            <div @click="toPayOrder" v-if="list.orderStatus == '0'">
                立即支付
            </div>
        </div>
    </div>
</template>

<script>
import patientDesc from "./components/patientDesc.vue";
import orderList from "./components/orderList.vue";
import { query, finish, cancel } from "@/api/consult.js";
import common from "@/util/common.js";
import { confirmOrder, submitOrderAgainCheck } from "@/api/consult";
import util from "@/util/util.js";
import tools from "@/util/tools";
import { Dialog } from "vant";
export default {
    components: {
        patientDesc,
        orderList,
    },
    data() {
        return {
            list: {},
            closeInfo: {},
            callModalShow: false,
        };
    },
    filters: {
        orderStatus(value) {
            let status = "";
            switch (value) {
                case "0":
                    status = "待支付";
                    break;
                case "1":
                    status = "待接单";
                    break;
                case "2":
                    status = "咨询中";
                    break;
                case "3":
                    status = "待评价";
                    break;
                case "4":
                    status = "已结束";
                    break;
                case "51":
                    status = "已取消(超时未接单)";
                    break;
                case "52":
                    status = "已取消(用户支付后取消)";
                    break;
                case "53":
                    status = "已取消(超时未支付)";
                    break;
                case "54":
                    status = "已取消(用户未支付取消)";
                    break;
                case "6":
                    status = "已拒绝";
                    break;
                case "7":
                    status = "已退回";
                    break;
                case "8":
                    status = "退款成功";
                    break;
                default:
                    status = "其他";
                    break;
            }
            return status;
        },
    },
    mounted() {
        this.query();
    },

    methods: {
        dencryptHeader: common.dencryptHeader,
        query() {
            const data = {
                orderNo: this.$route.query.orderNo,
            };
            query(data).then((res) => {
                this.list = res;
            });
        },
        finishCancel() {
            const that = this;
            this.$dialogBox({
                title: this.list.orderStatus == "2" ? "结束咨询" : "取消咨询",
                content:
                    this.list.orderStatus == "2"
                        ? "我的问题已解决"
                        : this.list.orderStatus == "1"
                        ? "一天内取消次数不超过3次"
                        : "",
                confirmTxt: this.list.orderStatus == "2" ? "结束" : "确认取消",
                cancelTxt: "再问问",
                cancelCallback: function () {},
                confirmCallback: function () {
                    that.finishConsult();
                },
            });
        },
        finishConsult() {
            this.checkOrderStatus((status) => {
                const data = {
                    orderNo: this.list.orderNo,
                };
                if (status == "2") {
                    finish(data).then(() => {
                        this.$router.push({
                            path: "/consultEvaluation",
                            query: {
                                orderNo: this.list.orderNo,
                            },
                        });
                    });
                } else if (status == "0" || status == "1") {
                    cancel(data).then((res) => {
                        if (res) {
                            this.query();
                        }
                    });
                } else {
                    util.showToast("该订单状态已变更");
                    this.query();
                }
            });
        },
        toEvaluation() {
            this.$router.push({
                path: "/consultEvaluation",
                query: {
                    orderNo: this.list.orderNo,
                },
            });
        },
        goDoctor() {
            let url =
                location.origin +
                location.pathname +
                `#/doctor?staffId=${
                    this.list.teamStaffId || this.list.staffId
                }&deptId=${this.list.deptId}&unicode=${this.list.unicode}`;
            tools.jumpUrl(url, "2");
        },
        reConsultation() {
            submitOrderAgainCheck({
                orderInfoId: this.list.orderInfoId,
            }).then((res) => {
                if (res.success !== 1) {
                    Dialog.alert({
                        message: res.respDesc,
                        confirmButtonColor: "#00CCA6",
                        confirmButtonText: "好的",
                        className: "checkDialog",
                    });
                } else {
                    if (res.value && res.value.orderNo) {
                        const token = window.localStorage.getItem("token");
                        const IMId = this.list.imId;
                        let originStr =
                            location.hostname.indexOf("10.100.10") > -1
                                ? "https://jsbceshi.hfi-health.com:18188"
                                : location.origin;
                        const url = `${originStr}/${process.env.VUE_APP_TALK}/#/chat-detail?id=${IMId}&userType=2&token=${token}&orderNo=${res.value.orderNo}`;
                        window.location.href = url;
                        return;
                    }
                    let url =
                        location.origin +
                        location.pathname +
                        `#/consultService?module=imageConsultApply&staffId=${this.list.staffId}&deptId=${this.list.deptId}&unicode=${this.list.unicode}&latestOrderNo=${this.list.orderNo}`;
                    tools.jumpUrl(url, "2");
                }
            });
        },
        toConsult() {
            const uInfo =
                JSON.parse(window.localStorage.getItem("userInfo")) || {};
            //   const token = uInfo.token
            const token = window.localStorage.getItem("token");
            // const IMId = this.$md5(uInfo.paperNo);
            const IMId = this.list.imId;
            const orderNo = this.list.orderNo;
            // const certNum = this.list.certNum;
            // const doctorCertNum = this.list.doctorCertNum;
            let originStr =
                location.hostname.indexOf("10.100.10") > -1
                    ? "https://jsbceshi.hfi-health.com:18188"
                    : location.origin;
            const url = `${originStr}/${process.env.VUE_APP_TALK}/#/chat-detail?id=${IMId}&userType=2&token=${token}&orderNo=${orderNo}`;
            console.log("会话地址:", url);
            window.location.href = url;
        },
        toPayOrder() {
            console.log(this.list);
            this.checkOrderStatus((status) => {
                // 待支付状态
                if (status == 0) {
                    let logTraceId = new Date().getTime();
                    // let merchantId = "**********"; // 测试数据
                    //   let alipayUserId = this.$store.state.alipayUserId
                    //     ? this.$store.state.alipayUserId
                    //     : window.localStorage.getItem("alipayUserId");
                    let alipayUserId =
                        window.localStorage.getItem("alipayUserId");
                    // alipayUserId = "2088702714255878";
                    let remark2Obj = {};
                    if (navigator.userAgent.indexOf("AliApp") > -1) {
                        remark2Obj = {
                            openid: alipayUserId,
                        };
                    } else if (
                        navigator.userAgent
                            .toLowerCase()
                            .indexOf("micromessenger") > -1
                    ) {
                        remark2Obj = {
                            openid: localStorage.getItem("wxOpenid"),
                            appid: window.localStorage.getItem("wxAppid"),
                        };
                    }

                    console.log("***", remark2Obj);
                    let backurl =
                        location.origin +
                        location.pathname +
                        "#/myConsult?orderNo=" +
                        this.list.orderNo;

                    let data = {
                        orderNo: this.list.orderNo,
                        merchantId: this.list.merchantId,
                        unicode: this.list.unicode,
                        amount: this.list.totalAmount + "",
                        logTraceID: logTraceId,
                        callBackUrl:
                            location.origin +
                            location.pathname +
                            "#/myConsult?orderNo=" +
                            this.list.orderNo,
                        remark2: JSON.stringify(remark2Obj), //str
                    };
                    console.log("确认订单传参----", data);
                    confirmOrder(data).then((res) => {
                        if (res) {
                            if (
                                window.localStorage.getItem("localId") &&
                                this.list.busiType == "21"
                            ) {
                                // 图文咨询和有localid的情况下，给收银台传localid进行收银台埋点
                                window.location.href = `${
                                    res.cashierUrl
                                }&localid=${window.localStorage.getItem(
                                    "localId"
                                )}`;
                            } else {
                                window.location.href = res.cashierUrl;
                            }
                        }
                    });
                } else {
                    util.showToast("该订单已超时未支付，请重新咨询");
                    this.query();
                }
            });
        },
        checkOrderStatus(callback) {
            const data = {
                orderNo: this.$route.query.orderNo,
            };
            query(data).then((res) => {
                callback(res.orderStatus);
            });
        },
    },
};
</script>

<style lang="less" scoped>
.myConsult {
    background-image: url("./../../images/img/background.png");
    background-size: 100% 1.11rem;
    background-repeat: no-repeat;
    padding-top: 0.2rem;
    background-color: #f6f6f6;
}

.top_content {
    height: 85vh;
    // overflow-y: scroll;
    margin-left: 0.15rem;
    margin-right: 0.15rem;
    margin-bottom: 0.1rem;

    .top_state {
        display: flex;
        align-items: center;
    }

    .cont_ {
        height: 100%;
        overflow-y: scroll;
    }
}
.defaultHeight {
    height: 95vh;
}

.img_ {
    width: 0.35rem;
    height: 0.35rem;
    // 图片与span对齐
    // vertical-align: top;
}

.state_ {
    margin-left: 0.16rem;

    p {
        color: #fff;
        font-size: 0.12rem;
    }

    .state {
        font-size: 0.23rem;
        color: #ffffff;
        font-weight: bold;
        margin-bottom: 0.05rem;
    }
}

.hospContnet {
    background-color: #fff;
    border-radius: 0.08rem;
    margin-top: 0.24rem;
    padding: 0.2rem 0.15rem;
    display: flex;
    align-items: center;

    img {
        display: block;
        width: 0.165rem;
        height: 0.16rem;
    }

    .hospName {
        font-size: 0.16rem;
        font-weight: bold;
        color: #333333;
        margin-left: 0.095rem;
    }
}

.docInfo {
    background-color: #ffffff;
    margin-top: 0.12rem;
    border-radius: 0.08rem;
    padding: 0.2rem 0.15rem;
    display: flex;

    img {
        display: block;
        width: 0.6rem;
        height: 0.6rem;
        border-radius: 50%;
    }

    .rightContent {
        margin-left: 0.15rem;
        display: flex;
        flex-direction: column;
        justify-content: space-around;

        .docName {
            font-size: 0.21rem;
            font-weight: bold;
            color: #333333;
        }

        .deptInfo {
            display: flex;
            font-size: 0.14rem;
            font-weight: 400;
            color: #888888;

            div {
                margin-right: 0.1rem;
            }
        }
    }
}
.firstPhoneTime {
    background-color: #ffffff;
    margin-top: 0.12rem;
    border-radius: 0.08rem;
    padding: 0.2rem 0.15rem;
    display: flex;
    justify-content: space-between;
}

.notes {
    text-align: center;
    color: #fe963a;
    font-size: 0.14rem;
    margin-top: 0.15rem;
}

.bottom_btn {
    background-color: #fff;
    height: 0.64rem;
    margin-top: 0.15rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    div {
        width: 1.28rem;
        height: 0.4rem;
        line-height: 0.4rem;
        text-align: center;
        border-radius: 0.2rem;
    }

    div:first-child {
        border: 1px solid #e8e9ec;
    }

    div:last-child {
        background-color: #01cda7;
        color: #fff;
        margin-left: 0.1rem;
        margin-right: 0.15rem;
    }
}
</style>
