<template>
  <div class="scontent">
    <van-swipe
      vertical
      :autoplay="3000"
      :show-indicators="false"
      :touchable="false"
      :style="'height:58px;'"
    >
      <van-swipe-item v-for="(item, index) in list" :key="index">
        <div class="flexbtnc" @click="jumpUrl(item)">
          <div class="info">
            <img src="@/images/img/home_msg.png" alt="" />
            <span>{{ item.text }}</span>
          </div>
          <div class="time">
            <span>{{ item.time }}</span>
            <span class="dot"></span>
            <van-icon name="arrow" color="#333333" />
          </div>
        </div>
      </van-swipe-item>
    </van-swipe>
  </div>
</template>

<script>
import Vue from "vue";
import { Swipe, SwipeItem } from "vant";
Vue.use(Swipe).use(SwipeItem);
import tools from "@/util/tools";

export default {
  name: "<PERSON>rollver",
  data() {
    return {
      list: [
        {
          text: "挂号预约成功，请及时到医就诊1",
          time: "14:06",
        },
        {
          text: "挂号预约成功，挂号预约成功挂号预约成功请及时到医就诊2",
          time: "18:06",
        },
      ],
    };
  },
  mounted() {},
  methods: {
    jumpUrl(a, b) {
      tools.handleSetPoint({
        stageId: this.stageId,
        childStageId: this.childStageId,
        stageAppId: a.applicationId,
        trackingContent: a.applicationName,
      });
      console.log("参数", a, b);
      // tools.jumpUrlManage(a, b);
    },
  },
};
</script>

<style lang="less" scoped>
.scontent {
  margin-bottom: -5px;
  .flexbtnc {
    height: 90%;
    display: flex;
    align-content: center;
    justify-content: space-between;
    padding: 0 10px;
    font-size: 12px;
    color: #333333;
    background: #fff;
    border-radius: 8px;
    margin: 0 15px;
    .info {
      display: flex;
      align-items: center;
      font-weight: 500;
      img {
        margin-right: 14px;
        width: 30px;
        height: 23px;
        background: 100% auto;
      }
    }
    .dot {
      width: 4px;
      height: 4px;
      background: #fc0303;
      border-radius: 50%;
    }
    .time {
      display: flex;
      align-items: center;
      margin-left: 10px;
      span {
        margin-right: 6px;
      }
    }
  }
}
</style>
