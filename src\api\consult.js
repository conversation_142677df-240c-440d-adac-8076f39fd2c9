import fetch from "./http";
import util from "../util/util";

//在线咨询 提交订单
export const orderSubmit = (data) => {
    return fetch(
        "/inthos/internetHospital/submitOrder",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            return response;
        })
        .catch((err) => {
            return "";
        });
};
// 查询订单信息
export const consultOrderQuery = (data) => {
    return fetch("/inthos/internetHospital/query", JSON.stringify(data), "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
// 确认订单
export const confirmOrder = (data) => {
    return fetch(
        "/inthos/internetHospital/confirmOrder",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
// 计算优惠券
// /Coupons/calPrice
export const discount = (data) => {
    return fetch("/inthos/Coupons/calPrice", JSON.stringify(data), "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

//身份证查询关联亲情账号（未成年人+老年人）
export const getLinkFamilyList = (data) => {
    return fetch("/hzAppMS/Core/getLinkFamilyList", JSON.stringify(data), "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

//我的咨询-列表
export const queryList = (data) => {
    return fetch(
        "/inthos/internetHospital/queryList",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
//我的咨询-咨询详情
export const query = (data) => {
    return fetch("/inthos/internetHospital/query", JSON.stringify(data), "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
//我的咨询-结束咨询
export const finish = (data) => {
    return fetch("/inthos/internetHospital/finish", JSON.stringify(data), "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
//我的咨询-取消咨询
export const cancel = (data) => {
    return fetch("/inthos/internetHospital/cancel", JSON.stringify(data), "post")
        .then((response) => {
            console.log(response);
            if (response.success == 1) {
                return true;
                // 特殊判断
            } else if (
                response.success == 0 &&
                (response.respCode == "-6001" || response.respCode == "-6003")
            ) {
                setTimeout(() => {
                    util.showToast(response.respDesc);
                }, 50);
                return false;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
//我的咨询-满意度评价
export const orderEvaluate = (data) => {
    return fetch(
        "/inthos/internetHospital/orderEvaluate",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return true;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
                return false;
            }
        })
        .catch((err) => { });
};
//我的咨询-退款售后列表
export const afterSaleList = (data) => {
    return fetch(
        "/inthos/internetHospital/afterSale/List",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
//我的咨询-退款售后详情
export const afterSaledetail = (data) => {
    return fetch(
        "/inthos/internetHospital/afterSale/detail",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 获取机构服务须知弹窗配置内容
export const appQueryPatientNoticePop = (data) => {
    return fetch(
        "/inthos/internetHospital/appQueryPatientNoticePop",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                // let message = response.respDesc || "网络连接超时，请稍后再试~";
                // util.showToast(message);
                return "";
            }
        })
        .catch((err) => {
            return "";
        });
};
// 获取机构服务描述配置内容
export const appQueryOrgDescSetting = (data) => {
    return fetch(
        "/inthos/internetHospital/appQueryOrgDescSetting",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                // let message = response.respDesc || "网络连接超时，请稍后再试~";
                // util.showToast(message);
                return "";
            }
        })
        .catch((err) => {
            return "";
        });
};

export const getPatientImUserID = (id) => {
    return fetch("/inthos/internetHospital/patientImUserID", { id }, "get").then(
        (response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
                return "";
            }
        }
    );
};
// 获取预约时间列表
export const getAppointTime = (data) => {
    return fetch("/inthos/internetHospital/phone/getAppointTime", data, "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                // let message = response.respDesc || "网络连接超时，请稍后再试~";
                // util.showToast(message);
                return "";
            }
        })
        .catch((err) => { });
};

// /internetHospital/openPeriodOfDayByDoctor 查询医生的接诊时间

export const getReceptionTime = (data) => {
    return fetch("/inthos/internetHospital/openPeriodOfDayByDoctor", data, "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 再下一单前置校验
export const submitOrderAgainCheck = (data) => {
    return fetch("/inthos/internetHospital/submitOrderAgainCheck", data, "post")
        .then((response) => {
            if (response.success != 9999) {
                return response;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};