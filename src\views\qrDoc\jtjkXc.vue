<!--
 * @Author: your name
 * @Date: 2025-01-15 10:03:38
 * @LastEditTime: 2025-01-16 09:40:43
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: hlw/jtjk.html的跳转页面
 * @FilePath: \h5-interhosp\src\views\qrDoc\jtjkXc.vue
-->
<template>
    <div class="bg" :class="{ cou: activeId && code }">
        <div
            style="
                position: absolute;
                width: 100%;
                bottom: 0.2rem;
                min-height: 2.9rem;
            "
        >
            <!-- <div class="mb" v-if="!(activeId && code)"></div> -->
            <div class="btn" v-show="inWechat && btnShow"></div>
        </div>

        <div v-show="inWechat && btnShow">
            <wx-open-launch-weapp
                class="btn_"
                id="launch-btn"
                appid="wx040fa6c3585a0bfc"
                username="gh_939d526d2d2e"
                path="lib/shop/dist/pages/index/index"
                :env-version="env"
            >
                <script type="text/wxtag-template">
                    <style>
                        .btn{
                            width:55px;
                            height:56px;
                            border:none;
                            background: transparent;
                        }

                    </style>
                    <button class="btn"></button>
                </script>
            </wx-open-launch-weapp>
        </div>
    </div>
</template>

<script>
import common from "@/util/common.js";
import util from "@/util/util.js";
import { wxInit } from "@/api/api";
export default {
    components: {},
    data() {
        return {
            inWechat: false,
            inAli: false,
            btnShow: false,
            prId: "",
            url: "",
            // 优惠券
            activeId: "",
            code: "",
            env: process.env.VUE_APP_WECHART_VERSION || "release",
        };
    },

    mounted() {
        document.title = "1号润肤霜";
        if (!common.getUrlParam("prId")) {
            util.openDialogAlert("", "缺少必要参数，请检查链接是否正确~");
            return;
        }
        this.prId = decodeURIComponent(common.getUrlParam("prId"));
        if (
            navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 ||
            navigator.userAgent.indexOf("AliApp") > -1
        ) {
            // 微信、支付宝中
            this.initRrlData();
        } else {
            // 暂时不做
            util.openDialogAlert("", "请使用微信扫码");
        }
    },

    methods: {
        initRrlData(url) {
            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                // 微信中
                this.inWechat = true;
                this.initWechat();
            } else {
                // 暂时不做
                util.openDialogAlert("", "请使用微信扫码");
            }
        },
        async initWechat() {
            await wxInit();
            let that = this;
            wx.ready(function () {
                console.log("ready·········");
                wx.checkJsApi({
                    jsApiList: ["wx-open-launch-app"], // 需要检测的JS接口列表，所有JS接口列表见附录2,
                    success: (res) => {
                        console.log("可用········");
                        var btn = document.querySelector("#launch-btn");
                        btn.setAttribute(
                            "path",
                            that.url
                            // "/pages/wxIndex/wxIndex?authStatus=1&returnURL=https%3A%2F%2Fzjshlwyy.zjjgpt.com%2Fweixin%2Fthird%2FjinTouApp2%2Fauth%3Fmodule%3DsingleDoctIndex%26did%3D182680%26organId%3D2000006%26source%3DjinTou-zfb-sy%26uInfo%3D"
                        );
                        btn.addEventListener("launch", function (e) {
                            console.log("success········");
                            // openTip(1000);
                        });
                        btn.addEventListener("error", function (e) {
                            console.log("fail········", e.detail);
                            // openTip(100);
                        });
                        that.btnShow = true;
                    },
                    fail: (err) => {
                        console.log(err, "不可用·····");
                        // openTip(100);
                        // document.querySelector("#btn-other").style.display = "";
                    },
                });
            });
            wx.error(function (err) {
                console.log(err);
            });
        },
    },
};
</script>

<style lang="less" scoped>
.bg {
    background: url("./../../images/jtjkxc/jtjk_xpmBJ.png") no-repeat top center;
    background-size: cover;
    width: 100%;
    height: 100%;
    min-height: 6.67rem;
    float: left;
    position: relative;
}
.btn {
    background: url("./../../images/jtjkxc/jtjk_xpmGM.png") no-repeat top center;
    background-size: 100%;
    width: 0.55rem;
    height: 0.56rem;
    position: fixed;
    left: 1.6rem;
    top: 2rem;
}
.btn_ {
    position: absolute;
    left: 1.57rem;
    top: 1.98rem;
    width: 0.55rem;
    height: 0.56rem;
    overflow: hidden;
    z-index: 111;
}
</style>
