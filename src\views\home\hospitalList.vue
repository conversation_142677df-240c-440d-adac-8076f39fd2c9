<template>
    <div class="content">
        <div class="dropdown">
            <van-dropdown-menu :active-color="$store.state.primaryColor">
                <div>
                    <van-dropdown-item
                        v-model="areaVal"
                        :options="optionArea"
                        @change="getHospListFun"
                        @open="openVanDropdown"
                        ref="item"
                    />
                    <!-- <van-dropdown-item title="筛选" ref="item" class="define">
              <div class="level-cont">
                <p class="level-desc">医院等级</p>
                <van-radio-group v-model="resultL" direction="horizontal">
                  <van-radio
                    v-for="(item, index) in optionHospitalLevel"
                    :name="item.value"
                    :key="index"
                    :class="resultL == item.value ? 'active' : ''"
                  >
                    {{ item.text }}
                  </van-radio>
                </van-radio-group>
              </div>
              <div class="flex">
                <div class="btn default" @click="onReset">重置</div>
                <div class="btn info" @click="onConfirm">确定</div>
              </div>
            </van-dropdown-item> -->
                </div>
            </van-dropdown-menu>
            <div class="divider"></div>
            <!-- <van-dropdown-menu :active-color="$store.state.primaryColor">
          <div>
            <van-dropdown-item
              title="距离"
              v-model="distanceVal"
              :options="optionDistance"
              @change="filterDistanceHospList"
              @open="openVanDropdown"
              ref="item"
            />
          </div>
        </van-dropdown-menu> -->
            <div class="define-dropdown-menu">
                <div @click="defineDropdownClick">
                    <span>筛选</span>
                    <van-icon name="filter-o" />
                </div>
                <div
                    class="define-dropdown-item"
                    v-if="show"
                    :style="{ top: popTop + 'px' }"
                >
                    <van-popup
                        v-model="show"
                        position="top"
                        get-container=".define-dropdown-item"
                    >
                        <div>
                            <div class="level-cont">
                                <p class="level-desc">医院等级</p>
                                <van-radio-group
                                    v-model="resultL"
                                    direction="horizontal"
                                >
                                    <van-radio
                                        v-for="(
                                            item, index
                                        ) in optionHospitalType"
                                        :name="item.value"
                                        :key="index"
                                        :class="
                                            resultL == item.value
                                                ? 'active'
                                                : ''
                                        "
                                    >
                                        {{ item.text }}
                                    </van-radio>
                                </van-radio-group>
                            </div>
                            <div class="flex">
                                <div class="btn default" @click="onReset">
                                    重置
                                </div>
                                <div class="btn info" @click="onConfirm">
                                    确定
                                </div>
                            </div>
                        </div>
                    </van-popup>
                </div>
            </div>
        </div>
        <div class="hospList">
            <div class="hospList-inner" v-if="hospList && hospList.length > 0">
                <div v-for="(item, index) in hospList" :key="index">
                    <hospital-item :data="item"></hospital-item>
                </div>
            </div>
            <van-empty v-else :image="emptyImg" description="暂无搜索结果" />
        </div>
    </div>
</template>
  
  <script>
import Vue from "vue";
import {
    DropdownMenu,
    DropdownItem,
    RadioGroup,
    Radio,
    Button,
    Empty,
    Popup,
    Icon,
} from "vant";
Vue.use(DropdownMenu)
    .use(DropdownItem)
    .use(RadioGroup)
    .use(Radio)
    .use(Button)
    .use(Empty)
    .use(Popup)
    .use(Icon);
import HospitalItem from "../home/<USER>/hospitalItem.vue";
import {
    optionArea,
    optionHospitalLevel,
    optionHospitalType,
} from "@/util/dict.js";
import { getHospList } from "../../api/api";
export default {
    name: "StageItem",
    props: {
        data: {
            type: Object,
            default: () => {},
        },
    },
    components: {
        HospitalItem,
    },
    data() {
        return {
            areaVal: "",
            optionArea,
            optionHospitalLevel,
            optionHospitalType,
            resultL: "",
            longitude: "",
            latitude: "",
            hospList: [],
            show: false,
            popTop: "",
            distanceVal: "",
            emptyImg: require("@/images/search/no-data.png"),
            optionDistance: [
                {
                    text: "离我最近",
                    value: "00",
                },
                {
                    text: "距离我1km",
                    value: "1",
                },
                {
                    text: "距离我2km",
                    value: "2",
                },
                {
                    text: "距离我5km",
                    value: "5",
                },
            ],
        };
    },
    created() {
        this.getHospListFun();
    },
    mounted() {},
    methods: {
        getDistance(lat1, lng1) {
            let lat2 = this.$store.state.curPosition.latitude;
            let lng2 = this.$store.state.curPosition.longitude;
            if (lat1 && lng1 && lat2 && lng2) {
                var radLat1 = (lat1 * Math.PI) / 180.0;
                var radLat2 = (lat2 * Math.PI) / 180.0;
                var a = radLat1 - radLat2;
                var b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
                var s =
                    2 *
                    Math.asin(
                        Math.sqrt(
                            Math.pow(Math.sin(a / 2), 2) +
                                Math.cos(radLat1) *
                                    Math.cos(radLat2) *
                                    Math.pow(Math.sin(b / 2), 2)
                        )
                    );
                s = s * 6378.137; // EARTH_RADIUS;
                s = Math.round(s * 10000) / 10000;
                return s.toFixed(1);
            } else {
                return "";
            }
        },
        async oprHosList(list) {
            /* if (!this.$store.state.curPosition) {
          // this.$store.dispatch("getLocation");
          
        } */
            await this.$store.dispatch("getLocation");
            let distaceList = [];
            let noDisList = [];
            for (let i = 0; i < list.length; i++) {
                let ele = list[i];
                ele.distance = this.getDistance(ele.latitude, ele.longitude);
                if (ele.distance) {
                    distaceList.push(ele);
                } else {
                    noDisList.push(ele);
                }
            }
            if (this.resultL == "00") {
                this.hospList = distaceList
                    .sort(this.compare("distance"))
                    .concat(noDisList);
            } else {
                this.hospList = distaceList.concat(noDisList);
            }
            console.log("********", this.hospList);
            // let listcomp = distaceList.concat(noDisList);
            // console.log(this.distanceVal);
            // this.hospList = listcomp.filter((item) => {
            //   if (this.distanceVal == "00") {
            //     return item.distance <= 0.5;
            //   } else if (this.distanceVal == "1") {
            //     return item.distance > 0.5 && item.distance <= 1;
            //   } else if (this.distanceVal == "2") {
            //     return item.distance > 1 && item.distance <= 2;
            //   } else if (this.distanceVal == "5") {
            //     return item.distance > 2 && item.distance <= 5;
            //   }
            // });
        },
        compare(property) {
            return function (a, b) {
                var value1 = a[property];
                var value2 = b[property];
                return value1 - value2;
            };
        },
        filterAreaHospList() {
            this.distanceVal = "";
            this.getHospListFun();
        },
        filterDistanceHospList() {
            this.areaVal = "";
            this.getHospListFun();
        },
        openVanDropdown() {
            this.show = false;
        },
        onReset() {
            this.resultL = "";
        },
        onConfirm() {
            this.show = false;
            this.getHospListFun();
        },
        defineDropdownClick() {
            this.popTop = document.querySelector(
                ".van-dropdown-menu__item"
            ).clientHeight;
            this.show = true;
        },
        getHospListFun() {
            console.log(this.areaVal);
            console.log(this.resultL);
            let channel = "6";
            if (
                window.localStorage.getItem("interHosp_origin") == "jktwxMini"
            ) {
                // 在健康通微信小程序内
                channel = 4;
                console.log("4444");
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_hospital_aksu_mini"
            ) {
                // 阿克苏
                channel = 9;
            } else if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                channel =
                    sessionStorage.getItem("hlw_remoteChannel") == "xhmhMini"
                        ? "10"
                        : "5";
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_zheliban_H5"
            ) {
                channel = 3;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                    "health_smk_H5" ||
                window.navigator.userAgent.indexOf("smkVersion") > -1
            ) {
                // 市民卡
                channel = 8;
            }
            let data = {
                businessPlatform: channel,
                hospId: "",
                hospName: "",
                hospTypeName: "",
                locationCode: this.areaVal,
                hospLevel: "", //医院等级：三级甲等
                hospTypeCode: this.resultL == "00" ? "" : this.resultL,
                platform: "",
                todayFlag: "",
                internetFlag: "1",
                pageNum: 1,
                pageSize: 500,
            };
            getHospList(data).then((r) => {
                // this.hospList = r;
                if (r) {
                    if (this.resultL == "00") {
                        this.oprHosList(r);
                    } else {
                        // this.hospList = r;
                        let otherHosp = [];
                        let shHosp = [];
                        r.forEach((item) => {
                            if (item.hospTypeCode == "3") {
                                shHosp.push(item);
                            } else {
                                otherHosp.push(item);
                            }
                        });
                        let listH = shHosp.concat(otherHosp);
                        this.oprHosList(listH);
                    }
                }
            });
        },
    },
};
</script>
  
  <style lang="less" scoped>
.content {
    height: 100%;
    overflow: hidden;
}
.hospList {
    padding: 12px 16px;
    background: #ececec;
    height: calc(100vh - 50px);
    overflow: scroll;
}

.hospList-inner {
    /* background: #ffffff; */
    border-radius: 8px;
    margin-bottom: 20px;
}

.level-cont {
    padding: 20px 16px;
    padding-right: 6px;
}

.level-desc {
    font-size: 12px;
    font-family: "PingFang SC";
    font-weight: 400;
    color: #657085;
    margin-bottom: 12px;
}

.flex {
    padding: 10px 16px;
    justify-content: space-between;
    border-top: 1px solid #f1f1f1;
}

.btn {
    font-size: 15px;
    font-family: "PingFang SC";
    font-weight: 400;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
    text-align: center;
}

.default {
    width: 143px;
    border: 1px solid #d0d4dd;
    color: #333;
}

.info {
    width: 190px;
    background: #3ebfa0;
    /* background: var(--primary-color); */
    color: #ffffff;
}
::v-deep .van-dropdown-menu {
    width: 50%;
}
::v-deep .van-dropdown-menu__bar {
    box-shadow: none;
}
::v-deep .van-dropdown-menu__title {
    color: #333;
    font-size: 14px;
}
::v-deep .van-dropdown-menu__title::after {
    border-color: transparent transparent #999 #999;
}
::v-deep .van-dropdown-menu__title--active::after {
    border-color: transparent transparent currentColor currentColor;
}

::v-deep .van-dropdown-item__content {
    border-radius: 0 0 10px 10px;
}
::v-deep .van-radio--horizontal {
    margin-right: 10px;
}

::v-deep .van-radio {
    width: calc(33.333% - 10px);
}

::v-deep .van-radio__icon {
    display: none;
}

::v-deep .van-radio__label {
    width: 100%;
    height: 36px;
    line-height: 36px;
    border-radius: 4px;
    text-align: center;
    background-color: #f6f6f6;
    font-size: 14px;
    font-family: "PingFang SC";
    font-weight: 400;
    color: #333;
    margin-bottom: 10px;
    margin-left: 0;
}

::v-deep .active .van-radio__label {
    background: #ebfffa !important;
    border: 1px solid #3ebfa0;
    color: #3ebfa0;
    /* border: 1px solid var(--primary-color);
    color: var(--primary-color); */
}
.dropdown {
    background: #fff;
    display: flex;
    align-items: center;
}
.divider {
    width: 1px;
    height: 25px;
    background: #f1f1f1;
}
.define-dropdown-menu {
    width: 50%;
    display: flex;
    justify-content: center;
}
.define-dropdown-item {
    position: fixed;
    /* top: 46px; */
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    z-index: 9999;
}
::v-deep .define-dropdown-item .van-overlay {
    position: absolute;
}
::v-deep .define-dropdown-item .van-popup {
    position: absolute;
    border-radius: 0 0 10px 10px;
}
::v-deep .van-empty__image {
    width: 107px;
    height: 120px;
    img {
        background-size: 100% auto;
    }
}
</style>
  