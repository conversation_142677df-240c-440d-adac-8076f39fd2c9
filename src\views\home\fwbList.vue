<template>
    <div class="fwb-list">
        <div class="fwb-item more" v-for="(it, index) in fwbList" :key="index">
            <fwbItem
                :item="it"
                :more="true"
                :unicode="unicode"
                :deptId="deptId"
                :doctorId="doctorId"
            />
        </div>
    </div>
</template>

<script>
import { getfwbList } from "../../api/api";
import tools from "@/util/tools.js";

import fwbItem from "./components/fwbItem.vue";
import common from "@/util/common";
export default {
    name: "fwbList",
    components: {
        fwbItem,
    },
    data() {
        return {
            fwbList: [],
            hospOrgCode: "",
            unicode: "",
            deptId: "",
            doctorId: "",
        };
    },
    created() {
        this.hospOrgCode = this.$route.query.hospOrgCode || "";
        this.unicode =
            this.$route.query.unicode || this.$route.query.internetId || "";
        window.localStorage.setItem("unicode", this.unicode);
        this.deptId = this.$route.query.deptId || "";
        this.doctorId = this.$route.query.doctorId || "";
        this.getList(
            this.hospOrgCode,
            this.unicode,
            this.deptId,
            this.doctorId
        );
    },
    methods: {
        getList(hospOrgCode, unicode, deptId, doctorId) {
            let data = {
                hospOrgCode,
                unicode,
                deptId,
                doctorId,
            };
            // 获取服务包列表
            // api/v1/servicePack/list
            getfwbList(data).then((res) => {
                if (res && res.length) {
                    this.fwbList = res;
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
.fwb {
    &-list {
        display: flex;
        align-items: center;
        -webkit-overflow-scrolling: touch;
        flex-direction: column;
        width: 100%;
        .fwb-item {
            width: 3.45rem;
            margin-top: 0.15rem;
            // height: 134px;
            flex-shrink: 0;
            background: linear-gradient(0deg, #e5fcfa 0%, #f0fbf5 100%);
            // url("@/images/fwb/bg.png") no-repeat top;
            border-radius: 8px;
            box-sizing: border-box;
            padding: 10px;
            position: relative;

            .bg {
                display: block;
                background: url("@/images/fwb/bg.png") no-repeat top;
                width: 34px;
                height: 28px;
                position: absolute;
                background-size: 100%;
                right: 40px;
                top: 6px;
            }
            .name {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #106450;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .cont {
                width: 100%;
                // height: 91px;
                background: #ffffff;
                border-radius: 8px;
                margin-top: 6px;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                .detail {
                    display: flex;
                    flex-wrap: nowrap;
                    width: 100%;
                    overflow: hidden;
                    .item {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                        // display: flex;
                        flex-direction: row;
                        align-items: center;
                        margin-right: 10px;
                        flex-shrink: 0;
                        margin: 8px 8px 0px 8px;
                        width: 98%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;

                        .dg {
                            display: inline-block;
                            background: url("@/images/fwb/dg.png") no-repeat
                                center;
                            width: 11px;
                            height: 11px;
                            background-size: 100%;
                            margin-right: 3px;
                        }
                        .it {
                            margin-right: 10px;
                            :last-child {
                                margin-right: 0;
                            }
                        }
                    }
                    :last-child {
                        margin-right: 0;
                    }
                }
                .gift {
                    width: 249px;
                    height: 19px;
                    line-height: 19px;
                    background: linear-gradient(
                        90deg,
                        #e5fcfa 0%,
                        #f4fffe 100%
                    );
                    border-radius: 2px;
                    margin-left: 8px;
                    width: 234px;
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #3ebfa0;
                    padding: 0 3px;
                }
                .bt {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 8px;
                    // margin-bottom: 10px;

                    .price {
                        font-weight: 600;
                        font-size: 12px;
                        color: #fa541c;
                        line-height: 7px;
                    }
                    .xl {
                        font-weight: 400;
                        font-size: 12px;
                        color: #999999;
                        line-height: 5px;
                    }
                    .qg {
                        width: 73px;
                        height: 22px;
                        background: linear-gradient(
                            90deg,
                            #01cda7 0%,
                            #0fc2aa 99%
                        );
                        border-radius: 11px;
                        font-size: 13px;
                        color: #ffffff;
                    }
                }
            }
        }

        :last-child {
            margin-right: 0;
        }
    }
}
</style>
