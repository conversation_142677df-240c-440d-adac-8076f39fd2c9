<template>
    <div>
        <div class="bg"></div>
        <div class="name">{{ item.goodsName }}</div>
        <div class="cont">
            <div class="detail">
                <p class="item">
                    <template v-for="(it, index) in item.commonServiceList">
                        <i class="dg"></i>
                        <span class="it"
                            >{{ it.serviceName || it.serviceTypeName }}*{{
                                it.serviceUsageCount
                            }}</span
                        >
                    </template>

                    <!-- <i class="dg"></i>
                    <span class="it">图文咨询*3</span>

                    <i class="dg"></i>
                    <span class="it">图文咨询*3</span>

                    <i class="dg"></i>
                    <span class="it">图文咨询*3</span>

                    <i class="dg"></i>
                    <span class="it">图文咨询*3</span> -->
                </p>
            </div>
            <!-- 入口处都不显示赠送服务 -->
            <div class="gift" v-if="more && item.giftServiceList.length > 0">
                赠送：<span
                    v-for="(it, index) in item.giftServiceList"
                    :key="index"
                    >{{ it.serviceName || it.serviceTypeName }}*{{
                        it.serviceUsageCount
                    }};</span
                >
            </div>
            <div class="bt">
                <div style="display: flex; align-items: center">
                    <p class="price">
                        <span>￥</span
                        ><span style="font-size: 20px">{{
                            item.goodsSellingPrice
                        }}</span>
                    </p>
                    <p class="xl" style="margin-left: 10px">
                        销量：{{ item.goodsSellCount }}
                    </p>
                </div>

                <button class="qg" @click="gotoMall()">立即抢购</button>
            </div>
        </div>
    </div>
</template>
<script>
import common from "@/util/common";
import tools from "@/util/tools";

export default {
    props: ["item", "more", "unicode", "deptId", "doctorId"],
    data() {
        return {};
    },
    created() {},
    mounted() {},
    methods: {
        gotoMall() {
            // todo 跳转到服务包详情 地址还没提供
            // 如果有doctorId 需要拼接
            if (this.doctorId) {
                window.location.href = `${process.env.VUE_APP_fwbUrl}#/service-package/${this.item.goodsId}?unicode=${this.unicode}&deptId=${this.deptId}&doctorId=${this.doctorId}`;
            } else {
                window.location.href = `${process.env.VUE_APP_fwbUrl}#/service-package/${this.item.goodsId}?unicode=${this.unicode}`;
            }
            //
        },
        dencryptHeader: common.dencryptHeader,
        getData(hospOrgCode, unicode, deptId, doctorId) {
            let data = {
                hospOrgCode,
                unicode: unicode,
                deptId: deptId,
                doctorId: doctorId,
            };
            const that = this;
            // 获取服务包列表
            // api/v1/servicePack/list
            getfwbList(data).then((res) => {
                console.log("tjdoctor=--==--4444", res);
                if (res && res.length) {
                    var matrix = [[]];
                    for (const item of res) {
                        if (matrix[matrix.length - 1].length < 4) {
                            matrix[matrix.length - 1].push(item);
                        } else if (matrix[matrix.length - 1].length === 4) {
                            matrix.push([item]);
                        }
                    }
                    this.tjList = matrix;
                }
            });
        },
        jumpToPage(item) {
            console.log(item);
            let url =
                location.origin +
                location.pathname +
                `#/doctor?staffId=${item.staffId}&deptId=${item.deptId}&unicode=${item.unicode}`;
            item.status = "2";
            item.jumpUrl = url;
            tools.jumpUrlManage(item, 1);
        },
    },
};
</script>
<style lang="less" scoped>
.fwb {
    padding: 15px;

    .title {
        font-size: 18px;
        font-weight: 500;
        color: #000000;
        line-height: 1;
        margin-bottom: 15px;
        .more {
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 13px;
            color: #999999;
            float: right;
            .arr {
                display: inline-block;
                background: url("@/images/img/arrow_gray.png") no-repeat top;
                width: 5px;
                height: 10px;
                background-size: 100%;
                margin-left: 4px;
            }
        }
    }

    &-list {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        flex-direction: row;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;

        .fwb-item {
            width: 289px;
            // height: 134px;
            flex-shrink: 0;
            margin-right: 0.1rem;
            margin-bottom: 0.05rem;
            background: linear-gradient(0deg, #e5fcfa 0%, #f0fbf5 100%);
            // url("@/images/fwb/bg.png") no-repeat top;
            border-radius: 8px;
            box-sizing: border-box;
            padding: 10px;
            position: relative;

            .bg {
                display: block;
                background: url("@/images/fwb/bg.png") no-repeat top;
                width: 34px;
                height: 28px;
                position: absolute;
                background-size: 100%;
                right: 40px;
                top: 6px;
            }
            .name {
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 16px;
                color: #106450;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .cont {
                width: 100%;
                // height: 91px;
                background: #ffffff;
                border-radius: 8px;
                margin-top: 6px;
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                .detail {
                    display: flex;
                    flex-wrap: nowrap;
                    width: 100%;
                    overflow: hidden;
                    .item {
                        font-family: PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                        // display: flex;
                        flex-direction: row;
                        align-items: center;
                        margin-right: 10px;
                        flex-shrink: 0;
                        margin: 8px 8px 0px 8px;
                        width: 98%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;

                        .dg {
                            display: inline-block;
                            background: url("@/images/fwb/dg.png") no-repeat
                                center;
                            width: 11px;
                            height: 11px;
                            background-size: 100%;
                            margin-right: 3px;
                        }
                        .it {
                            margin-right: 10px;
                            :last-child {
                                margin-right: 0;
                            }
                        }
                    }
                    :last-child {
                        margin-right: 0;
                    }
                }
                .gift {
                    max-width: 95%;
                    width: fit-content;
                    line-height: 19px;
                    background: linear-gradient(
                        90deg,
                        #e5fcfa 0%,
                        #f4fffe 100%
                    );
                    border-radius: 2px;
                    margin-left: 8px;
                    font-size: 12px;
                    color: #3ebfa0;
                    padding: 0 3px;
                    margin-top: 10px;
                }
                .bt {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 8px;
                    // margin-bottom: 10px;

                    .price {
                        font-weight: 600;
                        font-size: 12px;
                        color: #fa541c;
                        line-height: 7px;
                    }
                    .xl {
                        font-weight: 400;
                        font-size: 12px;
                        color: #999999;
                        line-height: 5px;
                    }
                    .qg {
                        width: 73px;
                        height: 22px;
                        background: linear-gradient(
                            90deg,
                            #01cda7 0%,
                            #0fc2aa 99%
                        );
                        border-radius: 11px;
                        font-size: 13px;
                        color: #ffffff;
                    }
                }
            }
        }

        :last-child {
            margin-right: 0;
        }

        .more.fwb-item {
            .name {
                overflow: auto;
                text-overflow: unset;
                white-space: unset;
            }
            .cont .detail .item {
                width: 96%;
                overflow: auto;
                text-overflow: unset;
                white-space: unset;
            }
        }
    }
}
</style>