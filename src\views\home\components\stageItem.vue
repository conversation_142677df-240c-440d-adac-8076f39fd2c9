<template>
  <div class="content" @click="jumpUrl(data)">
    <img class="img" v-lazy="data.iconUrl" alt="" />
    <img
      class="angImg"
      v-lazy="data.angleIconUrl"
      alt=""
      v-show="data.angleIconUrl"
    />

    <h3 class="title">{{ data.applicationName }}</h3>
  </div>
</template>

<script>
import tools from "@/util/tools";
export default {
  name: "StageItem",
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    stageId: {
      type: String,
      default: () => "",
    },
    childStageId: {
      type: String,
      default: () => "",
    },
    position: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {};
  },
  mounted() {
    console.log("staageItem", this.$props.data);
  },
  methods: {
    jumpUrl(a, b) {
      console.log("参数", a, b);
      tools
        .handleSetPoint({
          stageId: this.stageId,
          childStageId: this.childStageId,
          stageAppId: a.applicationId,
          trackingContent: this.position
            ? `${this.position}|banner`
            : a.applicationName,
        })
        .then(() => {
          tools.jumpUrlManage(a, b);
        });
      // var applications = [
      //   "优势专科",
      //   "就医指南",
      //   "健康档案",
      //   "健康科普",
      //   "报告查询",
      //   "就医智管家",
      // ];
      // if (applications.indexOf(a.applicationName) !== -1) {
      //   tools
      //     .handleSetPoint({
      //       stageId: this.stageId,
      //       childStageId: this.childStageId,
      //       stageAppId: a.applicationId,
      //       trackingContent: a.applicationName,
      //     })
      //     .then(() => {
      //       tools.jumpUrlManage(a, b);
      //     });
      // } else {
      //   tools.jumpUrlManage(a, b);
      // }
    },
  },
};
</script>

<style scoped>
.content {
  font-size: 0;
  text-align: center;
  position: relative;
}
.img {
  height: 29px;
  background-size: auto 100%;
}
.angImg {
  position: absolute;
  right: 0;
  top: 0;
  height: 0.2rem;
  margin-top: -0.1rem;
}
.title {
  font-size: 12px;
  font-family: "PingFangSC";
  font-weight: 400;
  color: #333333;
  margin-top: 9px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
