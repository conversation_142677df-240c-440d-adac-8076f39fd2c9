<template>
    <div>
        <van-popup
            class="recordPopup"
            v-model="vis"
            position="bottom"
            round
            :style="{ height: '32%' }"
            :safe-area-inset-bottom="true"
            :close-on-click-overlay="false"
            @click-overlay="clickOverlay"
        >
            <p class="status">{{ statusTxt }}</p>
            <p class="prompt-txt">录音时长最长60秒</p>
            <div class="buttons">
                <div class="cancel" v-if="isStopFlag" @click="clearRecord()">
                    <img src="@/images/img/zx_delete.png" alt="" />
                </div>
                <div class="start">
                    <div
                        @touchstart="record('startRecord')"
                        @touchend="record('stopRecord')"
                        @touchmove="handleMove"
                    >
                        <img
                            v-if="!isRecording"
                            src="@/images/img/zx_record.png"
                            alt=""
                        />
                        <img
                            v-else
                            src="@/images/img/zx_recording.gif"
                            alt=""
                        />
                    </div>
                </div>
                <div class="stop" v-if="isStopFlag" @click="subVoice">
                    <img src="@/images/img/zx_confirm.png" alt="" />
                </div>
            </div>
        </van-popup>
    </div>
</template>
  
  <script>
import { getOssSign, ploadfilesOss } from "@/api/api";
import util from "@/util/util";
import Vue from "vue";
import { Popup } from "vant";
Vue.use(Popup);
import Recorder from "js-audio-recorder";
// const lamejs = require("lamejs");

export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            vis: this.visible,
            timeMax: 60,
            voiceObj: {
                res_: {
                    data: "",
                },
            },
            voicePath: "", // 音频服务器返回的访问路径
            totalTime: "",
            voiceFile: "",
            recorder: null,
            statusTxt: "按住录音",
            isRecording: false,
            isStopFlag: false,
            timestamp: "",
        };
    },
    watch: {
        visible(val) {
            console.log("__*****____", val);
            this.vis = val;
            this.isRecording = false;
            this.isStopFlag = false;
            this.statusTxt = "按住录音";
        },
    },
    created() {
        console.log("^^^^^^", this.visible);
        this.recorder = new Recorder({
            sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
            sampleRate: 48000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
            numChannels: 1, // 声道，支持 1 或 2， 默认是1
            // compiling: false,(0.x版本中生效,1.x增加中)  // 是否边录边转换，默认是false
        });
        const that = this;
        this.recorder.onprogress = function (params) {
            console.log("录音时长(秒)", params.duration);
            let duration = Math.round(params.duration);
            if (duration > that.timeMax) {
                that.recorder.stop();
                that.totalTime = duration;
                that.isRecording = false;
                that.isStopFlag = true;
                that.statusTxt = "录音结束";
            }
        };
    },
    mounted() {
        // this.voiceObj.res_.data = z.data;

        // let name = "2389i2347893478" + new Date().getTime() + ".mp3";
        // this.voiceFile = this.base64ToFile(z.data, name);
        // console.log("##############", this.voiceFile);

        if (navigator.userAgent.indexOf("AliApp") > -1) {
            let that = this;
            my.onMessage = function (e) {
                console.log("message", e);
                if (e.mini_audioRecord && e.system_microphoneAuthorized) {
                    that.isRecording = false;
                    that.isStopFlag = true;
                    that.statusTxt = "录音结束";
                    that.voiceObj = e;
                } else {
                    that.isRecording = false;
                    that.isStopFlag = false;
                    that.statusTxt = "按住录音";
                    that.$emit("update:visible", false);
                    // 如需使用上传语音功能，请同时开启以下麦克风权限
                    // 1、请点击右上角"...",进入"设置"，在设置中打开麦克风权限
                    // 2、请在手机设置中打开APP的麦克风权限
                    // if (!e.mini_audioRecord) {
                    //   util.showToast(
                    //     '请点击右上角"...",进入"设置"，在设置中打开麦克风权限'
                    //   );
                    //   return;
                    // }
                    // if (!e.system_microphoneAuthorized) {
                    //   util.showToast("请在手机设置中打开APP的麦克风权限");
                    //   return;
                    // }
                }
            };
        }
    },
    methods: {
        record(item) {
            console.log("-----r---", item);
            if (this.isStopFlag) {
                return;
            }
            if (item == "stopRecord") {
                this.isRecording = false;
                this.isStopFlag = true;
                this.statusTxt = "录音结束";

                // 非支付宝端toast提示，支付宝端小程序已经提示因此不用toast提示了
                let timestampStop = new Date().getTime();
                let timeDiff = timestampStop - this.timestamp;
                console.log("timeDiff-0-0-0-0-", timeDiff);
                if (timeDiff <= 1000) {
                    if (navigator.userAgent.indexOf("AliApp") == -1) {
                        util.showToast("录音时间太短");
                    }
                    let tda = {
                        url: "",
                        voiceTime: "",
                    };
                    this.$emit("refresh", tda);
                    this.$emit("update:visible", false);
                }
            }
            if (navigator.userAgent.indexOf("AliApp") > -1) {
                // 支付宝status startRecord 开始录音 stopRecord 结束录音 playVoice 播放录音
                if (item == "startRecord") {
                    this.isRecording = true;
                    this.isStopFlag = false;
                    this.statusTxt = "松开结束录音";
                    this.timestamp = new Date().getTime();
                }
                my.postMessage({
                    action: "record",
                    status: item,
                });
            } else {
                //h5
                if (item == "startRecord") {
                    this.startRecorder();
                } else if (item == "stopRecord") {
                    this.stopRecorder();
                }
            }
        },
        clearRecord() {
            let tda = {
                url: "",
                voiceTime: "",
            };
            this.$emit("refresh", tda);
            this.$emit("update:visible", false);
        },
        subVoice() {
            // 用户的userId + time;
            this.uInfo = JSON.parse(window.localStorage.getItem("userInfo"));
            let userId =
                window.localStorage.getItem("userId") ||
                this.uInfo.userId ||
                "";
            let n_ = userId + new Date().getTime();

            if (navigator.userAgent.indexOf("AliApp") > -1) {
                n_ += ".mp3";
                this.voiceFile = this.base64ToFile(this.voiceObj.res_.data, n_);
                this.totalTime = this.voiceObj.duration
                    ? Math.round(this.voiceObj.duration)
                    : 0;
            } else {
                n_ += ".wav";
                const blob = this.recorder.getWAVBlob(); // 获取wav格式音频数据
                this.voiceFile = new File([blob], n_);
            }
            console.log("录音文件-------", this.voiceFile);
            let data = {
                attachmentName: n_,
            };
            getOssSign(data).then((r) => {
                if (r) {
                    console.log(r);
                    var fromData = new FormData();
                    fromData.append("name", n_);
                    fromData.append("key", r.key);
                    fromData.append("OSSAccessKeyId", r.accessId);
                    fromData.append("policy", r.policy);
                    fromData.append("success_action_status", "200");
                    fromData.append("signature", r.signature);
                    fromData.append("file", this.voiceFile);
                    ploadfilesOss(fromData, r.host).then((res) => {
                        console.log("--^^^^~~~~~~~~~~-----", res);
                        this.voicePath = "https://cdnweb11.96225.com/" + r.key;
                        console.log("ddddvoicePath", this.voicePath);

                        let totalTime = "";
                        if (this.totalTime < 10) {
                            totalTime = "00:0" + this.totalTime;
                        } else if (
                            this.totalTime < 60 &&
                            this.totalTime >= 10
                        ) {
                            totalTime = "00:" + this.totalTime;
                        } else {
                            totalTime = "01:00";
                        }
                        let tda = {
                            url: this.voicePath,
                            voiceTime: totalTime,
                        };
                        this.$emit("refresh", tda);
                        this.$emit("update:visible", false);
                    });
                }
            });

            // this.voicePath =
            //   "https://cdnweb11.96225.com/inthis_test/20230824/49148aabf8594722ad6b57cb0b3f1b0f/2389i23478934781692862446251.mp3";
            // let tda = {
            //   url: this.voicePath,
            //   voiceTime: this.totalTime,
            // };
            // this.$emit("refresh", tda);
            // this.$emit("update:visible", false);
        },
        // 将 base64 转换为 File
        base64ToFile(base64, fileName) {
            // let arr = base64.split(",");
            // let mime = arr[0].match(/:(.\*?);/)[1];
            let bstr = atob(base64);
            let n = bstr.length;
            let u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            return new File([u8arr], fileName, { type: "audio/x-mpeg" });
        },
        /**
         *  录音的具体操作功能
         * */
        // 开始录音
        startRecorder() {
            let that = this;
            Recorder.getPermission().then(
                () => {
                    console.log("开始录音");
                    that.isRecording = true;
                    that.isStopFlag = false;
                    that.statusTxt = "松开结束录音";
                    that.recorder.start();
                    that.timestamp = new Date().getTime();
                },
                (error) => {
                    console.log(`H5录音error${error}`);
                    that.isRecording = false;
                    that.isStopFlag = false;
                    that.statusTxt = "按住录音";
                    that.$emit("update:visible", false);
                    that.$dialogBox({
                        title: "",
                        content: `如需使用上传语音功能，请同时开启以下麦克风权限\n1、请允许${location.hostname}使用你的麦克风\n2、请在手机设置中打开APP的麦克风权限`,
                        confirmTxt: "我知道了",
                        cancelTxt: "",
                        cancelCallback: function () {},
                        confirmCallback: function () {},
                    });
                }
            );
        },
        // 结束录音
        stopRecorder() {
            if (this.recorder) {
                this.recorder.stop();
                this.totalTime = Math.round(this.recorder.duration);
            }
        },
        clickOverlay() {
            if (
                !this.isRecording &&
                !this.isStopFlag &&
                this.statusTxt == "按住录音"
            ) {
                this.$emit("update:visible", false);
            }
        },
        handleMove(e) {
            let cancelMaxTop = document.querySelector(".recordPopup").offsetTop;
            const y = e.touches[0].clientY;
            if (y < cancelMaxTop) {
                this.record("stopRecord");
            }
        },
        // convertToMp3(wavDataView) {
        //   // 获取wav头信息
        //   // const wav = lamejs.WavHeader.readHeader(wavDataView); // 此处其实可以不用去读wav头信息，毕竟有对应的config配置
        //   // const { channels, sampleRate } = wav;
        //   var channels = 1;
        //   var sampleRate = 48000;
        //   const mp3enc = new lamejs.Mp3Encoder(channels, sampleRate, 128);
        //   // 获取左右通道数据
        //   const result = this.recorder.getChannelData();
        //   const buffer = [];

        //   const leftData =
        //     result.left &&
        //     new Int16Array(result.left.buffer, 0, result.left.byteLength / 2);
        //   const rightData =
        //     result.right &&
        //     new Int16Array(result.right.buffer, 0, result.right.byteLength / 2);
        //   const remaining = leftData.length + (rightData ? rightData.length : 0);

        //   const maxSamples = 1152;
        //   for (let i = 0; i < remaining; i += maxSamples) {
        //     const left = leftData.subarray(i, i + maxSamples);
        //     let right = null;
        //     let mp3buf = null;

        //     if (channels === 2) {
        //       right = rightData.subarray(i, i + maxSamples);
        //       mp3buf = mp3enc.encodeBuffer(left, right);
        //     } else {
        //       mp3buf = mp3enc.encodeBuffer(left);
        //     }

        //     if (mp3buf.length > 0) {
        //       buffer.push(mp3buf);
        //     }
        //   }

        //   const enc = mp3enc.flush();

        //   if (enc.length > 0) {
        //     buffer.push(enc);
        //   }

        //   return new Blob(buffer, { type: "audio/mp3" });
        // },
    },
};
</script>
  <style lang="less" scoped>
.status {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin: 30px 47px 16px;
    text-align: center;
}
.prompt-txt {
    font-size: 14px;
    font-family: PingFang SC;
    color: #fe963a;
    line-height: 14px;
    text-align: center;
}
.buttons {
    padding: 0 47px;
    display: flex;
    align-items: center;
    justify-content: center;
    .cancel {
        img {
            width: 31px;
            height: 31px;
        }
    }
    .stop {
        img {
            width: 31px;
            height: 31px;
        }
    }
    .start {
        img {
            width: 133px;
            height: 133px;
        }
        margin: 0 40px;
    }
}
img {
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -webkit-touch-callout: none;
}
.recordPopup {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -webkit-touch-callout: none;
}
</style>
  