<template>
    <div class="listContainer">
        <van-tabs
            v-model="activeTab"
            title-active-color="#333333"
            @change="changeTab"
            sticky
        >
            <van-tab
                v-for="(item, index) in tabList"
                :key="index"
                :title="item"
            >
                <div
                    class="listData"
                    :class="consultList.length == 0 ? 'columnFlex' : ''"
                >
                    <div v-if="consultList.length == 0" class="noList">
                        <img
                            :src="require('@/images/coupon/nodata.png')"
                            alt=""
                        />
                        <div class="tip">暂无优惠券</div>
                    </div>
                    <div class="consultList" v-else>
                        <van-list
                            v-model="listLoading"
                            :finished="finished"
                            finished-text="没有更多了"
                            :immediate-check="false"
                            @load="queryList"
                        >
                            <couponItem
                                v-for="(item, index) in consultList"
                                :key="index"
                                :data="item"
                                :curStatus="activeTab"
                                @refreshData="refreshData"
                            ></couponItem>
                        </van-list>
                    </div>
                </div>
            </van-tab>
        </van-tabs>
    </div>
</template>
<script>
import couponItem from "./components/couponItem.vue";
import { Tab, Tabs, List } from "vant";
import { queryList } from "@/api/coupon.js";
export default {
    components: {
        [Tab.name]: Tab,
        [Tabs.name]: Tabs,
        [List.name]: List,
        couponItem,
    },
    data() {
        return {
            activeTab: 0,
            listLoading: false,
            finished: false,
            pageNum: 1,

            tabList: ["未使用", "已使用", "已过期"],
            consultList: [],
            MoreShow: true,
        };
    },
    created() {
        if (
            sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5" ||
            localStorage.getItem("interHosp_origin") == "smk"
        ) {
            // s市民卡中，隐藏纳里的查看更多 入口
            this.MoreShow = false;
        }
        this.queryList();
    },
    methods: {
        changeTab(name, title) {
            debugger;
            console.log(this.activeTab);
            this.consultList = [];
            this.finished = false;
            this.pageNum = 1;
            this.queryList();
        },
        getStatus(name) {
            // 0:待发放
            // 1:已领取
            // 2:已使用
            // 3:使用中
            // 4:已过期
            // 9:已禁用
            // ["未使用", "已使用", "已过期"]
            let status = [];
            switch (name) {
                case 0:
                    status = [1]; // 未使用
                    break;
                case 1:
                    status = ["2", "3"]; // 已使用
                    break;
                case 2:
                    status = ["4"]; // 已过期
                    break;
                default:
                    status = [];
                    break;
            }
            return status.toString();
        },
        queryList() {
            /* this.consultList = [
                {
                    id: "11",
                    name: "11",
                    couponName: "三八节活动三八节活动三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.01.31 19:10-2024.02.02 23:59",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    couponType: "1",
                    minThreshold: "30",
                    discount: "5",
                    staffId: "dddd",
                    useDesc:
                        "仅在微信端-杭州健康通使用仅在微信端-杭州健康通使用仅在微信端-杭州健康通使用仅在微信端-杭州健康通使用",
                },
                {
                    id: "11",
                    name: "11",
                    couponName: "三八节活动三八节活动三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.01.31 19:10-2024.02.02 23:59",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    couponType: "1",
                    minThreshold: "30",
                    staffId: "dddd",
                    discount: "5",
                    useDesc: "",
                },
                {
                    id: "11",
                    name: "11",
                    couponName:
                        "三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.06.25至2024.08.31",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    couponType: "2",
                    staffId: "dddd",
                    discount: "5",
                    useDesc:
                        "仅在微信端-杭州健康通使用仅在微信端-杭州健康通使用仅在微信端-杭州健康通使用仅在微信端-杭州健康通使用",
                },
                {
                    id: "11",
                    name: "11",
                    couponName:
                        "三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.06.25至2024.08.31",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    couponType: "2",
                    staffId: "dddd",
                    discount: "5",
                    useDesc: "",
                },
                {
                    id: "11",
                    name: "11",
                    couponName: "三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.06.25至2024.08.31",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    couponType: "2",
                    staffId: "dddd",
                    discount: "5",
                    useDesc:
                        "仅在微信端-杭州健康通使用仅在微信端-杭州健康通使用仅在微信端-杭州健康通使用仅在微信端-杭州健康通使用",
                },
                {
                    id: "11",
                    name: "11",
                    couponName: "三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.06.25至2024.08.31",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    couponType: "2",
                    staffId: "dddd",
                    discount: "5",
                    useDesc: "",
                },
                {
                    id: "11",
                    couponName: "三八节活动三八节活动",
                    name: "11",
                    time: "2024.06.25至2024.08.31",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    remark: "",
                    couponType: "3",
                    staffId: "dddd",
                    discount: "5",
                },
                {
                    id: "11",
                    couponName: "11",
                    remark: "ddddddddd",
                    couponType: "0",
                    staffId: "dddd",
                    discount: "5",
                },
            ];
            return; */

            const data = {
                // orderStatus: ["51", "52", "53", "54", "6", "7"],
                userId: window.localStorage.getItem("userId"),
                couponStatus: this.getStatus(this.activeTab),
                pageNum: this.pageNum,
                pageSize: 10,
            };
            queryList(data).then((res) => {
                if (res && res.list) {
                    this.listLoading = false;
                    this.consultList = this.consultList.concat(res.list);
                    if (this.pageNum == res.pages) {
                        this.finished = true;
                    } else {
                        this.pageNum++;
                    }
                }
            });
        },
        toDoctorList() {
            this.$router.push({
                path: "/searchDoctorList",
                query: {},
            });
        },
        toRefundDetail(item) {
            this.$router.push({
                path: "/myConsult",
                query: {
                    orderNo: item.orderNo,
                },
            });
        },
        refreshData() {
            this.consultList = [];
            this.finished = false;
            this.pageNum = 1;
            this.queryList();
        },
        otherChannel() {
            let url = "";
            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                // 微信环境
                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=userConsultList&uInfo=${window.localStorage.getItem(
                    "naliWxEncrUser"
                )}`;
            } else {
                // 其余支付宝小程序环境
                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=userConsultList&source=jinTou-zfb-hlw&uInfo=${window.localStorage.getItem(
                    "encrUser"
                )}`;
            }
            window.location.href = url;
        },
    },
};
</script>
<style lang="less" scoped>
::v-deep .van-tabs__line {
    background: linear-gradient(90deg, #26c8b3 0%, #36eaab 100%);
    width: 0.27rem;
    bottom: 0.22rem;
}

.listContainer {
    font-family: PingFang SC;

    .listData {
        height: calc(100vh - 0.44rem);
        overflow-y: auto;

        .noList {
            img {
                display: block;
                width: 1.07rem;
                height: 0.8rem;
                margin: auto;
            }

            .tip {
                font-size: 0.14rem;
                font-weight: 400;
                color: #777777;
                text-align: center;
                margin-top: 0.195rem;
            }

            .turnBtn {
                width: 1.85rem;
                height: 0.4rem;
                background: #01cda7;
                border-radius: 0.2rem;
                font-size: 0.15rem;
                color: #fff;
                margin: auto;
                line-height: 0.4rem;
                text-align: center;
                margin-top: 0.3rem;
            }
        }
    }
    .channel {
        display: flex;
        justify-content: space-between;
        margin-top: 0.12rem;
        background-color: #fff;
        padding: 0.165rem 0.165rem 0.165rem 0.15rem;
    }
}
</style>
