<template>
    <div class="consultItem">
        <div
            class="cardDiv"
            :class="{ gray: curStatus !== 0, timeout: curStatus === 2 }"
        >
            <div class="left">
                <div class="title van-multi-ellipsis--l2">
                    <!-- <img
                        :src="require('@/images/coupon/tuwen.png')"
                        class="tImg"
                    /> -->
                    <span class="tImg">图文咨询</span>
                    <span class="tspan">
                        {{ data.couponName }}
                    </span>
                </div>
                <div class="timeSpan">{{ getTime(data) }}</div>
                <div class="time">
                    <!-- <div class="channel">
                        {{ data.channel }}
                    </div> -->
                    <div
                        @click="showR"
                        class="flex"
                        style="
                            flex-direction: row;
                            margin-right: 0.1rem;
                            margin-bottom: 0.15rem;
                            width: 90%;
                            justify-content: space-between;
                        "
                    >
                        <div v-show="data.useDesc">使用说明</div>
                        <div
                            v-show="data.useDesc"
                            class="arrow"
                            :class="{ up: showRemark }"
                        ></div>
                    </div>
                </div>
            </div>
            <div class="right" v-show="data.couponType == 1">
                <div class="flex">
                    <p class="gSP fw">
                        <span class="moneyUnit">￥</span
                        ><span class="moneyAmount">{{ data.discount }}</span>
                    </p>
                    <span class="gSP">满{{ data.minThreshold }}使用</span>
                </div>

                <button class="btn" @click="goUse" v-show="data.staffId">
                    去使用
                </button>
            </div>
            <div class="right" v-show="data.couponType == 0">
                <p
                    class="gSP fw moneyUnit"
                    style="width: 50%; text-align: center; line-height: 1.1"
                >
                    全额减免
                </p>
                <button class="btn" @click="goUse" v-show="data.staffId">
                    去使用
                </button>
            </div>
            <div class="right" v-show="data.couponType == 2">
                <p class="gSP fw">
                    <span class="moneyAmount">{{ data.discount }}</span>
                    <span class="moneyUnit">折</span>
                </p>
                <button class="btn" @click="goUse" v-show="data.staffId">
                    去使用
                </button>
            </div>

            <div class="right" v-show="data.couponType == 3">
                <div class="flex">
                    <p class="gSP fw">
                        <span class="moneyUnit">￥</span
                        ><span class="moneyAmount">{{ data.discount }}</span>
                    </p>
                    <span class="gSP">无门槛</span>
                </div>

                <button class="btn" @click="goUse" v-show="data.staffId">
                    去使用
                </button>
            </div>
        </div>
        <!-- :class="{ animate__slideInDown: showRemark }" -->
        <div v-show="showRemark" class="remark">
            {{ data.useDesc }}
        </div>
        <!-- <transition name="van-slide-down">
            
        </transition> -->
    </div>
</template>
<script>
import { confirmOrder } from "@/api/consult";
import { query, finish, cancel } from "@/api/consult.js";
import common from "@/util/common.js";
import util from "@/util/util.js";
import tools from "@/util/tools";

export default {
    props: {
        data: {
            type: Object,
            default: () => {},
        },
        curStatus: 0,
    },
    data() {
        return {
            showRemark: false,
        };
    },
    filters: {
        orderStatus(value) {
            let status = "";
            switch (value) {
                case "0":
                    status = "待支付";
                    break;
                case "1":
                    status = "待接单";
                    break;
                case "2":
                    status = "咨询中";
                    break;
                case "3":
                    status = "待评价";
                    break;
                case "4":
                    status = "已结束";
                    break;
                case "51":
                    status = "已取消";
                    break;
                case "52":
                    status = "已取消";
                    break;
                case "53":
                    status = "已取消";
                    break;
                case "54":
                    status = "已取消";
                    break;
                case "6":
                    status = "已拒绝";
                    break;
                case "7":
                    status = "已退回";
                    break;
                case "8":
                    status = "退款成功";
                    break;
                default:
                    status = "其他";
                    break;
            }
            return status;
        },
        busiType(value) {
            let status = "";
            switch (value) {
                case "1":
                    status = "在线复诊";
                    break;
                case "21":
                    status = "图文咨询";
                    break;
                case "22":
                    status = "电话咨询";
                    break;
                case "3":
                    status = "处方";
                    break;
                default:
                    status = "";
                    break;
            }
            return status;
        },
    },
    methods: {
        goUse() {
            if (this.data.staffId && this.data.deptId && this.data.unicode) {
                window.location.href =
                    "#/doctor?staffId=" +
                    this.data.staffId +
                    "&deptId=" +
                    this.data.deptId +
                    "&unicode=" +
                    this.data.unicode;
            }
        },
        getTime(data) {
            return (
                /* this.subDate(data.startValidTime) +
                "-" +
                this.subDate(data.endValidTime) */

                data.startValidTime + "-" + data.endValidTime
            );
        },
        /* subDate(d) {
            if (d) {
                d = d.replace(/-/g, ".");
                return d.substring(0, d.length - 3);
            } else {
                return "";
            }
        }, */
        showR() {
            this.showRemark = !this.showRemark;
        },
        dencryptHeader: common.dencryptHeader,
        toEvaluation() {
            this.$router.push({
                path: "/consultEvaluation",
                query: {
                    orderNo: this.data.orderNo,
                },
            });
        },
        reConsultation() {
            let url =
                location.origin +
                location.pathname +
                `#/doctor?staffId=${this.data.staffId}&deptId=${this.data.deptId}&unicode=${this.data.unicode}`;
            tools.jumpUrl(url, "2");
        },

        checkOrderStatus(callback) {
            const data = {
                orderNo: this.data.orderNo,
            };
            query(data).then((res) => {
                callback(res.orderStatus);
            });
        },
    },
};
</script>
<style lang="less" scoped>
.consultItem {
    width: 3.45rem;
    // height: 1.805rem;
    font-family: PingFang SC;
    // background: #ffffff;
    border-radius: 0.08rem;
    margin: 0.12rem auto 0;
    position: relative;
    // top: 0.12rem;
    // padding: 0.145rem 0.11rem 0.16rem 0.105rem;

    .flex {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .fw {
        font-weight: bold;
    }
    .cardDiv {
        background: url("../../../images/coupon/cardBg.png") no-repeat center;
        background-size: 100%;
        width: 3.45rem;
        height: 1.17rem;
        display: flex;
    }
    .left {
        width: 2.51rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .title {
            margin: 0.15rem 0 0 0.15rem;
            /*  overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2; 
            -webkit-box-orient: vertical;
            text-overflow: ellipsis; */
            position: relative;
            color: #333333;
            vertical-align: middle;
            width: 85%;
            .tImg {
                position: absolute;
                left: 0;
                top: 0;
                margin-top: 0.02rem;
                display: inline-block;
                width: 0.46rem;
                line-height: 0.16rem;
                background: linear-gradient(90deg, #3fe5cf 0%, #26c8b3 100%);
                border-radius: 0.02rem;
                font-size: 0.1rem;
                font-family: PingFang SC;
                font-weight: 400;
                color: #ffffff;
                text-align: center;
                // padding: 0.02rem;
            }
            .tspan {
                width: 95%;
                font-size: 0.15rem;
                font-weight: bold;
                margin-left: 0.6rem;
                line-height: 1.4;
            }
        }
        .channel {
            color: #3ebfa0;
            font-size: 0.11rem;
            // margin: 0rem 0rem 0rem 0rem;
        }
        .timeSpan {
            color: #888888;
            font-size: 0.11rem;
            margin: 0rem 0rem 0rem 0.15rem;
        }
        .time {
            color: #888888;
            font-size: 0.11rem;
            margin: 0rem 0rem 0rem 0.15rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .arrow {
            background: url("../../../images/coupon/arrow.png") no-repeat center;
            background-size: 100%;
            width: 0.08rem;
            height: 0.08rem;
            margin-left: 0.05rem;
        }
        .up {
            transform: rotate(180deg);
        }
    }

    .right {
        width: 0.94rem;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        .gSP {
            color: #3ebfa0;
            font-size: 0.12rem;
        }

        .moneyUnit {
            font-size: 0.2rem;
        }
        .moneyAmount {
            font-size: 0.33rem;
        }
        .btn {
            background: #01cda7;
            width: 0.6rem;
            height: 0.25rem;
            border-radius: 0.12rem;
            color: #ffffff;
            font-size: 0.13rem;
            margin-bottom: 0.05rem;
        }
    }

    .gray {
        background: url("../../../images/coupon/cardBgGray.png") no-repeat
            center;
        background-size: 100%;
        .tImg {
            background: #c2c2c2 !important;
        }
        .channel,
        .gSP {
            color: #c2c2c2 !important;
        }
        .btn {
            display: none;
        }
    }
    .timeout {
        background: url("../../../images/coupon/cardBgT.png") no-repeat center;
        background-size: 100%;
    }

    .remark {
        background: #ffffff;
        border-radius: 0 0 0.08rem 0.08rem;
        color: #999999;
        font-size: 0.12rem;
        padding: 0.12rem 0.15rem 0.12rem 0.15rem;
        margin-top: 0.01rem;
    }

    /* @-webkit-keyframes slideInDown {
        0% {
            -webkit-transform: translate3d(0, -100%, 0);
            transform: translate3d(0, -100%, 0);
            visibility: visible;
        }

        to {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }
    }

    @keyframes slideInDown {
        0% {
            -webkit-transform: translate3d(0, -100%, 0);
            transform: translate3d(0, -100%, 0);
            visibility: visible;
        }

        to {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }
    }

    .animate__slideInDown {
        -webkit-animation-name: slideInDown;
        animation-name: slideInDown;
    } */
}
</style>
