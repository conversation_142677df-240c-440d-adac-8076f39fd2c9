
import dialogBox from './dialogBox.vue'

export default {
    install(Vue) {
        let confirm = null;
        // 全局确认框
        const ConfirmBoxObj = Vue.extend(dialogBox);
        confirm = new ConfirmBoxObj();
        Vue.prototype.$dialogBox = (options = {
            title: '',
            content: '',
            confirmTxt: "",
            cancelTxt: "",
            cancelCallback: function () {
                console.log('cancelCallback ***');
            },
            confirmCallback: function () {
                console.log('confirmCallback ***');
            }
        }) => {
            confirm.$mount();
            confirm.setData(options);
            document.querySelector('body').appendChild(confirm.$el);
            confirm.open();
        };
        Vue.prototype.$hiddenConfirmBox = function () {
            // confirm.visible = false;
            confirm.close()
        }
    },

};