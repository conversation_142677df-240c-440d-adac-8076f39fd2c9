<!--Author: 郭元扬
ProjectDescription: 医院搜索组件
CreateTime: 2023-06-09
UpdateTime: 2023-06-09-->

<template>
    <div class="hospital-selector">
        <!-- style="height: 100vh" -->
        <div class="selector">
            <van-tree-select
                ref="tree1"
                :height="h_"
                :items="items"
                :active-id.sync="activeId"
                :main-active-index.sync="activeIndex"
                @click-nav="clickNav"
                @click-item="clickItem"
            />
        </div>
    </div>
</template>

<script>
import { TreeSelect } from "vant";
import { getDictionary, getHospList } from "@/api/api";
export default {
    data() {
        return {
            h_: "",
            items: [
                {
                    text: "不限",
                    children: [],
                },
                {
                    text: "市属公立",
                    id: "3",
                    children: [],
                },
                {
                    text: "区县",
                    id: "6",
                    children: [],
                },
                {
                    text: "社区",
                    id: "5",
                    children: [],
                },
                {
                    text: "其他",
                    children: [],
                },
            ],
            activeId: null,
            activeIndex: -1,
            hospitalList: [],
        };
    },
    components: {
        [TreeSelect.name]: TreeSelect,
    },
    methods: {
        initH() {
            // debugger
            console.log(
                document.querySelector(".van-dropdown-item--down").clientHeight
            );
            this.h_ =
                document.querySelector(".van-dropdown-item--down")
                    .clientHeight *
                    0.8 +
                "px";
            window.sessionStorage.setItem("h_", this.h_);
            console.log(this.h_);
        },

        clickNav(index) {
            if (index == 0) {
                this.activeId = null;
                this.$emit("sendSelect", {
                    unicode: "",
                    hospName: "不限",
                    hospLevel: "",
                });
            }
            //   this.handleGetHospital(index);
        },
        clickItem(val) {
            this.activeId = val.id;
            this.$emit("sendSelect", val);
        },
        handleGetHospital() {
            let channel = "6";
            if (
                window.localStorage.getItem("interHosp_origin") == "jktwxMini"
            ) {
                // 在健康通微信小程序内
                channel = 4;
                console.log("4444");
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_hospital_aksu_mini"
            ) {
                // 阿克苏
                channel = 9;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                    "health_smk_H5" ||
                window.navigator.userAgent.indexOf("smkVersion") > -1
            ) {
                // 市民卡
                channel = 8;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_zheliban_H5"
            ) {
                channel = 3;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") == "xhmhMini"
            ) {
                channel = 10;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") == "XsmhMin" ||
                sessionStorage.getItem("hlw_remoteChannel") == "XsmhMin_alipay"
            ) {
                channel = 11;
            } else if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                channel = 5;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") == "lian_health"
            ) {
                channel = 12;
            }
            let data = {
                businessPlatform: channel,
                hospTypeName: "",
                locationCode: "",
                hospLevel: "",
                platform: "",
                todayFlag: "",
                internetFlag: "1",
                pageNum: 1,
                pageSize: 500,
            };
            getHospList(data).then((res) => {
                this.hospitalList = res;
                // let hospDict = window.sessionStorage.getItem("hospDictionary");
                // if (hospDict) {
                // let hospDictionary = JSON.parse(hospDict);
                // this.items = hospDictionary;
                for (const levle of this.items) {
                    for (const hosp of this.hospitalList) {
                        if (["3", "5", "6"].indexOf(hosp.hospTypeCode) === -1) {
                            this.items[this.items.length - 1].children.push({
                                text: hosp.hospName,
                                unicode:
                                    hosp.unicode || hosp.internetId || "-1", // 没有internetId取unicode，没unicode直接传-1，为了适配医院没有internetId和unicode会查询所有医院医生的问题
                                hospName: hosp.hospName,
                                hospLevel: hosp.hospLevel,
                                id: hosp.hospName,
                            });
                        } else if (levle.id === hosp.hospTypeCode) {
                            levle.children.push({
                                text: hosp.hospName,
                                unicode:
                                    hosp.unicode || hosp.internetId || "-1", // 没有internetId取unicode，没unicode直接传-1，为了适配医院没有internetId和unicode会查询所有医院医生的问题
                                hospName: hosp.hospName,
                                hospLevel: hosp.hospLevel,
                                id: hosp.hospName,
                            });
                        }
                    }
                }
                this.items = this.items.filter(
                    (item) => !item.id || item.children.length
                );
                // } else {
                //   this.handleGetDictionary("105");
                // }
            });
        },
    },
    created() {
        this.handleGetHospital();
    },
};
</script>

<style lang="less" scoped>
.hospital-selector {
}
</style>
