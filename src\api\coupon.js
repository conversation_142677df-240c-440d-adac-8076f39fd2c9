import fetch from "./http";
import util from "../util/util";

//优惠券列表
export const queryList = (data) => {
    return fetch(
        "/inthos/Coupons/queryList",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => {
            return "";
        });
};

// 领券
export const getCoup = (data) => {
    return fetch(
        "/inthos/Coupons/couponByScan",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            return response;
        })
        .catch((err) => {
            return "";
        });
};


