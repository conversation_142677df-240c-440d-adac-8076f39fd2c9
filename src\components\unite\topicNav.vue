<!--
 * @Author: your name
 * @Date: 2024-12-06 11:04:39
 * @LastEditTime: 2024-12-17 17:40:13
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 双展台左，双展台右，拼成平行四边形，左侧展台为轮播
 * @FilePath: \h5-interhosp\src\components\unite\topicNav.vue
-->
<template>
    <div class="topnav" v-if="bannerList.length !== 0">
        <div class="left">
            <van-swipe :autoplay="3000" indicator-color="#01CDA7">
                <van-swipe-item
                    v-for="(item, index) in bannerList"
                    :key="index"
                >
                    <img
                        v-lazy="item.iconUrl"
                        :key="item.iconUrl"
                        alt=""
                        :data-exposure-id="elementId"
                        :data-exposure-content="item.elementContent"
                        :data-exposure-sn="index + 1"
                        @click="go(item, index, elementId)"
                    />
                </van-swipe-item>
            </van-swipe>
        </div>
        <div class="right">
            <div
                v-for="(item, index) in rightList"
                @click="go(item, index, elementId2)"
                :key="index"
            >
                <img
                    v-lazy="item.iconUrl"
                    :key="item.iconUrl"
                    :data-exposure-id="elementId2"
                    :data-exposure-content="item.elementContent"
                    :data-exposure-sn="index + 1"
                />
            </div>
        </div>
    </div>
</template>
  
  <script>
import tools from "@/util/tools.js";
import Vue from "vue";
import { Swipe, SwipeItem } from "vant";
Vue.use(Swipe).use(SwipeItem);
export default {
    components: {
        "mt-swipe": Swipe,
        "mt-swipe-item": SwipeItem,
    },
    props: {
        datalists: {
            type: Object,
        },
        dataOtherLists: {
            type: Object,
        },
    },
    data() {
        return {
            // 左子展台数据
            bannerList: "",
            // 右子展台数据
            rightList: "",
            elementId: "",
            elementId2: "",
        };
    },

    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("双展台1111111", temp);
        let temp_other = JSON.parse(JSON.stringify(this.dataOtherLists));
        console.log("双展台22222", temp_other);
        if (
            temp.stageTypeName == "双展台左" &&
            temp.childStageList.length !== 0
        ) {
            // 第一个子展台数据名称为双展台左，才可以渲染左侧
            this.bannerList = temp.childStageList;
            this.elementId = temp.elementId;
            console.log("bannerList", this.bannerList);
        }
        if (
            temp_other.stageTypeName == "双展台右" &&
            temp_other.childStageList.length !== 0
        ) {
            //第二个数据的子展台类别为双展台右时，才可以进行渲染右侧的图标
            this.rightList = temp_other.childStageList;
            this.elementId2 = temp_other.elementId;
            console.log("rightList", this.rightList);
        }
    },

    methods: {
        go(item, index, elementId) {
            item.elementId = elementId;
            item.index = index;
            console.log("双展台", item, index);
            tools.jumpUrlManage(item);
        },
    },
};
</script>
  
  <style scoped lang="less">
.topnav {
    width: 3.45rem;
    height: 1.38rem;
    margin: 0 auto;
    display: flex;
    margin-bottom: 0.1rem;
    .left {
        width: 1.78rem;
        height: 1.38rem;
        overflow: hidden;
        img {
            width: 100%;
            height: 1.38rem;
        }
        img[lazy="loading"] {
            display: block;
            width: 20%;
            line-height: 100%;
            margin: auto;
        }
        /deep/ .van-swipe__indicator.van-swipe__indicator--active {
            width: 0.1rem;
            height: 0.04rem;
            opacity: 1;
        }
        /deep/ .van-swipe__indicator {
            opacity: 0.2;
            background: #01cda7;
            width: 0.06rem;
            height: 0.04rem;
            border-radius: 0.02rem;
        }
        /deep/ .van-swipe__indicators {
            bottom: 0.16rem;
            left: 0.25rem;
        }
    }
    .right {
        > div:first-child {
            // margin: 0;
            width: 1.62rem;
            height: 0.64rem;
            margin-left: 0.1rem;
        }
        > div:last-child {
            margin-top: 0.1rem;
            width: 1.72rem;
            height: 0.64rem;
            position: relative;
        }
        img {
            width: 100%;
            height: 100%;
        }
        img[lazy="loading"] {
            display: block;
            width: 20%;
            line-height: 100%;
            margin: auto;
        }
    }
}
</style>
  