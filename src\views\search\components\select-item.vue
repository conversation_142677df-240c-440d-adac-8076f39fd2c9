<template>
  <div class="select" :style="{ height: h_, minHeight: '2rem' }">
    <div class="list">
      <p
        v-for="(item, index) of list"
        :key="index"
        @click="select(item)"
        :style="
          item.deptFirstClassCode === select1
            ? { color: '#3EBFA0', background: 'white' }
            : {}
        "
      >
        {{ item.deptFirstClassName }}
      </p>
    </div>
    <div class="list" v-show="level >= 1 && secondeList && secondeList.length">
      <p
        @click="selectAll(secondeFather, secondeFatherName, 2)"
        :style="
          secondeFather === select2
            ? { color: '#3EBFA0' }
            : { color: '#333333' }
        "
      >
        全部科室
      </p>
      <p
        v-for="(item, index) of secondeList"
        :key="index"
        @click="select(item, !!item.deptId)"
        :style="
          item.deptClassCode === select2 || item.deptId === select3
            ? { color: '#3EBFA0' }
            : { color: '#333333' }
        "
      >
        {{ item.deptClassName || item.deptName }}
      </p>
    </div>
    <div class="list" v-show="level >= 2 && thirdList && thirdList.length">
      <p
        @click="selectAll(thirdFather, thirdFatherName, 3)"
        :style="
          thirdFather === select3 ? { color: '#3EBFA0' } : { color: '#333333' }
        "
      >
        全部科室
      </p>
      <p
        v-for="(item, index) of thirdList"
        :key="index"
        @click="select(item)"
        :style="item.deptId === select3 ? { color: '#3EBFA0' } : {}"
      >
        {{ item.deptName }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: "SelectItem",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      h_: "",
      select1: "",
      select2: "",
      select3: "",
      level: 0,
      secondeList: [],
      secondeFather: "",
      secondeFatherName: "",
      thirdList: [],
      thirdFather: "",
      thirdFatherName: "",
      deptClassCode: "",
      deptId: "",
      deptName: "",
      type: "",
    };
  },
  mounted() {
    // 如果有一级科室
    if (this.$route.query.department) {
      this.select1 = this.$route.query.department;
      for (const item of this.list) {
        if (item.deptFirstClassCode === this.select1) {
          this.secondeFatherName = item.deptFirstClassName;
        }
      }
    }
    // 如果有二级科室
    if (
      this.$route.query.deptClassCode ||
      (!this.$route.query.deptClassCode && this.$route.query.deptId)
    ) {
      const secondId =
        this.$route.query.deptClassCode || this.$route.query.deptId;
      for (const item of this.list) {
        if (item.deptFirstClassCode === this.select1) {
          this.selectForShow(item);
        }
      }
      for (const second of this.secondeList) {
        if (second.deptClassCode == secondId || second.deptId == secondId) {
          this.selectForShow(second, !!second.deptId);
        }
      }
    }
    // 如果有三级科室
    if (this.$route.query.deptClassCode && this.$route.query.deptId) {
      for (const third of this.thirdList) {
        if (third.deptId === this.$route.query.deptId) {
          this.selectForShow(third);
        }
      }
    }
  },
  methods: {
    initH() {
      // debugger
      console.log(
        document.getElementsByClassName(".van-dropdown-item__content")
      );
      this.h_ =
        window.sessionStorage.getItem("h_") ||
        document.querySelector(".van-dropdown-item__content").clientHeight +
          "px";
    },
    selectForShow(item, isMixed) {
      if (item.deptFirstClassCode) {
        this.select1 = item.deptFirstClassCode;
        this.select2 = "-1";
        this.select3 = "-1";
        this.type = "";
        this.secondeList = [];
        this.level = 1;
        this.type = "二级";
        if (item.deptClassList) {
          this.secondeList = item.deptClassList.concat(item.deptList || []);
          this.secondeFather = item.deptFirstClassCode;
          this.secondeFatherName = item.deptFirstClassName;
        } else if (item.deptList) {
          this.level = 2;
          this.secondeList = item.deptList;
          this.secondeFather = item.deptFirstClassCode;
          this.secondeFatherName = item.deptFirstClassName;
        }
      } else if (item.deptClassCode) {
        this.select2 = item.deptClassCode;
        this.type = "";
        this.thirdList = [];
        this.level = 2;
        this.thirdList = item.deptList;
        this.thirdFather = item.deptClassCode;
        this.thirdFatherName = item.deptClassName;
        this.type = "三级";
      } else {
        this.select3 = item.deptId;
        this.deptId = item.deptId;
        this.deptName = item.deptName;
      }
      // 是否三级科室混入到二级科室列表
      if (isMixed !== undefined) {
        if (isMixed) {
          this.select2 = "-1";
          this.level = 1;
        } else {
          this.select3 = "-1";
        }
      }
    },
    select(item, isMixed) {
      if (item.deptFirstClassCode) {
        this.select1 = item.deptFirstClassCode;
        this.select2 = "-1";
        this.select3 = "-1";
        this.type = "";
        this.secondeList = [];
        this.level = 1;
        this.type = "二级";
        if (item.deptClassList) {
          this.secondeList = item.deptClassList.concat(item.deptList || []);
          this.secondeFather = item.deptFirstClassCode;
          this.secondeFatherName = item.deptFirstClassName;
        } else if (item.deptList) {
          this.level = 2;
          this.thirdList = item.deptList;
          this.thirdFather = item.deptFirstClassCode;
          this.thirdFatherName = item.deptFirstClassName;
        } else {
          // 蔡睿科室树只到二级可能只有一级
          this.$emit(
            "sendDept",
            "deptClassCode",
            item.deptFirstClassCode,
            item.deptFirstClassName
          );
        }
      } else if (item.deptClassCode) {
        this.select2 = item.deptClassCode;
        this.type = "";
        this.thirdList = [];
        this.level = 2;
        this.thirdList = item.deptList;
        this.thirdFather = item.deptClassCode;
        this.thirdFatherName = item.deptClassName;
        // 蔡睿科室树只到二级
        if (!item.deptList) {
          this.$emit(
            "sendDept",
            "deptClassCode",
            item.deptClassCode,
            item.deptClassName
          );
        }
        this.type = "三级";
      } else {
        this.select3 = item.deptId;
        this.deptId = item.deptId;
        this.deptName = item.deptName;
        this.$emit("sendDept", "deptId", this.deptId, this.deptName);
      }
      // 是否三级科室混入到二级科室列表
      if (isMixed !== undefined) {
        if (isMixed) {
          this.select2 = "-1";
          this.level = 1;
        } else {
          this.select3 = "-1";
        }
      }
    },
    selectAll(code, name, num) {
      console.log(
        this.secondeFather,
        "secondeFather",
        code,
        "code",
        this.thirdFather,
        num
      );
      if (num === 2) {
        this.select2 = "-1";
        this.select3 = "-1";
        this.$emit("sendDept", "deptClassCode", code, name);
      } else if (num === 3) {
        this.select3 = "-1";
        this.$emit("sendDept", "deptClassCode", code, name);
      }
      console.log(this.type, name, code, "name");
      if (this.type === "二级") {
        this.deptClassCode = code;
      } else if (this.type === "三级") {
        this.deptId = code;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.select {
  display: flex;
  .list {
    display: flex;
    flex-direction: column;
    width: 33.33%;
    box-sizing: border-box;
    background: white;
    overflow-y: auto;
    overflow-x: hidden;
    &:nth-of-type(1) {
      background: #f6f7f8;
    }
    p {
      box-sizing: border-box;
      min-height: 0.49rem;
      width: 100%;
      color: #333333;
      font-size: 0.14rem;
      padding-left: 0.19rem;
      text-overflow: ellipsis;
      //   overflow: hidden;
      //   white-space: nowrap;
      display: flex;
      align-items: center;
    }
  }
}
</style>
