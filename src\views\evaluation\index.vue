<!--
 * @Author: shenpp
 * @Date: 2023-06-14
 * @LastEditTime: 2023-10-08
 * @Description: 患者评价页面
-->
<template>
  <div class="evalution">
    <div class="top">
      <div class="all">
        <span>患者评价</span>
        <span>({{ totalNum }})</span>
      </div>
      <div class="num">
        <span>{{ Math.floor(averageScore * 100) / 100 }}</span
        >分
      </div>
    </div>
    <!-- 暂无数据，隐藏 -->
    <!-- <div class="taps">
      <ul>
        <li
          @click="changeActive(index,item.type)"
          :class="{ active: index == active_num }"
          v-for="(item, index) in tab_list"
          :key="index"
        >
          {{ item.type }}
        </li>
      </ul>
    </div> -->
    <van-list
      v-model="loading"
      :finished="finished"
      finished-text=""
      @load="getlist"
    >
      <evaluation-list
        v-if="evaluation_list && evaluation_list.length !== 0"
        :evaluation_list="evaluation_list"
      ></evaluation-list>
      <div v-else class="noAttention">
        <img src="@/images/img/noEvaluation.png" />
        <p>暂无患者评价</p>
      </div>
    </van-list>
  </div>
</template>

<script>
import EvaluationList from "./components/evaluationList.vue";
import { doctorEvaluatePageList } from "@/api/api.js";
import { List } from "vant";
export default {
  components: {
    EvaluationList,
    [List.name]: List,
  },
  data() {
    return {
      tab_list: [
        {
          type: "全部",
          num: "24",
          id: "0",
        },
        {
          type: "讲解细致",
          num: "11",
          id: "1",
        },
        {
          type: "回答很专业",
          num: "13",
          id: "2",
        },
        {
          type: "清晰",
          num: "13",
          id: "2",
        },
      ],
      active_num: 0,
      totalNum: "",
      evaluation_list: [],
      averageScore: "", //医生总评分
      doctorId: "", //医生身份证号
      staffId: "", //医生编码
      finished: false,
      loading: false,
      pageNum: 1,
    };
  },
  created() {
    console.log("传参", this.$route.query);
    // this.averageScore = this.$route.query.averageScore;
    this.doctorId = this.$route.query.doctorId;
    this.staffId = this.$route.query.staffId;
    this.unicode = this.$route.query.unicode;
    // this.getEvaluateList('142322198407133029','4250')
    // this.getEvaluateList(this.doctorId, this.staffId, this.unicode);
  },
  methods: {
    // 切换标签
    changeActive(index, type) {
      console.log("切换tag", index, type);
      this.active_num = index;
      this.getEvaluateList(
        this.doctorId,
        this.staffId,
        this.unicode,
        type == "全部" ? "" : type
      );
    },
    // 查询评价接口
    getlist() {
      let data = {
        doctorId: this.doctorId,
        staffId: this.staffId,
        unicode: this.unicode,
        // 是否对外可见
        isAvailable: "1",
        //是否分页，1是 0否
        isPaging: 0,
        // 默认是10
        pageSize: 10,
        pageNum: this.pageNum,
      };
      doctorEvaluatePageList(data).then((res) => {
        this.loading = false;
        console.log("患者评价列表", res);
        // 后端改出参
        if (res) {
          this.averageScore = res.totalScore;
          this.totalNum = res.page.total;
          let List = res.page.list;
          this.evaluation_list = this.evaluation_list.concat(List);
          if (this.pageNum >= res.page.pages) {
            this.finished = true;
          } else {
            this.pageNum++;
          }
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.evalution {
  background-color: #fff;
  width: 3.15rem;
  border-radius: 0.08rem;
  margin: 0 auto;
  margin-top: 0.1rem;
  padding: 0.15rem;
  margin-bottom: 0.1rem;
}
.top {
  display: flex;
  align-items: center;
  .all {
    flex: 1;
    span {
      color: #333333;
      font-size: 0.18rem;
    }
    span:last-child {
      color: #999999;
      font-size: 0.14rem;
      margin-left: 0.05rem;
    }
  }
  .num {
    color: #fe963a;
    font-size: 0.14rem;
    font-weight: bold;
    font-size: 0.18rem;
  }
}
.taps {
  width: 3.15rem;
  display: flex;
  overflow-y: hidden;
  overflow-x: scroll;
  margin-top: 0.2rem;
  ul {
    display: flex;
    li {
      padding: 0.08rem 0.12rem;
      margin-right: 0.08rem;
      width: auto;
      white-space: nowrap;
      border-radius: 0.14rem;
      color: #333333;
      background: #f8f8f8;
      font-size: 0.12rem;
    }
    .active {
      background-color: #01cda7;
      color: #fff;
    }
  }
}
.noAttention {
  color: #777777;
  font-size: 0.14rem;
  margin-top: 35vh;
  text-align: center;
  margin-bottom: calc(100vh - 35vh - 3rem);
  img {
    display: inline-block;
    width: 1.07rem;
    height: 1.2rem;
  }
  p {
    margin-top: 0.12rem;
  }
}
</style>
