<template>
  <div class="content">
    <!-- <img
      class="leftImg"
      v-lazy="data.newHospImgUrl || data.internetImgUrl"
      alt=""
    /> -->
    <!-- <button @click="goDh(data)">导航</button> -->

    <div style="position: relative" @click="gotoOrg(data)">
      <div>
        <h3 class="title">{{ data.hospName }}</h3>
        <div class="arrow">
          <img src="@/images/img/arrow_gray.png" alt="" />
        </div>
      </div>
      <div class="tags">
        <div class="tag-level" v-if="data.hospTypeCode">
          {{ data.hospTypeCode | getHospitalType }}
        </div>
        <div class="tag" v-if="data.hospTag">
          <div v-for="(txt, index) in data.hospTag.split(',')" :key="index">
            <img src="@/images/img/home_tag_img.png" alt="" />
            <span>{{ txt }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="addr" @click="goDh(data)">
      <div>
        <img src="@/images/img/home_addr_img.png" alt="" srcset="" />
        <span class="txt">
          {{
            (data.internetExtendsObj && data.internetExtendsObj.address) ||
            data.address
          }}
        </span>
      </div>
      <!-- <span class="distance">1.2km</span> -->
      <span class="distance" v-if="data.distance && data.distance != 0">
        {{
          data.distance < 1
            ? parseFloat(data.distance) * 1000 + "m"
            : data.distance + "km"
        }}
      </span>
    </div>
  </div>
</template>

<script>
import tools from "@/util/tools.js";
import { userAuthorize } from "@/api/api.js";
export default {
  name: "index",
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      hospTag: "可挂号,医保定点",
    };
  },
  filters: {
    getHospitalType(value) {
      let methods = "";
      switch (value) {
        case "3":
          methods = "市属公立";
          break;
        case "6":
          methods = "区县";
          break;
        case "5":
          methods = "社区";
          break;
        default:
          methods = "其他";
          break;
      }
      return methods;
    },
  },
  created() {},
  methods: {
    goDh(data) {
      if (
        navigator.userAgent.indexOf("AliApp") > -1 &&
        data.longitude &&
        data.latitude
      ) {
        my.openLocation({
          longitude: data.longitude,
          latitude: data.latitude,
          name: data.hospName,
          address: data.address,
          success: res => {
            console.log(res);
          },
          fail: res => {
            console.log(res);
          },
        });
      } else if (
        navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 &&
        data.longitude &&
        data.latitude
      ) {
        wx.openLocation({
          longitude: data.longitude,
          latitude: data.latitude,
          name: data.hospName,
          address: data.address,
          success: res => {
            console.log(res);
          },
          fail: res => {
            console.log(res);
          },
        });
      }
    },
    gotoOrg(data_) {
      tools
        .handleSetPoint({
          trackingContent: "机构主页",
          orgId: data_.unicode,
          orgName: data_.hospName,
        })
        .then(() => {
          tools.gotoOrg(data_);
        });
      /* let data = { ...data_ };
            console.log("医院列表", data);
            let jumpUrl = data.jumpUrl;
            let hospUrl =
                location.origin +
                location.pathname +
                `#/hosporg?internetId=${data.internetId}&hospName=${data.hospName}&hospId=${data.hospId}`;
            if (!data.miniId) {
                // 没有小程序id，区分卫健和自建平台
                // 卫健
                if (this.$store.state.isWjAliMini) {
                    // 1.没有miniid，无clientid  跳转我们自己的
                    if (!data.clientId) {
                        jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            hospUrl
                        )}`;
                        // jumpUrl = `/pages/index/index?hospId=${data.hospId}`;
                    } else {
                        // 2.没有miniid，有clientid 一定有unicode，如市儿童H5  先跳自建平台小程序
                        let tempUrl =
                            data.jumpUrl.indexOf("?") > -1
                                ? `${data.jumpUrl}&clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`
                                : `${data.jumpUrl}?clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`;
                        // 小程序跳转链接拼接
                        jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            tempUrl
                        )}`;
                        // 有clientid的需要userid authStatus传2
                        data.authStatus = "2";
                    }
                    data.jumpUrl = jumpUrl;
                    data.appType = "3";
                } else {
                    // 没有小程序id  自建平台
                    if (!data.clientId) {
                        // 1.没有miniid，无clientid  跳转我们自己页面
                        data.jumpUrl = hospUrl;
                    } else {
                        // 2.没有clientid  有clientid 一定有unicode  如市儿童H5  授权后跳对应H5
                        data.jumpUrl =
                            data.jumpUrl.indexOf("?") > -1
                                ? `${data.jumpUrl}&clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`
                                : `${data.jumpUrl}?clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`;
                        data.status = "2";
                    }
                }
            } else {
                // 有小程序id 卫健与自建平台逻辑一致的情况
                // 3.有小程序id  有unicode  拼接unicode
                data.jumpUrl =
                    data.jumpUrl.indexOf("?") > -1
                        ? `${data.jumpUrl}&unicode=${data.unicode}&source=miniAlipay`
                        : `${data.jumpUrl}?unicode=${data.unicode}&source=miniAlipay`;
                // 4.有小程序id  无unicode  无特殊处理  直接跳转对方小程序
            }
            tools.jumpUrlHosporg(data); */
    },
    // 2023.8.10之前原机构跳转--已不用
    /* gotoOrg_(data) {
            console.log("医院列表", data);
            if (this.$store.state.isWjAliMini) {
                //卫健 跳转小程序
                let jumpUrl = data.jumpUrl;
                if (data.clientId) {
                    let tempUrl =
                        data.jumpUrl.indexOf("?") > -1
                            ? `${data.jumpUrl}&clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`
                            : `${data.jumpUrl}?clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`;
                    // 小程序跳转链接拼接
                    jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                        tempUrl
                    )}`;
                    // 有clientid的需要userid authStatus传2
                    data.authStatus = "2";
                }
                console.log("医院跳转", jumpUrl);
                if (!data.jumpUrl) {
                    let hospUrl =
                        location.origin +
                        location.pathname +
                        `#/hosporg?internetId=${data.internetId}&hospName=${data.hospName}&hospId=${data.hospId}`;
                    jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                        hospUrl
                    )}`;
                }
                data.jumpUrl = jumpUrl;
                if (!data.miniId) {
                    data.miniId = "2021002138635948";
                }
                data.appType = "3";
                console.log("医院列表```", data);
                tools.jumpUrlHosporg(data);
            } else {
                // if (data.clientId) {
                //   // userid:'d653fc8d-779a-482d-bab1-045a775cb731'    clientid:8A2F0306A7AB4BA8
                //   let userId = "d653fc8d-779a-482d-bab1-045a775cb731";
                //   userAuthorize(userId,data.clientId).then((res) => {
                //     console.log('auth授权',res)
                //   });
                // }
                // return
                // 非小程序跳转
                if (data.appType != "3") {
                    // 特杨， auth授权,拼接clientId
                    if (data.clientId) {
                        // data.jumpUrl = `${data.jumpUrl}&clientId=${data.clientId}`
                        data.jumpUrl =
                            data.jumpUrl.indexOf("?") > -1
                                ? `${data.jumpUrl}&clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`
                                : `${data.jumpUrl}?clientId=${data.clientId}&source=miniAlipay&unicode=${data.unicode}`;
                        data.status = "2";
                    } else {
                        // 非小程序跳转 H5
                        data.jumpUrl =
                            location.origin +
                            location.pathname +
                            `#/hosporg?internetId=${data.internetId}&hospName=${data.hospName}&hospId=${data.hospId}`;
                    }
                }

                tools.jumpUrlHosporg(data);
            }
        }, */
  },
};
</script>

<style lang="less" scoped>
.content {
  // display: flex;
  // align-items: flex-start;
  padding: 15px 15px 17px;
  // border-top: 1px solid #f1f1f1;
  font-family: "PingFang SC";
  margin-bottom: 12px;
  border-radius: 8px;
  background-color: #fff;
  position: relative;
}
.title {
  width: 300px;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 10px;
}
.tags {
  margin-bottom: 10px;

  // color: var(--primary-color);
  font-size: 12px;
  font-weight: 400;
  display: flex;
  // align-items: center;
  .tag-level {
    color: #3ebfa0;
    border-radius: 2px;
    background: rgba(1, 205, 167, 0.1);
    // border: 1px solid #3ebfa0;
    // border: 1px solid var(--primary-color);
    padding: 3px 6px;
    height: 17px;
    line-height: 17px;
    flex-shrink: 0;
  }
  .tag {
    margin-left: 22px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    color: #55a2f0;
    > div {
      display: flex;
      align-items: center;
      margin-bottom: 3px;
    }
    img {
      display: inline-block;
      width: 13px;
      height: 13px;
      background-size: 100% 100%;
      margin: 0 4px;
    }
  }
}
.addr {
  font-size: 12px;
  font-weight: 400;
  color: #666666;
  line-height: 1.4;
  display: flex;
  justify-content: space-between;
  > div {
    width: 248px;
    display: flex;
    align-items: flex-start;
    img {
      width: 9px;
      height: 12px;
      background-size: 100% 100%;
      margin-top: 3px;
      margin-right: 8px;
    }
  }
  span {
    display: inline-block;
  }
  .txt {
    // width: calc(100% - 55px);
  }
  .distance {
    width: 40px;
    text-align: right;
  }
}
.leftImg {
  width: 60px;
  height: 60px;
  font-size: 0;
  flex-shrink: 0;
  border-radius: 4px;
  /* object-fit: fill; */
  background-size: 100% 100%;
  margin-right: 17px;
  margin-top: 3px;
}
.arrow {
  width: 5px;
  height: 10px;
  position: absolute;
  top: 0;
  right: 0;
  img {
    width: 100%;
    background-size: 100% auto;
    position: relative;
    display: block;
  }
}
</style>
