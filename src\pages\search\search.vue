<template>
  <div class="search-container">
    <!-- 搜索头部 -->
    <div class="search-header">
      <van-nav-bar
        title="搜索活动"
        left-arrow
        @click-left="$router.back()"
      />
      
      <div class="search-input-wrapper">
        <van-search
          v-model="searchKeyword"
          placeholder="请输入活动名称或编号进行搜索"
          show-action
          @search="handleSearch"
          @cancel="handleCancel"
          @clear="handleClear"
        />
      </div>
    </div>

    <!-- 搜索内容 -->
    <div class="search-content">
      <!-- 搜索历史 -->
      <div class="search-history" v-if="!searchKeyword && searchHistory.length > 0">
        <div class="history-header">
          <h3>搜索历史</h3>
          <van-button type="default" size="mini" @click="clearHistory">清空</van-button>
        </div>
        <div class="history-tags">
          <van-tag
            v-for="(item, index) in searchHistory"
            :key="index"
            type="default"
            @click="searchKeyword = item"
          >
            {{ item }}
          </van-tag>
        </div>
      </div>

      <!-- 热门搜索 -->
      <div class="hot-search" v-if="!searchKeyword && hotSearchList.length > 0">
        <h3>热门搜索</h3>
        <div class="hot-tags">
          <van-tag
            v-for="(item, index) in hotSearchList"
            :key="index"
            :type="index < 3 ? 'primary' : 'default'"
            @click="searchKeyword = item"
          >
            {{ item }}
          </van-tag>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results" v-if="searchKeyword">
        <!-- 搜索结果统计 -->
        <div class="result-stats" v-if="searchResults.length > 0">
          找到 {{ totalCount }} 个相关活动
        </div>

        <!-- 活动列表 -->
        <van-list
          v-model="loading"
          :finished="finished"
          finished-text="—我也是有底线的—"
          @load="loadSearchResults"
        >
          <div 
            v-for="activity in searchResults" 
            :key="activity.id"
            class="activity-item"
            @click="goToActivityDetail(activity)"
          >
            <img :src="activity.imageUrl" :alt="activity.title" class="activity-image" />
            <div class="activity-content">
              <h4 class="activity-title" v-html="highlightKeyword(activity.title)"></h4>
              <div class="activity-meta">
                <span class="activity-status" :class="activity.statusClass">{{ activity.statusText }}</span>
                <span class="activity-time">{{ activity.timeText }}</span>
              </div>
              <div class="activity-info">
                <span class="activity-participants">报名人数: {{ activity.participantCount }}/{{ activity.maxParticipants }}</span>
                <span class="activity-location" v-if="activity.location">{{ activity.location }}</span>
              </div>
              <div class="activity-description" v-html="highlightKeyword(activity.description)"></div>
            </div>
          </div>
        </van-list>

        <!-- 空状态 -->
        <van-empty 
          v-if="!loading && searchResults.length === 0 && hasSearched"
          description="没有找到相关活动"
          image="search"
        >
          <van-button type="primary" size="small" @click="handleClear">重新搜索</van-button>
        </van-empty>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import { 
  NavBar, 
  Search, 
  Button, 
  Tag, 
  List, 
  Empty 
} from 'vant';

Vue.use(NavBar)
  .use(Search)
  .use(Button)
  .use(Tag)
  .use(List)
  .use(Empty);

export default {
  name: 'SearchPage',
  data() {
    return {
      searchKeyword: '',
      searchHistory: [],
      hotSearchList: [],
      searchResults: [],
      loading: false,
      finished: false,
      hasSearched: false,
      page: 1,
      totalCount: 0
    };
  },
  mounted() {
    this.loadSearchHistory();
    this.loadHotSearch();
    
    // 如果有传入的搜索关键词，直接搜索
    if (this.$route.query.keyword) {
      this.searchKeyword = this.$route.query.keyword;
      this.handleSearch();
    }
  },
  methods: {
    loadSearchHistory() {
      // 从本地存储加载搜索历史
      const history = localStorage.getItem('activity_search_history');
      if (history) {
        this.searchHistory = JSON.parse(history);
      }
    },
    
    saveSearchHistory(keyword) {
      if (!keyword.trim()) return;
      
      // 移除重复项
      this.searchHistory = this.searchHistory.filter(item => item !== keyword);
      // 添加到开头
      this.searchHistory.unshift(keyword);
      // 限制历史记录数量
      this.searchHistory = this.searchHistory.slice(0, 10);
      
      // 保存到本地存储
      localStorage.setItem('activity_search_history', JSON.stringify(this.searchHistory));
    },
    
    clearHistory() {
      this.searchHistory = [];
      localStorage.removeItem('activity_search_history');
    },
    
    async loadHotSearch() {
      try {
        // TODO: 调用API获取热门搜索数据
        this.hotSearchList = [
          '理财讲座',
          '亲子研学',
          '健康体检',
          '微信支付礼',
          '金融必知必晓'
        ];
      } catch (error) {
        console.error('加载热门搜索失败:', error);
      }
    },
    
    handleSearch() {
      if (!this.searchKeyword.trim()) return;
      
      this.saveSearchHistory(this.searchKeyword.trim());
      this.resetSearchResults();
      this.loadSearchResults();
    },
    
    handleCancel() {
      this.searchKeyword = '';
      this.resetSearchResults();
    },
    
    handleClear() {
      this.searchKeyword = '';
      this.resetSearchResults();
    },
    
    resetSearchResults() {
      this.searchResults = [];
      this.page = 1;
      this.finished = false;
      this.hasSearched = false;
      this.totalCount = 0;
    },
    
    async loadSearchResults() {
      if (this.loading || !this.searchKeyword.trim()) return;

      this.loading = true;
      this.hasSearched = true;

      try {
        // 1.8 首页搜索中间页接口调用
        const params = {
          actTitle: this.searchKeyword.trim(), // 活动名称搜索
          // actId: null // 活动Id搜索，这里不使用
        };

        const response = await this.$api.searchActivity(params);

        if (response && response.success === 1) {
          const activities = response.value || [];

          // 转换数据格式以适配页面显示
          const formattedActivities = activities.map(item => ({
            id: item.id || item.actId,
            title: item.actTitle,
            imageUrl: item.headerImg,
            headerImgId: item.headerImgId,
            activityStatus: item.activityStatus,
            statusText: this.getActivityStatusText(item.activityStatus),
            statusClass: this.getStatusClass(item.activityStatus),
            timeText: item.actTime,
            userRegistered: item.register === 1, // 1已报名，0未报名
            maxParticipants: item.numRage,
            participantCount: item.registerCount,
            description: item.actTitle // 使用标题作为描述
          }));

          if (this.page === 1) {
            this.searchResults = formattedActivities;
            this.totalCount = formattedActivities.length;
          } else {
            this.searchResults.push(...formattedActivities);
          }

          this.page++;
          this.finished = formattedActivities.length < 10;
        } else {
          this.searchResults = [];
          this.totalCount = 0;
          this.finished = true;
        }
      } catch (error) {
        console.error('搜索失败:', error);
        this.$toast('搜索失败，请重试');
        this.searchResults = [];
        this.finished = true;
      } finally {
        this.loading = false;
      }
    },
    
    highlightKeyword(text) {
      if (!text || !this.searchKeyword.trim()) return text;

      const keyword = this.searchKeyword.trim();
      const regex = new RegExp(`(${keyword})`, 'gi');
      return text.replace(regex, '<span class="highlight">$1</span>');
    },

    getActivityStatusText(status) {
      const statusMap = {
        'not_started': '未开始',
        'ongoing': '进行中',
        'ended': '已结束',
        '0': '未开始',
        '1': '进行中',
        '2': '已结束'
      };
      return statusMap[status] || '未知状态';
    },

    getStatusClass(status) {
      const statusMap = {
        'not_started': 'not-started',
        'ongoing': 'ongoing',
        'ended': 'ended',
        '0': 'not-started',
        '1': 'ongoing',
        '2': 'ended'
      };
      return statusMap[status] || 'not-started';
    },

    goToActivityDetail(activity) {
      this.$router.push(`/activity/${activity.id}`);
    }
  }
};
</script>

<style lang="less" scoped>
.search-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f8f8;
}

.search-header {
  background: white;
  
  .search-input-wrapper {
    padding: 0 16px 12px;
  }
}

.search-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.search-history, .hot-search {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  
  h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
  }
  
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .history-tags, .hot-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .van-tag {
      cursor: pointer;
    }
  }
}

.search-results {
  .result-stats {
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #666;
  }
  
  .activity-item {
    display: flex;
    background: white;
    border-radius: 8px;
    margin-bottom: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .activity-image {
      width: 120px;
      height: 120px;
      object-fit: cover;
    }
    
    .activity-content {
      flex: 1;
      padding: 12px;
      display: flex;
      flex-direction: column;
      
      .activity-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        line-height: 1.4;
        
        :deep(.highlight) {
          background: #fff2e8;
          color: #fa8c16;
        }
      }
      
      .activity-meta {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        
        .activity-status {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          
          &.not-started {
            background: #f0f0f0;
            color: #666;
          }
          
          &.ongoing {
            background: #e8f5e8;
            color: #52c41a;
          }
          
          &.ended {
            background: #fff2e8;
            color: #fa8c16;
          }
        }
        
        .activity-time {
          font-size: 12px;
          color: #999;
        }
      }
      
      .activity-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .activity-participants, .activity-location {
          font-size: 12px;
          color: #666;
        }
      }
      
      .activity-description {
        font-size: 13px;
        color: #999;
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        
        :deep(.highlight) {
          background: #fff2e8;
          color: #fa8c16;
        }
      }
    }
  }
}
</style>
