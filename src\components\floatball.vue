<!--
 * @Author: your name
 * @Date: 2024-12-26 10:03:55
 * @LastEditTime: 2024-12-31 19:02:06
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: In User Settings Edit
 * @FilePath: \h5-interhosp\src\components\floatball.vue
-->
<template>
    <div
        class="float-window"
        ref="floatWindow"
        :style="{ left: left + 'px', top: top + 'px' }"
    >
        <div
            class="jia"
            @touchstart="onTouchStart"
            @touchmove="onTouchMove"
            @touchend="onTouchEnd"
            @click="jumpBall"
            ref="jia"
            v-if="ballShow"
        >
            <img class="jia" :src="imgUrl" alt="" />
        </div>
    </div>
</template>

<script>
import { queryBanner } from "@/api/api";
import tools from "@/util/tools";

export default {
    props: {
        fathername: {
            type: String,
            default: () => "jigou",
        },
    },
    data() {
        return {
            imgUrl: require("../images/float/open.png"),
            startX: 0,
            startY: 0,
            offsetX: 0,
            offsetY: 0,
            left: 0,
            fixedLeft: 0,
            top: 400,
            isScrolling: false,
            rotate: {},
            ballShow: false,
            itemData: "",
        };
    },
    mounted() {
        console.log("innerWidth", window.innerWidth);
        console.log("innerWidth", this.$refs.floatWindow.offsetWidth);
        this.fixedLeft =
            window.innerWidth - this.$refs.floatWindow.offsetWidth - 55;
        this.left = this.fixedLeft;
        this.top =
            window.innerHeight -
            document.querySelector(".float-window").clientHeight -
            180;
        this.listT();
    },
    methods: {
        listT() {
            let channel = "6";
            if (
                window.localStorage.getItem("interHosp_origin") == "jktwxMini"
            ) {
                // 在健康通微信小程序内
                channel = 4;
                console.log("4444");
            } else if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                channel =
                    sessionStorage.getItem("hlw_remoteChannel") == "xhmhMini"
                        ? "10"
                        : "5";
                console.log("5555");
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_zheliban_H5"
            ) {
                channel = 3;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5"
            ) {
                // 市民卡，医疗健康
                channel = 2;
            }
            queryBanner({
                appType: "5",
                channel,
                floatAdPositionName: this.fathername,
            }).then((res) => {
                console.log("悬浮球出参", res);
                console.log("悬浮球出参第一个", res[0]);
                if (res.length) {
                    this.ballShow = true;
                    this.imgUrl = res[0].iconUrl;
                    this.itemData = res[0];
                }
            });
        },
        jumpBall() {
            tools.jumpUrlManage(this.itemData);
        },
        onTouchStart(e) {
            this.startX = e.touches[0].clientX;
            this.startY = e.touches[0].clientY;
            this.offsetX = this.left;
            this.offsetY = this.top;
            this.isScrolling = false;
        },
        onTouchMove(e) {
            this.status = "close";
            const x = e.touches[0].clientX - this.startX + this.offsetX;
            const y = e.touches[0].clientY - this.startY + this.offsetY;
            const maxX =
                window.innerWidth - this.$refs.floatWindow.offsetWidth - 50;
            const maxY =
                window.innerHeight - this.$refs.floatWindow.offsetHeight;
            this.left = x;
            this.top = y < 0 ? 0 : y > maxY ? maxY : y;
            if (
                Math.abs(e.touches[0].clientX - this.startX) > 5 ||
                Math.abs(e.touches[0].clientY - this.startY) > 5
            ) {
                this.isScrolling = true;
            }
        },
        onTouchEnd(e) {
            this.left = this.fixedLeft;
        },
    },
};
</script>

<style lang="less" scoped>
.float-window {
    // width: 1.5rem;
    height: 0.47rem;
    position: fixed;
    display: flex;
    .jia {
        width: 0.47rem;
        height: 0.47rem;
        background: 100% 100% no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
