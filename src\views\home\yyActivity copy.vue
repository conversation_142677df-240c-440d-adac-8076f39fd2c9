<!--
 * @Author: your name
 * @Date: 2025-05-28 09:33:20
 * @LastEditTime: 2025-05-28 10:27:46
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: In User Settings Edit
 * @FilePath: \h5-interhosp\src\views\home\yyActivity copy.vue
-->
<!-- 活动承载页 -->
<template>
    <div style="height: 100%; overflow-y: scroll">
        <div class="cont">
            <div class="ModContainer" v-if="list.length > 0">
                <!-- 配置展台 -->
                <div
                    class="Mod-common"
                    v-for="(listItem, index) in list"
                    :key="index"
                >
                    <!-- 活动页图片 -->
                    <!-- 子展台类别名称：活动页图片 -->
                    <div
                        v-if="
                            listItem.stageTypeName == '活动页图片' &&
                            listItem.childStageList &&
                            listItem.childStageList.length > 0
                        "
                    >
                        <div
                            class="Mod-item hdItem"
                            v-for="(item, index) in listItem.childStageList"
                            :key="index"
                            @click="
                                jumpUrl(
                                    item,
                                    listItem.childStageId,
                                    listItem.childStageName
                                )
                            "
                        >
                            <img v-lazy="item.iconUrl" alt="" />
                        </div>
                    </div>
                    <div
                        v-if="
                            listItem.stageTypeName.indexOf('三联展台') > -1 &&
                            listItem.childStageList &&
                            listItem.childStageList.length > 0
                        "
                        class="Mod"
                    >
                        <div
                            class="Mod-item"
                            v-for="(item, index) in listItem.childStageList"
                            :key="index"
                            @click="
                                jumpUrl(
                                    item,
                                    listItem.childStageId,
                                    listItem.childStageName
                                )
                            "
                        >
                            <img class="ModHead" v-lazy="item.iconUrl" alt="" />
                            <div>
                                <p>{{ item.applicationName }}</p>
                                <p class="sub">{{ item.applicationSubName }}</p>
                            </div>
                            <div
                                class="divider"
                                v-if="
                                    index != listItem.childStageList.length - 1
                                "
                            ></div>
                        </div>
                    </div>
                    <div
                        v-if="
                            listItem.stageTypeName.indexOf('滚动展台') > -1 &&
                            listItem.list &&
                            listItem.list.length > 0
                        "
                        v-show="listItem.list.length > 0"
                        class="Mod-stage"
                    >
                        <van-swipe
                            class="my-swipe"
                            :loop="false"
                            indicator-color="#01CDA7"
                        >
                            <van-swipe-item
                                v-for="(list, index) in listItem.list"
                                :key="index"
                            >
                                <div
                                    class="stageList"
                                    v-for="(item, index) in list"
                                    :key="index"
                                >
                                    <stage-item :data="item"></stage-item>
                                </div>
                            </van-swipe-item>
                        </van-swipe>
                    </div>

                    <div
                        v-if="
                            listItem.stageTypeName.indexOf('多行应用') > -1 &&
                            listItem.childStageList &&
                            listItem.childStageList.length > 0
                        "
                    >
                        <div class="Mod-stage pd-b0">
                            <div
                                class="stageList"
                                v-for="(
                                    itemin, index
                                ) in listItem.childStageList"
                                :key="index"
                            >
                                <stage-item
                                    :data="itemin"
                                    :stageId="stageId"
                                    :childStageId="rollChildStageId"
                                    position="首页"
                                ></stage-item>
                            </div>
                        </div>
                    </div>
                    <div
                        v-if="
                            listItem.stageTypeName.indexOf('banner') > -1 &&
                            listItem.childStageList &&
                            listItem.childStageList.length > 0
                        "
                    >
                        <van-swipe
                            :autoplay="3000"
                            :loop="true"
                            indicator-color="#fff"
                            v-if="
                                listItem.childStageList &&
                                listItem.childStageList.length > 0
                            "
                        >
                            <van-swipe-item
                                v-for="(
                                    itemin, index
                                ) in listItem.childStageList"
                                :key="index"
                            >
                                <img
                                    class="banner"
                                    :src="itemin.iconUrl"
                                    @click="
                                        jumpUrl(
                                            itemin,
                                            listItem.childStageId,
                                            listItem.childStageName
                                        )
                                    "
                                    alt=""
                                />
                            </van-swipe-item>
                        </van-swipe>
                    </div>
                    <!-- 宣传展台区 -->
                    <div
                        v-if="
                            listItem.stageTypeName.indexOf('宣传应用') > -1 &&
                            listItem.childStageList &&
                            listItem.childStageList.length >= 3
                        "
                        style="
                            display: flex;
                            flex-wrap: wrap;
                            align-items: flex-start;
                            justify-content: space-evenly;
                        "
                    >
                        <div
                            class="stageList"
                            style="
                                width: 1.58rem;
                                height: 1.28rem;
                                margin-top: 0.1rem;
                                margin-bottom: 0.1rem;
                            "
                        >
                            <img
                                style="width: 100%"
                                v-lazy="listItem.childStageList[0].iconUrl"
                                @click="
                                    jumpUrl(
                                        itlistItemm.childStageList[0],
                                        listItem.childStageId,
                                        listItem.childStageName
                                    )
                                "
                                alt=""
                            />
                        </div>
                        <div
                            class="stageList"
                            style="
                                width: 1.58rem;
                                height: 1.28rem;
                                margin-top: 0.1rem;
                                margin-bottom: 0.1rem;
                                display: flex;
                                flex-direction: column;
                                justify-content: space-between;
                            "
                        >
                            <img
                                style="width: 100%"
                                v-lazy="listItem.childStageList[1].iconUrl"
                                @click="
                                    jumpUrl(
                                        listItem.childStageList[1],
                                        listItem.childStageId,
                                        listItem.childStageName
                                    )
                                "
                                alt=""
                            />
                            <img
                                style="width: 100%"
                                v-lazy="listItem.childStageList[2].iconUrl"
                                @click="
                                    jumpUrl(
                                        listItem.childStageList[2],
                                        listItem.childStageId,
                                        listItem.childStageName
                                    )
                                "
                                alt=""
                            />
                        </div>
                    </div>
                    <!-- 十字格？？ -->
                    <div
                        v-if="
                            listItem.stageTypeName.indexOf('十字格') > -1 &&
                            listItem.childStageList &&
                            listItem.childStageList.length > 0
                        "
                        style="
                            display: flex;
                            flex-wrap: wrap;
                            align-items: flex-start;
                            justify-content: space-evenly;
                        "
                    >
                        <div
                            class="stageList"
                            v-for="(innerEle, index) in listItem.childStageList"
                            :key="index"
                            style="
                                width: 1.58rem;
                                height: 0.64rem;
                                margin-top: 0.1rem;
                                margin-bottom: 0.1rem;
                            "
                        >
                            <img
                                style="width: 100%"
                                v-lazy="innerEle.iconUrl"
                                @click="
                                    jumpUrl(
                                        innerEle,
                                        listItem.childStageId,
                                        listItem.childStageName
                                    )
                                "
                                alt=""
                            />
                        </div>
                    </div>
                </div>

                <!-- 找医生模块--暂时不匹配 -->
                <!-- <div class="Mod-common">
                    <find></find>
                </div> -->
            </div>
        </div>

        <pop v-if="showPop" :position="'活动页'" :dialogInfo="dialogInfo"></pop>
    </div>
</template>

<script>
import Vue from "vue";
import common from "@/util/common";
import tools from "@/util/tools";
import { Icon, Swipe, SwipeItem, Tab, Tabs, Search, Empty } from "vant";
Vue.use(Icon)
    .use(Swipe)
    .use(SwipeItem)
    .use(Tab)
    .use(Tabs)
    .use(Search)
    .use(Empty);

import { getHospList, queryAllStagetData, queryBanner } from "../../api/api";

import HospitalItem from "./components/hospitalItem.vue";
import StageItem from "./components/stageItem.vue";
import Find from "./components/find.vue";
import Pop from "@/components/popUP.vue";
// import Scrollver from "./components/scrollver.vue";

export default {
    name: "Home",
    data() {
        return {
            list: [],
            tf: false,
            per: 0,
            tHeight: 0,
            statusBarHeight: 0,
            titleBarHeight: 0,
            rollChildStageId: "",
            // xcList: [],
            hospList: [],
            // mutiRList: [],
            // 父展台
            stageId: "",
            // 子展台
            childStageId: "",
            operateStartTime: new Date().getTime(),
            hospList: [],
            // bannerList: [],
            stageList: [],
            thStageList: [],
            orgId: "",
            showPop: false,
            dialogInfo: {},
            topBg: "",
            emptyImg: require("@/images/search/no-data.png"),
            searchValue: "",
            activeName: "",
            stageName: "",
        };
    },
    components: {
        StageItem,
        HospitalItem,
        Find,
        Pop,
        // Scrollver,
    },
    computed: {
        isWjAliMini() {
            return this.$store.state.isWjAliMini;
        },
    },
    created() {},
    mounted() {
        // debugger;

        let stageName = common.getUrlParam("stageName") || "活动模板1";
        let region = 1;
        console.log("getChannel方法");
        // 2 市民卡 6 支付宝
        let channel = this.getChannel() || "2";
        let data = {
            stageName,
            channel,
            region,
        };
        queryAllStagetData(data).then((response) => {
            if (response && response.length > 0) {
                debugger;
                // 处理滚动展台数据
                // stageTypeName.indexOf('滚动展台')
                response[0].stageList.filter((item) => {
                    if (item.stageTypeName.indexOf("滚动展台") > -1) {
                        item.list = this.chunkArray(item.childStageList, 10);
                        debugger;
                    }
                });
                this.list = response[0].stageList;
                this.stageId = response[0].stageId;
                this.stageName = response[0].stageName;

                this.debugger;
            }
        });

        queryBanner({
            channel,
            appType: "7",
            floatAdPositionName: stageName,
        }).then((response) => {
            console.log(response);
            if (response && response.length > 0) {
                this.dialogInfo = response[0];
                this.showPop = true;
            }
        });
    },

    methods: {
        chunkArray(array, size) {
            return Array.from(
                { length: Math.ceil(array.length / size) },
                (_, index) => array.slice(index * size, index * size + size)
            );
        },
        getChannel() {
            // channel 代表所属渠道  微信和支付宝都是6
            // 1健康通 2市民卡 3浙里办 4 金投云健康小程序 5互联网医院， 6支付宝小程序 7 干部之家， 直接返回数字
            var ua = window.navigator.userAgent.toLowerCase();
            if (ua.indexOf("alipay") > -1) {
                return "6";
            } else if (
                ua.indexOf("dtdreamweb") > -1 ||
                common.getUrlParam("origin") === "zheliban"
            ) {
                return "3";
            } else if (window.navigator.userAgent.indexOf("smkVersion") > -1) {
                return "2";
            } else {
                return "6";
            }
        },

        compare(property) {
            return function (a, b) {
                var value1 = a[property];
                var value2 = b[property];
                return value1 - value2;
            };
        },
        tabChange(name, title) {
            console.log(name, title);
            this.getHospListFun();
        },

        jumpUrl(item, id, name) {
            console.log("跳转参数", item);
            if (item.jumpUrl) {
                tools
                    .handleSetPoint({
                        stageId: this.stageId,
                        childStageId: id,
                        stageAppId: item.applicationId,
                        trackingContent: `${this.stageName}-${name}-${item.elementContent}`,
                        businessName: "activity",
                    })
                    .then(() => {
                        tools.jumpUrlManage(item);
                    });
            }
        },
        callPhone(phone) {
            window.location.href = `tel:${phone}`;
        },
    },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less" scoped>
.topFixed {
    position: fixed;
    width: 100%;
    // height: 90px;
    padding-left: 0.35rem;
    font-size: 18px;
    box-sizing: border-box;
    // line-height: 1.5rem;
    background-color: #fff;
    z-index: 9;
}
.Mod-top {
    width: 100%;
    min-height: 237px;
    // background-image: url("@/images/img/headbg2.png");
    background-size: 100% auto;
    background-repeat: no-repeat;
    /* display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column; */
}
.wjBgImg {
    background-image: url("@/images/img/wjTopSs.jpg");
}
.bgImg {
    background-image: url("@/images/img/topBS.jpg");
}
.Mod-top-img {
    width: 100%;
    background-size: 100% auto;
}

.ModContainer {
    // margin-top: -129px;
    background-color: transparent;
    position: relative;
}

.ModContainer_wj {
    margin-top: -124px;
    background-color: transparent;
    position: relative;
}
.searchCont {
    min-height: 35px;
    margin: 0 16px 12px;
    /* background-image: url('@/images/img/search.png'); */
    /* background-size: 100% auto; */
}
.searchCont-img {
    width: 100%;
    background-size: 100% auto;
}
.Mod {
    background: #ffffff;
    margin: 0 16px;
    /* border-radius: 8px; */
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20px 0 15px;
    border-bottom: 1px solid #f1f1f1;
}
.Mod-item {
    font-size: 0;
    text-align: center;
    position: relative;
    flex: 1;
    flex-shrink: 0;
}
/* .Mod :last-child::after {
  display: none;
}
.Mod-item::after {
  content: "";
  width: 0.5px;
  height: 38px;
  background: #f1f1f1;
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
} */
.divider {
    content: "";
    width: 1px;
    height: 38px;
    background: #f1f1f1;
    position: absolute;
    right: 1px;
    top: 50%;
    transform: translateY(-50%);
}

.flex {
    height: 40px;
    margin: 20px 0;
    padding: 0 16px;
    flex: 1;
}
.flex_ {
    margin: 0.2rem 0rem 0.2rem 0.15rem;
    /* padding-right: 0.1rem; */
    width: 1.46rem;
    height: 0.48rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.yygh {
    background-image: url("@/images/img/yygh.png");
}
.bgcx {
    background-image: url("@/images/img/bgcx.png");
}

.ModHead {
    width: 55px;
    background-size: 100% auto;
    margin-bottom: 9px;
}

.border-right {
    border-right: 1px solid #ececec;
    /* margin-right: 0.1rem; */
}

.Mod p {
    font-size: 16px;
    font-family: "Source Han Sans SC";
    font-weight: 500;
    color: #333333;
    line-height: 1;
}

.Mod .sub {
    font-size: 12px;
    font-family: "Source Han Sans SC";
    font-weight: 400;
    color: #999999;
    margin-top: 6px;
    line-height: 1;
}

.Mod-stage {
    display: flex;
    // align-items: center;
    align-items: flex-start;
    flex-wrap: wrap;
    padding: 15px 10px 8px;
}
.pd-b0 {
    padding-bottom: 0;
}
.stageList {
    flex-shrink: 0;
    width: 20%;
    margin-bottom: 15px;
}

.Mod-common {
    //margin: 12px 16px;
    background: #ffffff;
    //border-radius: 8px;
}
.Mod-hospList {
    margin: 12px 16px;
    border-radius: 8px;
}
.hosp-item-container {
    /* height: calc(100vh - 506px);
        overflow: scroll; */
    margin-bottom: 76px;
    // margin-top: 8px;
    padding-top: 12px;
    background: #f5f5f5;
}
.hosp-item-container > div {
    background: #f5f5f5;
}
.hospList-top {
    background-color: #fff;
    border-radius: 8px;
}
.hosp-item-container_wj {
    margin-bottom: 30px;
    padding-top: 12px;
}

.hosp-item-container > div:first-child {
    // border-top: none !important;
}

.hospList-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 16px 0;
    margin-bottom: 5px;
}

.hospList-title p {
    font-size: 18px;
    font-family: "PingFang SC";
    font-weight: 500;
    color: #333333;
}

.more {
    font-size: 12px !important;
    font-family: "PingFangSC";
    font-weight: 400 !important;
    color: #999999 !important;
}

.more img {
    width: 4px;
    height: 8px;
    margin-left: 7px;
    object-fit: fill;
}

.fixed-login {
    height: 50px;
    background: rgba(20, 31, 53, 0.8);
    padding: 0 16px;
    z-index: 999;
    bottom: 101px;
    left: 0;
    right: 0;
    position: fixed;
}

.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.fixed-login p {
    font-size: 16px;
    font-family: "PingFang SC";
    font-weight: 500;
    color: #ffffff;
}

.login-btn {
    width: 80px;
    height: 28px;
    line-height: 28px;
    background: #ffffff;
    border-radius: 14px;
    font-size: 12px;
    font-family: "PingFang SC";
    font-weight: 400;
    color: #376af5;
    text-align: center;
}

.getMore {
    height: 45px;
    line-height: 45px;
    // border-top: 1px solid #f1f1f1;
    text-align: center;
    color: #3ebfa0;
    /* color: var(--primary-color); */
    font-size: 12px;
}
.getMore img {
    width: 4px;
    height: 8px;
    margin-left: 7px;
    object-fit: fill;
}
.banner {
    width: 100%;
    display: block;
}
.my-swipe {
    width: 100%;
}
.my-swipe .van-swipe-item {
    display: flex;
    flex-wrap: wrap;
    /* justify-content: space-between; */
}
::v-deep .my-swipe .van-swipe__indicators {
    bottom: 0;
}
::v-deep .my-swipe .van-swipe__indicator {
    width: 10px;
    height: 4px;
    border-radius: 2px;
    background-color: #dbfff8;
    opacity: 1;
}
::v-deep .my-swipe .van-swipe__indicator--active {
    width: 20px;
    height: 4px;
    border-radius: 2px;
}
::v-deep .my-swipe .van-swipe__indicator:not(:last-child) {
    margin-right: 0;
}
::v-deep .van-empty__image {
    width: 107px;
    height: 120px;
    img {
        background-size: 100% auto;
    }
}
.bottomText {
    text-align: center;
    margin-bottom: 0.85rem;
    color: #999999;
    font-size: 0.12rem;
}
.search-input {
    width: 100%;
    height: 0.55rem;
    background: #ffffff;
    position: relative;
    .s-div {
        & > img {
            position: absolute;
            &:nth-of-type(1) {
                width: 0.13rem;
                height: 0.14rem;
                top: 0.2rem;
                left: 0.32rem;
            }
            &:nth-of-type(2) {
                width: 0.15rem;
                height: 0.15rem;
                top: 0.2rem;
                right: 0.7rem;
            }
        }
        span {
            font-size: 0.15rem;
            font-weight: 400;
            color: #3ebfa0;
            position: absolute;
            right: 0.16rem;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    .s-input {
        width: 2.7rem;
        box-sizing: border-box;
        position: absolute;
        height: 0.33rem;
        border-radius: 0.14rem;
        background-color: #f5f5f5;
        font-size: 0.12rem;
        color: #363636;
        padding-left: 0.34rem;
        padding-right: 0.34rem;
        margin-right: 0.16rem;
        margin-left: 0.16rem;
        top: 0.11rem;
        border: none;
    }
    // IOS下移除原生样式
    -webkit-appearance: none;
    // 自定义placeholder颜色和字号
    input::-webkit-input-placeholder {
        font-size: 0.13rem;
        font-family: PingFang SC;
        font-weight: 400;
        color: #999999;
    }
    // 不显示搜索标识，自行添加搜索放大镜
    input[type="search"] {
        -webkit-appearance: none;
    }
    [type="search"]::-webkit-search-decoration {
        display: none;
    }
    // 不显示清空按钮，自行添加input后面的x清空文本
    input::-webkit-search-cancel-button {
        display: none;
    }
}
::v-deep .van-tabs__line {
    width: 0.3rem;
    border-radius: 0;
    background-color: #3ebfa0;
}
::v-deep .van-tabs {
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    border-radius: 8px;
    overflow: hidden;
    z-index: 1;
}
.phone {
    display: inline-block;
    margin-top: 8px;
    color: #3ebfa0;
}

.hdItem {
    width: 100%;
    img {
        // max-width: 100%;
        width: 100%;
    }
}
</style>
