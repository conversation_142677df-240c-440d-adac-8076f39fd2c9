<!--
 * @Author: your name
 * @Date: 2025-03-19 09:52:48
 * @LastEditTime: 2025-03-19 09:52:49
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: In User Settings Edit
 * @FilePath: \h5-interhosp\src\components\floatWindow copy.vue
-->
<!--Author: 郭元扬
ProjectDescription: 悬浮窗
CreateTime: 2023-06-16
UpdateTime: 2023-06-16-->

<template>
    <div
        class="float-window"
        ref="floatWindow"
        :style="{ left: left + 'px', top: top + 'px' }"
    >
        <div class="menu" ref="menu">
            <div v-show="status === 'open'">
                <img
                    :src="require(`@/images/float/${item.name}.png`)"
                    alt=""
                    v-for="item of this.tabs"
                    :key="item.name"
                    @click="jump(item)"
                />
            </div>
        </div>
        <div
            class="jia"
            @touchstart="onTouchStart"
            @touchmove="onTouchMove"
            @touchend="onTouchEnd"
            @click="changStatus"
            ref="jia"
        >
            <img class="jia" :src="imgUrl" alt="" />
        </div>
    </div>
</template>

<script>
export default {
    props: {
        active: {
            type: String,
            default: () => "jigou",
        },
    },
    data() {
        return {
            imgUrl: require("../images/float/open.png"),
            startX: 0,
            startY: 0,
            offsetX: 0,
            offsetY: 0,
            left: 0,
            fixedLeft: 0,
            top: 400,
            isScrolling: false,
            status: "close",
            rotate: {},
            tabs: [
                {
                    name: "jigou",
                    path: "/hosporg",
                },
                {
                    name: "xiaoxi",
                },
                {
                    name: "geren",
                    path: "/mine",
                },
            ],
        };
    },
    mounted() {
        this.fixedLeft =
            window.innerWidth - this.$refs.floatWindow.offsetWidth - 12;
        this.left = this.fixedLeft;
        this.top =
            window.innerHeight -
            document.querySelector(".float-window").clientHeight -
            60;
        for (const item of this.tabs) {
            if (item.name === this.active) {
                item.name = `${this.active}2`;
            }
        }
    },
    methods: {
        jump(val) {
            if (val.name === "xiaoxi") {
                this.$router.push({
                    path: "/talk",
                    query: {
                        orgId: window.localStorage.getItem("orgId"),
                    },
                });
                // if (window.localStorage.getItem("unicode") == "12330100470116681D") {
                //   window.location.href = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=Messagelist&source=jinTou-zfb-sz&uInfo=${window.localStorage.getItem(
                //     "encrUser"
                //   )}`;
                // } else if (
                //   window.localStorage.getItem("unicode") == "12330100470116614F"
                // ) {
                //   window.location.href = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=Messagelist&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                //     "encrUser"
                //   )}`;
                // } else {
                //   window.location.href = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=Messagelist&source=jinTou-zfb-hlw&uInfo=${window.localStorage.getItem(
                //     "encrUser"
                //   )}`;
                // }
            } else {
                // this.$router.push({
                //   path: val.path,
                // });
                this.$emit("jump", val.name);
            }
        },
        onTouchStart(e) {
            this.startX = e.touches[0].clientX;
            this.startY = e.touches[0].clientY;
            this.offsetX = this.left;
            this.offsetY = this.top;
            this.isScrolling = false;
        },
        onTouchMove(e) {
            this.status = "close";
            const x = e.touches[0].clientX - this.startX + this.offsetX;
            const y = e.touches[0].clientY - this.startY + this.offsetY;
            const maxX =
                window.innerWidth - this.$refs.floatWindow.offsetWidth - 12;
            const maxY =
                window.innerHeight - this.$refs.floatWindow.offsetHeight;
            const minX = -this.$refs.menu.offsetWidth;
            this.left = x < minX ? minX : x > maxX ? maxX : x;
            this.top = y < 0 ? 0 : y > maxY ? maxY : y;
            if (
                Math.abs(e.touches[0].clientX - this.startX) > 5 ||
                Math.abs(e.touches[0].clientY - this.startY) > 5
            ) {
                this.isScrolling = true;
            }
        },
        onTouchEnd(e) {
            this.left = this.fixedLeft;
            // if (!this.isScrolling) {
            //   if (e.changedTouches[0].clientX < window.innerWidth / 2) {
            //     this.left = 0;
            //   } else {
            //     // this.left = window.innerWidth - this.$refs.floatWindow.offsetWidth;
            //     // console.log(this.left, "this.left2");
            //   }
            // }
        },
        changStatus() {
            if (this.status === "open") {
                this.status = "close";
            } else {
                this.status = "open";
            }
        },
    },
    watch: {
        status(val) {
            if (val === "close") {
                this.imgUrl = require("../images/float/open.png");
            } else if (val === "open") {
                this.imgUrl = require("../images/float/fold.png");
            }
        },
    },
};
</script>

<style lang="less" scoped>
.float-window {
    width: 2.55rem;
    height: 0.47rem;
    position: fixed;
    display: flex;
    /* transform: translate3d(0, 0, 0); */
    /* transition: left 0.1s ease-out; */
    .menu {
        width: 1.96rem;
        height: 0.47rem;
        margin-right: 0.12rem;
        div {
            width: 1.96rem;
            height: 0.47rem;
            background: url(../images/float/dise.png) 100% 100% no-repeat;
            background-size: 100% 100%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            img {
                width: 0.43rem;
                height: 0.33rem;
            }
        }
    }
    .jia {
        width: 0.47rem;
        height: 0.47rem;
        background: 100% 100% no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
