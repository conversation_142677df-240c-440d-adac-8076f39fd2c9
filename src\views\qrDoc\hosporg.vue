<!--
 * @Author: your name
 * @Date: 2025-01-08 16:21:50
 * @LastEditTime: 2025-01-13 10:05:30
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 机构主页二码合一，二维码过渡页
 * @FilePath: \h5-interhosp\src\views\qrDoc\hosporg.vue
-->
<template>
    <div class="cont_">
        <div v-if="wxshow">微信小程序跳转中...</div>
        <div v-if="alishow">支付宝小程序跳转中...</div>
        <div v-if="othershow">请用支付宝或者微信扫码</div>
    </div>
</template>

<script>
import { getHospUrl } from "@/api/qrDoc";
import common from "@/util/common";
import util from "@/util/util.js";
export default {
    data() {
        return {
            wxshow: false,
            alishow: false,
            othershow: false,
            unicode: "",
        };
    },

    mounted() {
        // this.jumping();
        if (!common.getUrlParam("unicode")) {
            util.openDialogAlert("", "缺少必要参数，请检查链接是否正确~");
            return;
        }
        this.unicode = decodeURIComponent(common.getUrlParam("unicode"));
        console.log("unicode", this.unicode);
        console.log(
            "浏览器",
            navigator.userAgent.includes("MicroMessenger") ||
                navigator.userAgent.includes("Alipay")
        );
        if (
            navigator.userAgent.includes("MicroMessenger") ||
            navigator.userAgent.includes("Alipay")
        ) {
            console.log("268339");
            this.getUrl(this.unicode);
        } else {
            console.log("789999");
            this.othershow = true;
        }
    },

    methods: {
        getUrl(unicode) {
            getHospUrl(unicode).then((res) => {
                console.log("获取链接", res);
                let ua = navigator.userAgent;
                if (ua.includes("MicroMessenger")) {
                    this.wxshow = true;
                    setTimeout(function () {
                        window.location.href = res.wxURL;
                    }, 1000);
                } else if (ua.includes("Alipay")) {
                    this.alishow = true;
                    //支付宝
                    let url = res.alipayURL;
                    window.location.replace(url);
                } else {
                    this.othershow = true;
                }
            });
        },
        jumping() {
            let ua = navigator.userAgent;
            if (ua.includes("MicroMessenger")) {
                this.wxshow = true;
                setTimeout(function () {
                    window.location.href =
                        "weixin://dl/business/?appid=wx7f3513f8c7d02674&path=pages/wxQdy/wxQdy&query=authStatus%3D0%26returnURL%3Dhttps%253A%252F%252Fwww.hfi-health.com%253A28181%252Fh5InterHosp%252F%2523%252Fhosporg%253Funicode%253D12330100470116614F&env_version=release";
                }, 1000);
            } else if (ua.includes("Alipay")) {
                this.alishow = true;
                //支付宝
                let url =
                    `https://ds.alipay.com/?scheme=` +
                    encodeURIComponent(
                        "alipays://platformapi/startapp?appId=2021002138635948&page=pages/index/index?authStatus%3D2%26returnURL%3Dhttps%253A%252F%252Fwww.hfi-health.com%253A28181%252Fh5InterHosp%252F%2523%252Fhosporg%253Funicode%253D12330100563047232C"
                    );
                window.location.replace(url);
            } else {
                this.othershow = true;
            }
        },
    },
};
</script>

<style lang="less" scoped>
.cont_ {
    padding-top: 40vh;
    background-color: #fff;
    height: 60vh;
    div {
        text-align: center;
        font-size: 0.2rem;
    }
}
</style>