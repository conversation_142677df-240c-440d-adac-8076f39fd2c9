{"name": "interhosp", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "test": "vue-cli-service build --mode test", "build": "vue-cli-service build --mode pro", "build.prepro": "vue-cli-service build --mode preprod", "stabletest": "vue-cli-service build --mode stabletest", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^1.4.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "js-audio-recorder": "^1.0.7", "js-md5": "^0.7.3", "lamejs": "^1.2.1", "less": "^4.1.3", "less-loader": "^11.1.2", "moment": "^2.29.4", "postcss-pxtorem": "^6.0.0", "vue": "^2.6.14", "vue-clipboard2": "^0.3.3", "vue-cookies": "^1.8.3", "vue-lazyload": "^1.3.5", "vuex": "^3.6.2", "wavesurfer.js": "^7.1.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.6", "core-js": "^3.30.2", "crypto-js": "^4.1.1", "js-sha256": "^0.9.0", "sass": "^1.91.0", "sass-loader": "^16.0.5", "vant": "^2.12.50", "vue-router": "^3.0.1", "vue-template-compiler": "^2.6.14", "webpack-zepto": "^0.0.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}