<template>
    <div class="container">
        <div class="backImg"></div>
        <div class="ModContainer">
            <div class="intro block">
                <img
                    class="headImg"
                    :src="
                        data.headerUrl
                            ? dencryptHeader(data.headerUrl)
                            : data.sexCode == '1'
                            ? require('@/images/search/man.png')
                            : require('@/images/search/woman.png')
                    "
                    alt=""
                />
                <img
                    class="focusImg"
                    v-if="focusImg"
                    @click="operateFocus"
                    :src="focusImg"
                    alt=""
                />

                <div class="summary-info">
                    <h3 class="doctor-name">{{ data.doctorName }}</h3>
                    <span class="tag" v-if="data.teamFlag != '1'">
                        {{ data.levelName }}
                    </span>
                    <p class="depart">
                        {{ data.deptName }}
                    </p>
                </div>
                <div class="organize" v-if="data.hospName" @click="toOrgPage">
                    {{ data.hospName }}
                    <img src="@/images/img/arrow_dark.png" alt="" />
                </div>
                <div class="brief" v-if="data.remark">
                    <p class="tips">
                        专业擅长：<span>{{ data.remark }}</span>
                    </p>
                    <p class="moretxt" @click="showPopup = true">查看简介></p>
                </div>
                <div class="indic">
                    <p class="indic-f" v-if="data.favoriteQuantity != null">
                        关注
                        <span>{{
                            data.favoriteQuantity == 0
                                ? "无"
                                : data.favoriteQuantity
                        }}</span>
                    </p>
                    <p class="indic-c" v-if="data.serviceQuantity != null">
                        接诊量
                        <span>{{
                            data.serviceQuantity == 0
                                ? "无"
                                : data.serviceQuantity
                        }}</span>
                    </p>
                </div>
            </div>
            <div class="service block">
                <div class="service-func">
                    <p class="title">服务内容</p>
                    <div>
                        <div
                            class="service-func-item"
                            v-if="String(data.graphicServiceEnable) !== '0'"
                            @click="
                                jumpUrl(
                                    1,
                                    String(data.graphicServiceEnable),
                                    '图文咨询'
                                )
                            "
                        >
                            <img
                                v-if="String(data.graphicServiceEnable) === '1'"
                                src="@/images/img/doctor_twzx.png"
                                alt=""
                            />
                            <img
                                v-else
                                src="@/images/img/tuwenzhihui.png"
                                alt=""
                            />
                            <p
                                :style="{
                                    color:
                                        String(data.graphicServiceEnable) ===
                                        '1'
                                            ? '#333'
                                            : '#999',
                                }"
                            >
                                图文咨询
                            </p>
                            <p
                                style="
                                    color: #999;
                                    font-size: 11px;
                                    margin: 2px 0 3px 0;
                                "
                            >
                                {{
                                    String(data.graphicServiceEnable) === "1"
                                        ? "可立即预约"
                                        : String(data.graphicServiceEnable) ===
                                          "2"
                                        ? "不在服务时间"
                                        : "已约满"
                                }}
                            </p>
                            <p
                                class="cost"
                                v-if="data.graphicServiceFee != null"
                                :style="{
                                    color:
                                        String(data.graphicServiceEnable) ===
                                        '1'
                                            ? '#333'
                                            : '#999',
                                }"
                            >
                                {{
                                    data.graphicServiceFee == 0
                                        ? "免费"
                                        : `${data.graphicServiceFee}元${
                                              data.graphicServiceQuestionLimit
                                                  ? ""
                                                  : "/次"
                                          }`
                                }}<span
                                    class="limit"
                                    v-if="data.graphicServiceQuestionLimit"
                                    >/{{
                                        data.graphicServiceQuestionLimit
                                    }}条</span
                                >
                            </p>
                        </div>
                        <div
                            class="service-func-item"
                            v-if="
                                String(data.phoneServiceEnable) !== '0' &&
                                data.teamFlag != '1'
                            "
                            @click="
                                jumpUrl(
                                    2,
                                    String(data.phoneServiceEnable),
                                    '电话咨询'
                                )
                            "
                        >
                            <img
                                v-if="String(data.phoneServiceEnable) === '1'"
                                src="@/images/img/doctor_dhzx.png"
                                alt=""
                            />
                            <img
                                v-else
                                src="@/images/img/dianhuazhihui.png"
                                alt=""
                            />
                            <p
                                :style="{
                                    color:
                                        String(data.phoneServiceEnable) === '1'
                                            ? '#333'
                                            : '#999',
                                }"
                            >
                                电话咨询
                            </p>

                            <p
                                style="
                                    color: #999;
                                    font-size: 11px;
                                    margin: 2px 0 3px 0;
                                "
                            >
                                {{
                                    String(data.phoneServiceEnable) === "1"
                                        ? "可立即预约"
                                        : String(data.phoneServiceEnable) ===
                                          "2"
                                        ? "不在服务时间"
                                        : "已约满"
                                }}
                            </p>

                            <p
                                class="cost"
                                v-if="data.phoneServiceFee != null"
                                :style="{
                                    color:
                                        String(data.phoneServiceEnable) === '1'
                                            ? '#333'
                                            : '#999',
                                }"
                            >
                                {{
                                    data.phoneServiceFee == 0
                                        ? "免费"
                                        : `${data.phoneServiceFee}元/次`
                                }}
                            </p>
                        </div>
                        <div
                            class="service-func-item"
                            v-if="String(data.returnVisitEnable) !== '0'"
                            @click="
                                jumpUrl(
                                    3,
                                    String(data.returnVisitEnable),
                                    '在线复诊'
                                )
                            "
                        >
                            <img
                                v-if="String(data.returnVisitEnable) === '1'"
                                src="@/images/img/doctor_zxfz.png"
                                alt=""
                            />
                            <img
                                v-else
                                src="@/images/img/fuzhenzhihui.png"
                                alt=""
                            />
                            <p
                                :style="{
                                    color:
                                        String(data.returnVisitEnable) === '1'
                                            ? '#333'
                                            : '#999',
                                }"
                            >
                                复诊开方
                            </p>
                            <p
                                style="
                                    color: #999;
                                    font-size: 11px;
                                    margin: 2px 0 3px 0;
                                "
                            >
                                {{
                                    String(data.returnVisitEnable) === "1"
                                        ? "可立即预约"
                                        : String(data.returnVisitEnable) === "2"
                                        ? "不在服务时间"
                                        : "已约满"
                                }}
                            </p>
                            <p
                                class="cost"
                                v-if="data.returnVisitFee != null"
                                :style="{
                                    color:
                                        String(data.returnVisitEnable) === '1'
                                            ? '#333'
                                            : '#999',
                                }"
                            >
                                {{
                                    data.returnVisitFee == 0
                                        ? "免费"
                                        : `${data.returnVisitFee}元${
                                              data.returnVisitQuestionLimit
                                                  ? ""
                                                  : "/次"
                                          }`
                                }}<span
                                    class="limit"
                                    v-if="data.returnVisitQuestionLimit"
                                    >/{{
                                        data.returnVisitQuestionLimit
                                    }}条</span
                                >
                            </p>
                        </div>
                        <!-- 仅阿克苏医院的医生显示在线建档 -->
                        <div
                            class="service-func-item"
                            v-if="
                                String(data.unicode) == '123301004701166305aksu'
                            "
                            style="align-self: baseline"
                            @click="handleCreateRecord(data)"
                        >
                            <img
                                src="@/images/shizhong/zaixianjiandang.png"
                                alt=""
                            />
                            <p :style="{ color: '#333' }">在线建档</p>
                        </div>
                    </div>
                </div>
            </div>

            <fwb
                ref="fwb"
                :unicode="unicode"
                :deptId="deptId"
                :doctorId="doctorId"
            ></fwb>
            <!-- 推荐医生 -->
            <tjdoctor ref="tjdoctor"></tjdoctor>
            <!-- 团队成员 -->
            <team-members ref="teamMembers"></team-members>
            <div class="comment block" v-if="evaluation_list_total">
                <div class="comment-title">
                    <p>患者评价</p>
                    <p
                        class="more"
                        @click="toEvaluation"
                        v-if="evaluation_list_total > 2"
                    >
                        更多
                        <img src="@/images/img/arrow.png" alt="" />
                    </p>
                </div>
                <evaluation-list
                    :evaluation_list="evaluation_list"
                ></evaluation-list>
            </div>
        </div>
        <popup-define :show.sync="showPopup" :data="popupData"></popup-define>
        <van-overlay :show="showCoupon" :lock-scroll="false">
            <div class="couponDiv">
                <div class="couponBox">
                    <div class="itemBox">
                        <couponItem
                            v-for="(item, index) in consultList"
                            :key="index"
                            :data="item"
                        ></couponItem>
                    </div>
                    <div class="bottom">
                        <button class="btn" @click="useCoupon"></button>
                        <div class="more" @click="goCoupon"></div>
                    </div>
                </div>
            </div>

            <!-- <div>
                <p style="color: #333333;">{{ msg }}</p>
                <button class="btn"></button>
            </div> -->
        </van-overlay>
    </div>
</template>

<script>
import tools from "@/util/tools";
import { Toast } from "vant";
import PopupDefine from "./components/popupDefine.vue";
import EvaluationList from "../evaluation/components/evaluationList.vue";
import tjdoctor from "./components/tjdoctor.vue";
import teamMembers from "./components/teamMembers.vue";
import couponItem from "./components/couponItem.vue";
import fwb from "./components/fwb.vue";
import {
    doctorByUnicodeDeptIdStaffId,
    favoriteByHospitalDoctor,
    favoriteDocotor,
    cancelFavorite,
    doctorEvaluatePageList,
    creatRecord,
} from "@/api/api";
import { getCoup } from "@/api/coupon";
import common from "@/util/common";
import util from "@/util/util";
import Vue from "vue";
import { Overlay } from "vant";

Vue.use(Overlay);
export default {
    name: "Doctor",
    components: {
        PopupDefine,
        EvaluationList,
        tjdoctor,
        couponItem,
        teamMembers,
        fwb,
    },
    data() {
        return {
            // 显示优惠券弹窗
            showCoupon: false,
            hasCoupon: false, //已经领取
            activeId: "", //活动编号
            code: "", //优惠券校验
            consultList: [], //活动对应的优惠券列表
            unicode: "",
            staffId: "",
            deptId: "",
            doctorId: "",
            userId: "",
            focusFlag: "",
            focusImg: "",
            data: {},
            evaluation_list: [],
            evaluation_list_total: 0,
            showPopup: false,
            popupData: {
                title: "医生简介",
                subTitle: "专业擅长",
                intro: "",
            },
        };
    },
    created() {
        debugger;
        this.staffId = this.$route.query.staffId;
        this.deptId = this.$route.query.deptId;
        this.unicode = this.$route.query.unicode;
        if (window.localStorage.getItem("userInfo")) {
            this.uInfo = JSON.parse(window.localStorage.getItem("userInfo"));

            var userId =
                window.localStorage.getItem("userId") ||
                this.uInfo.userId ||
                "";
        }

        this.userId = userId;
        this.getDoctorData();
        this.getCouponInfo();
    },
    mounted() {
        debugger;
    },
    watch: {
        focusFlag: {
            handler(newVal) {
                console.log("wtach=====", newVal);
                if (newVal) {
                    this.focusImg = require("@/images/img/doctor_had_focus.png");
                } else {
                    this.focusImg = require("@/images/img/doctor_focus.png");
                }
            },
        },
    },
    methods: {
        // 在线建档的按钮 只有阿克苏医生进入
        handleCreateRecord(data) {
            console.log("建档参数", data);
            if (!window.localStorage.getItem("userInfo")) {
                tools.jumpUrl(window.location.href, 1);
                return;
            }

            let logTraceId = new Date().getTime();
            let userInfo = JSON.parse(window.localStorage.getItem("userInfo"));
            if (
                this.unicode == "123301004701166305aksu" &&
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                // 阿克苏且微信端 强制自费
                this.handleCreateRecordZF(data);
                return;
            }
            if (
                this.unicode == "123301004701166305aksu" &&
                navigator.userAgent.toLowerCase().indexOf("alipay") > -1
            ) {
                creatRecord({
                    logTraceId: logTraceId,
                    bizContent: {
                        reqSeq: logTraceId,
                        appId: "**********",
                        pageUrl: "2",
                        orgName: data.hospName,
                        certType: "1",
                        certCode: userInfo.paperNum,
                        name: userInfo.name,
                        phone: userInfo.phone,
                        callbackUrl: window.location.href,
                        hospOrgCode: this.data.hospOrgCode,
                        // 阿克苏查询状态时，需要增加patientFlag入参2，仅查询阿克苏的建档状态
                        // 其他医院为patientFlag=1
                        patientFlag:
                            this.unicode == "123301004701166305aksu"
                                ? "2"
                                : "1",
                    },
                })
                    .then((res) => {
                        if (String(res.buildFileStatus) !== "3") {
                            // 测试-跳转到预发
                            window.location.href =
                                "https://www.hfi-health.com:28181/" +
                                process.env.VUE_APP_RECORD +
                                "/#/?uid=" +
                                common.getUrlParam_(res.pageUrl, "uid");
                        } else {
                            Toast.fail("您已建档");
                        }
                    })
                    .catch(() => {
                        Toast.fail("获取建档地址失败");
                    });
            }
        },
        handleCreateRecordZF(data) {
            let logTraceId = new Date().getTime();
            let userInfo = JSON.parse(window.localStorage.getItem("userInfo"));
            creatRecord({
                logTraceId: logTraceId,
                bizContent: {
                    reqSeq: logTraceId,
                    appId: "**********",
                    pageUrl: "2",
                    orgName: data.hospName,
                    certType: "1",
                    certCode: userInfo.paperNum,
                    name: userInfo.name,
                    phone: userInfo.phone,
                    callbackUrl: window.location.href,
                    hospOrgCode: this.data.hospOrgCode,
                    // 阿克苏查询状态时，需要增加patientFlag入参2，仅查询阿克苏的建档状态
                    // 其他医院为patientFlag=1
                    patientFlag:
                        this.unicode == "123301004701166305aksu" ? "2" : "1",
                },
            })
                .then((res) => {
                    console.log("阿克苏建档", res);
                    // 1.未建档 2 医保已建档 自费未建档
                    if (
                        String(res.buildFileStatus) == "1" ||
                        String(res.buildFileStatus) == "2"
                    ) {
                        // 截取自费建档地址
                        let t = res.pageUrl.split("?");
                        let zfUrl =
                            "https://www.hfi-health.com:28181/" +
                            process.env.VUE_APP_RECORD +
                            "/#/zf?" +
                            t[1];
                        window.location.href = zfUrl;
                        return;
                    } else {
                        Toast.fail("您已建自费档");
                    }
                })
                .catch(() => {
                    Toast.fail("获取建档地址失败");
                });
        },
        useCoupon() {
            this.showCoupon = false;

            // 后退是否需要再次请求领券接口？
            //方案一 修改链接上的参数  replace
            // 点击更多按钮 也需要加上这个动作
            // let u = common.updateUrlParam("", "activeId", "");
            // u = common.updateUrlParam(u, "code", "");
            // window.location.replace(u);

            // 方案二 在session中插入值  请求接口前判断有无这个值 有则不请求领券接口  这个动作应该加到请求领券接口成功后；
            // window.sessionStorage.setItem("couponGot", "1");
        },
        goCoupon() {
            // window.location.href = "#/couponList";
            this.$router.push({
                path: "/couponList",
            });
        },
        getCouponInfo() {
            /* this.showCoupon = true;
            this.consultList = [
                {
                    id: "11",
                    name: "11",
                    couponName: "三八节活动三八节活动三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.01.31 19:10-2024.02.02 23:59",
                    couponType: "1",
                    minThreshold: "30",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    discount: "5",
                },
                {
                    id: "11",
                    name: "11",
                    couponName:
                        "三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.06.25至2024.08.31",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    couponType: "2",
                    discount: "5",
                },
                {
                    id: "11",
                    couponName: "三八节活动三八节活动",
                    name: "11",
                    time: "2024.06.25至2024.08.31",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    remark: "",
                    couponType: "3",
                    discount: "5",
                },
                {
                    id: "11",
                    couponName: "11",
                    remark: "ddddddddd",
                    couponType: "0",
                    discount: "5",
                },
                {
                    repeatCouponInstanceId: null,
                    instanceUseId: "2c81eda6-4704-4990-ad5b-b7784bf34fe8",
                    couponCode: "38e3e377e66511eebec0005056aff7cd",
                    activityId: 11,
                    couponName: null,
                    couponType: "0",
                    valueDesc: "满10减5",
                    minThreshold: null,
                    discount: null,
                    showStyle: null,
                    useDesc: "中医可以使用",
                    canStack: null,
                    note: "1111",
                    validType: "1",
                    startValidTime: "2024-03-30 00:00:00",
                    endValidTime: "2024-03-30 00:00:00",
                    busiType: null,
                    serviceType: null,
                    platform: "1",
                    claimUseId: null,
                    doctorId: null,
                    couponStatus: "1",
                    claimType: null,
                    usedTime: null,
                    createTime: null,
                    updeteTime: null,
                },
            ];
            return; */
            if (this.$route.query.activeId && this.$route.query.code) {
                // todo请求活动相关接口
                let data = {
                    userId: window.localStorage.getItem("userId"),
                    staffId: this.staffId,
                    deptId: this.deptId,
                    unicode: this.unicode,
                    code: this.$route.query.code,
                    activityId: this.$route.query.activeId,
                };

                getCoup(data).then((res) => {
                    // 数据处理
                    if (res.success == 1) {
                        // 渲染券列表
                        if (res.value && res.value.coupons) {
                            this.showCoupon = true;

                            this.consultList = res.value.coupons;
                        }
                    } else {
                        // 抛错处理
                        util.openDialogAlert(
                            "",
                            res.respDesc || "网络连接超时，请稍后再试~",
                            null,
                            "  ",
                            "acDialog"
                        );
                    }
                });
            }
        },
        dencryptHeader: common.dencryptHeader,
        getDoctorData() {
            let data = {
                staffId: this.staffId,
                deptId: this.deptId,
                unicode: this.unicode,
            };
            doctorByUnicodeDeptIdStaffId(data).then((res) => {
                console.log("医生详细信息=====", res);
                if (res) {
                    this.data = res;
                    this.popupData.intro = this.data.remark;
                    this.doctorId = this.data.doctorId;
                    this.favoriteByHospitalDoctor(this.doctorId);
                    this.getEvaluateList(this.doctorId, this.staffId);
                    // 团队医生 没有医生推荐
                    // 2是团队，1是非团队
                    if (res.teamFlag != 1) {
                        this.$refs.tjdoctor.getData(
                            this.unicode,
                            this.deptId,
                            this.data.doctorId
                        );
                    } else {
                        this.$refs.teamMembers.getData(res.memberList);
                        // 团队成员
                        // this.$refs.teamMembers.lists = res.memberList;
                    }

                    this.$refs.fwb.getData(
                        "",
                        this.unicode,
                        this.deptId,
                        this.doctorId
                    );
                }
            });
        },
        favoriteByHospitalDoctor(doctorId) {
            // this.userId = "d8332783-c0f5-4cd8-88c8-3538098ec243";
            let data = {
                patientId: this.userId,
                doctorId: doctorId,
                staffId: this.data.staffId,
                deptId: this.data.deptId,
                unicode: this.data.unicode,
            };
            if (this.userId) {
                favoriteByHospitalDoctor(data).then((res) => {
                    if (res) {
                        this.focusFlag = true;
                    } else {
                        this.focusFlag = false;
                    }
                });
            }
        },
        operateFocus() {
            if (this.userId) {
                if (this.focusFlag) {
                    let data = {
                        patientId: this.userId,
                        doctorId: this.data.doctorId,
                        staffId: this.data.staffId,
                        deptId: this.data.deptId,
                        unicode: this.data.unicode,
                    };
                    cancelFavorite(data).then((res) => {
                        console.log("---cancel----", res);
                        if (!res) {
                            this.focusFlag = false;
                            this.data.favoriteQuantity =
                                this.data.favoriteQuantity - 1;
                        }
                    });
                } else {
                    let data = {
                        patientId: this.userId,
                        doctorId: this.data.doctorId,
                        staffId: this.data.staffId,
                        deptId: this.data.deptId,
                        unicode: this.data.unicode,
                    };
                    favoriteDocotor(data).then((res) => {
                        console.log("-------", res);
                        if (!res) {
                            this.focusFlag = true;
                            this.data.favoriteQuantity =
                                this.data.favoriteQuantity + 1;
                        }
                    });
                }
            }
        },
        getEvaluateList(doctorId, staffId) {
            let data = {
                doctorId,
                staffId,
                isPaging: 1,
                pageNum: 1,
                pageSize: 2,
                unicode: this.data.unicode,
                isAvailable: "1",
            };
            doctorEvaluatePageList(data).then((res) => {
                console.log("评价=--==--", res);
                if (res.page) {
                    this.evaluation_list_total = res.page.total;
                    if (res.page.total > 2) {
                        this.evaluation_list = res.page.list.slice(0, 2);
                    } else {
                        this.evaluation_list = res.page.list;
                    }
                }
            });
        },
        toSearchDoctorList() {
            this.$router.push({
                path: "searchDoctorList",
                query: {},
            });
        },
        toOrgPage() {
            this.$router.push({
                path: "hosporg",
                query: {
                    unicode: this.data.unicode,
                    origin:
                        this.data.unicode == "12330100470116614F" ||
                        this.data.unicode == "2000096" ||
                        this.data.unicode == "2000006"
                            ? "szMini"
                            : "",
                    // window.localStorage.getItem("interHosp_origin"),
                },
            });
        },
        toEvaluation() {
            this.$router.push({
                path: "patientEvaluation",
                query: {
                    averageScore: this.data.averageScore,
                    doctorId: this.doctorId,
                    staffId: this.data.staffId,
                    unicode: this.data.unicode,
                },
            });
        },
        async jumpUrl(type, status, serviceName) {
            if (this.data.inBlack === "0") {
                Toast("当前医生暂不提供服务");
                return;
            }
            if (status === "3") {
                Toast(`该医生当日${serviceName}已约满`);
            } else if (status === "1") {
                const content =
                    type == 1
                        ? "图文咨询"
                        : type == 2
                        ? "电话咨询"
                        : "在线复诊";
                await tools.handleSetPoint({
                    trackingContent: `${this.data.doctorName}|${content}`,
                    orgId: this.data.unicode,
                    orgName: this.data.hospName,
                });
                // type 1 图文 2电话 3 复诊
                // internetBuilder 3 在线咨询、在线复诊的链接配置成我们的 否则跳转纳里链接
                var url = "";
                // 支付宝
                let isZfb = navigator.userAgent.indexOf("AliApp") > -1;
                // 微信
                let isWeChat =
                    navigator.userAgent
                        .toLowerCase()
                        .indexOf("micromessenger") > -1;
                // 浙里办
                let isZLB =
                    navigator.userAgent.toLowerCase().indexOf("dtdreamweb") >
                        -1 ||
                    sessionStorage.getItem("hlw_remoteChannel") ==
                        "health_zheliban_H5";

                if (this.data.unicode == "2000096") {
                    if (type == 1) {
                        if (this.data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=imageConsultApply&staffId=${this.data.staffId}&deptId=${this.data.deptId}&unicode=${this.data.unicode}`;
                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=imageConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=imageConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=imageConsultApply&did=${this.data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else if (type == 2) {
                        if (this.data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=mobileConsultApply&staffId=${this.data.staffId}&deptId=${this.data.deptId}&unicode=${this.data.unicode}`;
                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=mobileConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=mobileConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=mobileConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=mobileConsultApply&did=${this.data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else {
                        if (this.data.internetBuilder == "3") {
                            url = `${location.origin}/${process.env.VUE_APP_FOLLOW}/#/furConsultConfirm?unicode=${this.data.unicode}&staffId=${this.data.staffId}&deptId=${this.data.deptId}&isTeamOrder=${this.data.teamFlag}`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=askDoctorApply&did=${this.data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=askDoctorApply&did=${this.data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=askDoctorApply&did=${this.data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    }
                    tools.jumpUrl(url);
                } else if (
                    this.data.unicode == "2000006" ||
                    this.data.unicode == "12330100470116614F"
                ) {
                    if (type == 1) {
                        if (this.data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=imageConsultApply&staffId=${this.data.staffId}&deptId=${this.data.deptId}&unicode=${this.data.unicode}`;
                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=imageConsultApply&did=${this.data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else if (type == 2) {
                        if (this.data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=mobileConsultApply&staffId=${this.data.staffId}&deptId=${this.data.deptId}&unicode=${this.data.unicode}`;
                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=mobileConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=mobileConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=mobileConsultApply&did=${this.data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=mobileConsultApply&did=${this.data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else {
                        if (this.data.internetBuilder == "3") {
                            url = `${location.origin}/${process.env.VUE_APP_FOLLOW}/#/furConsultConfirm?unicode=${this.data.unicode}&staffId=${this.data.staffId}&deptId=${this.data.deptId}&isTeamOrder=${this.data.teamFlag}`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=askDoctorApply&did=${this.data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=askDoctorApply&did=${this.data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=askDoctorApply&did=${this.data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    }
                    tools.jumpUrl(url);
                } else {
                    if (type == 1) {
                        // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=imageConsultApply&did=${this.data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                        if (this.data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=imageConsultApply&staffId=${this.data.staffId}&deptId=${this.data.deptId}&unicode=${this.data.unicode}`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=imageConsultApply&did=${this.data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=${this.data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=imageConsultApply&did=${this.data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else if (type == 2) {
                        if (this.data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=mobileConsultApply&staffId=${this.data.staffId}&deptId=${this.data.deptId}&unicode=${this.data.unicode}`;

                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=mobileConsultApply&did=${this.data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=mobileConsultApply&did=${this.data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=mobileConsultApply&did=${this.data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=mobileConsultApply&did=${this.data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else {
                        if (this.data.internetBuilder == "3") {
                            url = `${location.origin}/${process.env.VUE_APP_FOLLOW}/#/furConsultConfirm?unicode=${this.data.unicode}&staffId=${this.data.staffId}&deptId=${this.data.deptId}&isTeamOrder=${this.data.teamFlag}`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=askDoctorApply&did=${this.data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=askDoctorApply&did=${this.data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=askDoctorApply&did=${this.data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    }
                    tools.jumpUrl(url, "2");
                }
            }
        },
    },
};
</script>

<style lang="less" >
.acDialog {
    font-size: 0.18rem;
    width: 75%;
    background: url(../../images/coupon/tipBg.png) no-repeat center;
    background-size: cover;
    .van-dialog__message {
        font-size: 0.18rem;
    }
    .van-dialog__footer {
        justify-content: center;
        bottom: 0.2rem;
    }
    .van-dialog__confirm {
        width: 1.85rem;
        height: 0.45rem;
        background: url(../../images/coupon/tipBtn.png) no-repeat;
        background-size: 100%;
        flex: none;
    }
}
</style>
<style lang="less" scoped>
.container {
    font-family: "PingFang SC";

    .backImg {
        height: 237px;
        background: url("@/images/img/com_back.png");
        background-size: 100% auto;
        background-repeat: no-repeat;
    }

    .ModContainer {
        margin-top: -207px;
        background-color: transparent;
    }

    .block {
        margin: 12px 15px;
        background: #ffffff;
        border-radius: 8px;
    }

    .intro {
        padding: 15px;
        position: relative;

        .headImg {
            position: absolute;
            left: 15px;
            top: -20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-size: 100% 100%;
        }

        .focusImg {
            position: absolute;
            right: 12px;
            top: 15px;
            width: 58px;
            height: 24px;
            background-size: 100% 100%;
        }

        .summary-info {
            margin-top: 40px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            font-weight: 400;

            .doctor-name {
                font-size: 21px;
                font-weight: 500;
                color: #333333;
                margin-right: 12px;
            }

            .tag {
                font-size: 14px;
                color: #333333;
                margin-right: 12px;
            }

            .depart {
                font-size: 14px;
                color: #333333;
            }
        }

        .organize {
            font-size: 14px;
            font-weight: 400;
            color: #333333;
            margin-bottom: 12px;

            img {
                width: 5px;
                height: 9px;
                background-size: 100%;
                margin-left: 8px;
            }
        }

        .brief {
            position: relative;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            position: relative;
            font-size: 13px;
            font-weight: 400;

            .tips {
                color: #333333;
            }

            span {
                color: #777777;
            }

            .moretxt {
                position: absolute;
                right: 0;
                bottom: 0;
                display: inline-block;
                color: #3ebfa0;
                background: #fff;
                padding: 0 6px;
                box-shadow: -10px 12px 10px 9px #ffffff;
            }
        }

        .indic {
            display: flex;
            align-items: center;
            font-size: 11px;
            font-weight: 400;
            color: #888888;
            line-height: 1;
            margin-top: 15px;

            span {
                color: #3ebfa0;
                font-size: 15px;
                font-weight: 500;
            }

            &-f {
                padding-right: 8px;
                border-right: 1px solid #f1f1f1;
            }

            &-c {
                padding: 0 8px;
            }
        }
    }

    .service {
        &-func {
            padding: 15px;

            .title {
                font-size: 18px;
                font-weight: 500;
                color: #000000;
                line-height: 1;
                margin-bottom: 15px;
            }

            > div {
                display: flex;
                align-items: center;
            }

            &-item {
                text-align: center;
                width: 33.33%;
                flex-shrink: 0;

                img {
                    width: 45px;
                    height: 45px;
                    background-size: 100% 100%;
                }

                p {
                    font-size: 14px;
                    font-weight: 500;
                    color: #333333;
                }

                .cost {
                    font-size: 12px;
                    font-weight: 400;
                    color: #999999;
                }
            }
        }
    }

    .comment {
        padding: 15px;

        &-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;

            p {
                font-size: 18px;
                font-weight: 500;
                color: #333333;
            }

            .more {
                font-size: 12px;
                font-weight: 400;
                color: #999999;
            }

            .more img {
                width: 4px;
                height: 8px;
                margin-left: 7px;
                object-fit: fill;
            }
        }

        :last-child {
            border-bottom: none;
        }
    }

    // 优惠券弹窗
    .couponDiv {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        .couponBox {
            width: 2.95rem;
            max-height: 4.66rem;
            position: relative;
            background: url("../../images/coupon/coupBoxBg.png") no-repeat
                center top;
            background-size: cover;
            background-color: #e8fbf2;
            // margin: 10% auto;
            border-radius: 0.12rem;
            overflow: hidden;
            .itemBox {
                margin-top: 1rem;
                padding-bottom: 1.45rem;
                overflow-y: scroll;
                max-height: 2.21rem;
            }
            .bottom {
                position: absolute;
                bottom: 0;
                left: 0;
                background: url("../../images/coupon/botBg.png") no-repeat
                    center;
                background-size: 100%;
                width: 100%;
                height: 1.45rem;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                .btn {
                    width: 1.85rem;
                    height: 0.45rem;
                    background: url("../../images/coupon/docBtn.png") no-repeat
                        center;
                    background-size: 100%;
                    margin-top: 0.15rem;
                }
                .more {
                    width: 1.1rem;
                    height: 0.14rem;
                    background: url("../../images/coupon/couMore.png") no-repeat
                        center;
                    background-size: 100%;
                    margin-top: 0.15rem;
                }
            }
        }
    }
    .limit {
        font-size: 12px !important;
        color: #888888 !important;
    }
}
</style>
