<template>
  <div class="msgBoxWrap" v-if="visible">
    <div class="mask"></div>
    <div class="msgBox">
      <div class="msgBoxMain">
        <div class="msgBoxBody">
          <div class="msgBoxTitle" v-if="title">
            <p>
              <span>{{ title }}</span>
            </p>
          </div>
          <div class="msgBoxCont">
            <p>{{ content }}</p>
            <slot name="content"></slot>
          </div>
        </div>
        <div class="msgBoxBtn">
          <button
            type="button"
            v-if="confirmTxt"
            class="confirm"
            @click="confirmFn"
          >
            {{ confirmTxt }}
          </button>
          <button
            type="button"
            v-if="cancelTxt"
            class="cancel"
            @click="cancelFn"
          >
            {{ cancelTxt }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    content: {
      type: String,
      default: "",
    },
    confirmTxt: {
      type: String,
      default: "确定",
    },
    cancelTxt: {
      type: String,
      default: "取消",
    },
  },
  data() {
    return {
      vis: this.visible,
    };
  },
  methods: {
    open() {
      this.vis = true;
    },
    close() {
      this.vis = false;
    },
    confirmFn() {
      this.$emit("update:visible", false);
      this.$emit("confirm");
      this.close();
    },
    cancelFn() {
      this.$emit("update:visible", false);
      this.$emit("cancel");
      this.close();
    },
  },
};
</script>
<style lang="less" scoped>
.msgBoxWrap {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: transparent;
  z-index: 1000;
  .mask {
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: 0.6;
  }
  .msgBox {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    width: calc(100% - 85px);
    min-height: 200px;
    background-color: #fff;
    border-radius: 16px;
    overflow: hidden;
  }

  .msgBoxMain {
    padding: 29px 0;
  }

  .msgBoxTitle {
    width: 100%;
    margin-bottom: 9px;

    p {
      text-align: center;
      font-size: 16px;
      color: #333;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .msgBoxCont {
    font-size: 13px;
    color: #666666;
    text-align: center;
    p {
      white-space: break-spaces;
      span {
        padding: 0 24px;
        display: inline-block;
        white-space: break-spaces;
        word-break: break-word;
      }
    }
  }

  .msgBoxBtn {
    margin-top: 30px;
    button {
      height: 40px;
      line-height: 40px;
      font-size: 15px;
      text-align: center;
      width: 185px;
      display: block;
      margin: 0 auto;
      background: #fff;
    }
    .confirm {
      font-weight: 500;
      color: #01cda7;
      border: 2px solid #01cda7;
      border-radius: 20px;
      margin-bottom: 8px;
    }
    .cancel {
      color: #333333;
    }
  }
}
</style>
