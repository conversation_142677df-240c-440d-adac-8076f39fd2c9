<!--
 * @Author: your name
 * @Date: 2024-12-09 09:48:20
 * @LastEditTime: 2024-12-17 15:00:06
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 带角标banner
 * @FilePath: \h5-interhosp\src\components\unite\angleBanner.vue
-->
<template>
    <div class="miyt" v-if="bannerlist.length !== 0">
        <img class="angleIcon" :src="iconUrl" alt="" />
        <div class="swiper">
            <van-swipe
                :autoplay="3000"
                @change="changeItem"
                indicator-color="#01CDA7"
            >
                <van-swipe-item
                    v-for="(item, index) in bannerlist"
                    :key="index"
                >
                    <!-- <img class="angleIcon" src="https://jsbceshi.hfi-health.com:18188/appsImg/1732174396907.png" alt="" /> -->

                    <img
                        class="icon"
                        v-lazy="item.iconUrl"
                        :key="item.iconUrl"
                        alt=""
                        @click="go(item, index)"
                        :data-exposure-id="elementId"
                        :data-exposure-content="item.elementContent"
                        :data-exposure-sn="index + 1"
                    />
                </van-swipe-item>
            </van-swipe>
        </div>
    </div>
</template>
    <script>
import tools from "@/util/tools.js";
import Vue from "vue";
import { Swipe, SwipeItem } from "vant";
Vue.use(Swipe).use(SwipeItem);
export default {
    components: {
        "mt-swipe": Swipe,
        "mt-swipe-item": SwipeItem,
    },
    props: {
        datalists: {
            type: Object,
        },
    },
    data() {
        return {
            elementId: "",
            bannerlist: "",
            iconUrl: "",
        };
    },
    created() {},
    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        // if (temp.stageTypeName === '带角标banner') {
        this.elementId = temp.elementId;
        if (temp.childStageList.length && temp.childStageList[0].angleIconUrl) {
            // 只有一张banner图的时候不会触发changeItem方法
            this.iconUrl = temp.childStageList[0].angleIconUrl;
        }
        this.bannerlist = temp.childStageList;
        // }
    },
    methods: {
        changeItem(index) {
            //   每一页轮播结束后触发
            // index index, 当前页的索引
            this.iconUrl = this.datalists.childStageList[index].angleIconUrl;
        },
        go(item, index) {
            console.log("角标banneritem", item);
            // 将所属子展台的市民卡埋点id，赋值给点击的某项，在jumurl跳转方法里判断是否触发市民卡点击埋点
            item.elementId = this.elementId;
            item.index = index;
            tools.jumpUrlManage(item);
        },
    },
};
</script>
    <style scoped lang="less">
.miyt {
    width: 3.75rem;
    //   height: 0.875rem;
    margin: 0 auto;
    margin-bottom: 0.1rem;
    .angleIcon {
        position: absolute;
        top: 0;
        left: 0;
        display: inline-block;
        z-index: -1;
        width: 100vw;
        // height: 2.55rem;
    }
    /deep/.swiper {
        width: 100%;
        height: 100%;
        border-radius: 0.08rem;
        overflow: hidden;
        // position: relative;
        // z-index: 1;
        transform: rotate(
            0deg
        ); // 上面的position和z-index的效果和这个一样，为了解决overflow:hidden失效问题
        .icon {
            width: 100%;
            height: 100%;
        }
        .icon[lazy="loading"] {
            display: block;
            width: 20%;
            line-height: 100%;
            margin: auto;
        }

        .mint-swipe-indicators {
            bottom: 0;
        }
        .mint-swipe-indicator.is-active {
            background-color: #01cda7;
        }
        .van-swipe__indicator {
            opacity: 1;
            background-color: #e5e5e4;
            width: 0.06rem;
            height: 0.06rem;
        }
    }
}
</style>
    