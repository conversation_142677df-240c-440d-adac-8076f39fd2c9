<template>
    <div class="mainCont">
        <div class="Mod-common">
            <van-cell
                title="地址管理"
                @click="jumpUrl(addrUrl, '2', 'addressManagemen', '地址管理')"
            >
                <template #icon>
                    <img
                        class="leftimg"
                        src="@/images/img/setaddr.png"
                        alt=""
                    />
                </template>
                <template #right-icon>
                    <img
                        class="arrow"
                        src="@/images/img/arrow_dark.png"
                        alt=""
                    />
                </template>
            </van-cell>
        </div>
        <div class="Mod-common">
            <van-cell
                title="优化建议"
                @click="jumpUrl(feedbackUrl, '0', 'my_feedback', '优化建议')"
            >
                <template #icon>
                    <img
                        class="leftimg"
                        src="@/images/img/question.png"
                        alt=""
                    />
                </template>
                <template #right-icon>
                    <img
                        class="arrow"
                        src="@/images/img/arrow_dark.png"
                        alt=""
                    />
                </template>
            </van-cell>
        </div>
        <!-- 市民卡不显示政策隐私 -->
        <div v-if="!isSmk" class="Mod-common">
            <van-cell
                title="政策隐私"
                @click="
                    jumpUrl(
                        privacyUrl,
                        '0',
                        'setting-policyPrivacy',
                        '政策隐私'
                    )
                "
            >
                <template #icon>
                    <img
                        class="leftimg"
                        src="@/images/img/sprivacy.png"
                        alt=""
                    />
                </template>
                <template #right-icon>
                    <img
                        class="arrow"
                        src="@/images/img/arrow_dark.png"
                        alt=""
                    />
                </template>
            </van-cell>
        </div>
        <div v-show="isJktWx" class="Mod-common">
            <van-cell title="注销登录" @click="zhuxiaoDialog">
                <template #icon>
                    <img
                        class="leftimg"
                        src="@/images/img/zhuxiao.png"
                        alt=""
                    />
                </template>
            </van-cell>
        </div>
        <div v-show="loginOutShow" class="Mod-common">
            <van-cell title="退出登录" @click="loginOut">
                <template #icon>
                    <img class="leftimg" src="@/images/img/logout.png" alt="" />
                </template>
                <!-- <template #right-icon>
          <img class="arrow" src="@/images/img/arrow_dark.png" alt="" />
        </template> -->
            </van-cell>
        </div>
        <!-- <div class="Mod-common">
      <van-cell title="问题反馈" @click="jumpUrlRoute()">
        <template #icon>
          <img class="leftimg" src="@/images/img/sfeedback.png" alt="" />
        </template>
        <template #right-icon>
          <img class="arrow" src="@/images/img/arrow_dark.png" alt="" />
        </template>
      </van-cell>
    </div> -->
    </div>
</template>

<script>
import Vue from "vue";
import { CellGroup, Cell } from "vant";
Vue.use(CellGroup).use(Cell);
import tools from "@/util/tools";
import util from "@/util/util.js";
import { LogoutUser } from "@/api/api";

export default {
    name: "Setting",
    components: {},
    data() {
        return {
            // privacyUrl: "https://www.hfi-health.com:28181/interHosp/#/ysxy",
            privacyUrl:
                "https://www.hfi-health.com:28181/agreement/yszc_ihosp.html",
            jktPrivacyUrl:
                "https://www.hfi-health.com:28181/agreement/ysxy.html",
            // addrUrl: "https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=DeliveryAddressList&source=jinTou-zfb-hlw&uInfo=",
            addrUrl:
                location.origin + `/${process.env.VUE_APP_PHA}/#/address/list`,
            feedbackUrl:
                location.origin + `/${process.env.VUE_APP_FOLLOW}/#/question`,
            loginOutShow: false,
            isSmk: false,
            isJktWx: false,
        };
    },
    created() {
        tools.handleSetPoint({
            trackingContent: "我的|设置",
        });
        if (window.navigator.userAgent.indexOf("smkVersion") > -1) {
            this.isSmk = true;
        }
        if (window.localStorage.getItem("interHosp_origin") == "jktwxMini") {
            this.isJktWx = true;
        }
    },
    mounted() {
        if (
            navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 ||
            (navigator.userAgent.indexOf("AliApp") > -1 &&
                window.localStorage.getItem("interHosp_origin") != "szMini")
        ) {
            this.loginOutShow = true;
        }
    },
    methods: {
        jumpUrl(a, b, applicationCode, applicationName) {
            // 后两个参数是友盟埋点
            let that = this;
            tools
                .handleSetPoint({
                    trackingContent: `我的|设置|${applicationName}`,
                })
                .then(() => {
                    if (applicationName == "政策隐私") {
                        if (
                            window.localStorage.getItem("interHosp_origin") ==
                            "jktwxMini"
                        ) {
                            a = that.jktPrivacyUrl;
                        }
                    }
                    tools.jumpUrl(a, b);
                });
        },
        jumpUrlRoute() {
            this.$router.push({
                path: "/feedback",
            });
        },
        loginOut() {
            // 退出登录
            util.loginOut();
        },
        zhuxiaoDialog() {
            let that = this;
            // 取消与确认的按钮对调，绿色高亮在取消文字上
            this.$dialogBox({
                title: "是否注销当前账户",
                content: "",
                confirmTxt: "取消",
                cancelTxt: "确认",
                cancelCallback: function () {
                    // 其实是确认文字触发的
                    that.zhuxiao();
                },
                confirmCallback: function () {
                    // 其实是 取消文字触发的
                },
            });
        },
        zhuxiao() {
            // 注销账户
            LogoutUser().then((res) => {
                console.log("注销接口成功", res);
                // 接口成功，才能跳转
                if (res.success && res.success == 1) {
                    window.localStorage.clear();
                    window.sessionStorage.clear();
                    console.log("注销成功");
                    // 清除后，需跳转到小程序页面，小程序页面缓存也要清除
                    let setTime = setInterval(() => {
                        console.log("跳转到首页");
                        clearInterval(setTime);
                        wx.miniProgram.navigateTo({
                            url: "/pages/wxLogOut/wxLogOut?type=zhuxiao",
                        });
                    }, 500);
                }
            });
        },
    },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.mainCont {
    padding-top: 5px;
}
.Mod-common {
    margin: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
}
.leftimg {
    width: 25px;
    height: 25px;
    background-size: 100% auto;
    margin-right: 15px;
}
.arrow {
    width: 7px;
    height: 12px;
    object-fit: fill;
}
::v-deep .van-cell {
    align-items: center;
    padding: 25px 16px;
}
::v-deep .van-cell__title {
    font-size: 15px;
    font-family: "PingFang SC";
    font-weight: 400;
    color: #141f35;
}
</style>
