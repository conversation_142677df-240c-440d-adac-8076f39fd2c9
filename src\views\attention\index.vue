<!--
 * @Author: shenpp
 * @Date: 2023-06-14
 * @LastEditTime: 2024-08-19 17:44:38
 * @Description: 我关注的医生
-->

<template>
    <div class="attentionCon">
        <van-list
            v-model="loading"
            :finished="finished"
            finished-text=""
            @load="getlist"
        >
            <ul v-if="attentions && attentions.length !== 0">
                <li
                    @click="goDoctor(item)"
                    v-for="(item, index) in attentions"
                    :key="index"
                >
                    <img
                        class="hdImg"
                        v-lazy="
                            item.headerUrl
                                ? dencryptHeader(item.headerUrl)
                                : item.sexCode == '1'
                                ? require('@/images/search/man.png')
                                : require('@/images/search/woman.png')
                        "
                    />
                    <div class="content">
                        <div class="item1">
                            <div>{{ item.doctorName }}</div>
                            <!-- 团队标识 0-医生 1-团队    团队不需要显示 职称 -->
                            <div
                                class="levelName"
                                v-show="item.teamFlag != '1'"
                            >
                                {{ (item.teamFlag, item.levelName) }}
                            </div>
                            <!-- <div>
                {{item.state=='0'? '已关注': '关注' }}
                </div> -->
                            <img
                                @click="changeState($event, item)"
                                :src="
                                    item.state == 0
                                        ? require('@/images/search/follow.png')
                                        : require('@/images/search/followed.png')
                                "
                            />
                        </div>
                        <div class="item2">
                            <!-- <div>{{ item.hospLevel }}</div> -->
                            <div class="item2_1" v-if="item.hospTypeCode">
                                {{ item.hospTypeCode | getHospitalType }}
                            </div>
                            <div class="item2_2">{{ item.hospName }}</div>
                            <div class="item2_3">{{ item.deptName }}</div>
                        </div>
                        <div v-if="item.remark" class="item3">
                            擅长:{{ item.remark }}
                        </div>
                        <div class="item4">
                            <!-- <div>
                咨询量<span>{{ item.consultQuantity ? item.consultQuantity: '无' }}</span>
              </div> -->
                            <div>
                                接诊量<span>{{
                                    item.serviceQuantity
                                        ? item.serviceQuantity
                                        : "无"
                                }}</span>
                            </div>
                            <div>
                                评价<span>{{
                                    item.averageScore
                                        ? item.averageScore + "分"
                                        : "无"
                                }}</span>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
            <div v-else class="noAttention">
                <img src="@/images/img/noAttention.png" />
                <p>暂无关注医生</p>
            </div>
        </van-list>
        <!-- <van-empty v-else :image="require('@/images/img/noAttention.png')" description="暂无关注医生" /> -->
    </div>
</template>
  
  <script>
import {
    favoritePageList,
    favoriteDocotor,
    cancelFavorite,
} from "@/api/api.js";
import tools from "@/util/tools.js";
import common from "@/util/common";
import { List } from "vant";
export default {
    components: {
        [List.name]: List,
    },
    filters: {
        getHospitalType(value) {
            let methods = "";
            switch (value) {
                case "3":
                    methods = "市属公立";
                    break;
                case "6":
                    methods = "区县";
                    break;
                case "5":
                    methods = "社区";
                    break;
                default:
                    methods = "其他";
                    break;
            }
            return methods;
        },
    },
    data() {
        return {
            /* attentions2: [
          {
            imgurl: require("@/images/evaluationUser.png"),
            docName: "杨丽娜",
            docType: "主任医师",
            hospType: "三甲",
            hospName: "杭州市第一人民医院",
            deptName: "心血管科门诊",
            dec: "擅长：擅长治疗心血管系统的冠心病、高血压 病、心肌炎等疾病，有丰富的临床经验。",
            zxNum: "6578",
            jzNum: "9078",
            rateNum: "9分",
            state: "0",
          },
          {
            imgurl: require("@/images/evaluationUser.png"),
            docName: "杨丽娜",
            docType: "主任医师",
            hospType: "三甲",
            hospName: "杭州市第一人民医院",
            deptName: "心血管科门诊",
            dec: "擅长：擅长治疗心血管系统的冠心病、高血压 病、心肌炎等疾病，有丰富的临床经验。",
            zxNum: "6578",
            jzNum: "9078",
            rateNum: "9分",
            state: "1",
          },
          {
            imgurl: require("@/images/evaluationUser.png"),
            docName: "杨丽娜",
            docType: "主任医师",
            hospType: "三甲",
            hospName: "杭州市第一人民医院",
            deptName: "心血管科门诊",
            dec: "擅长：擅长治疗心血管系统的冠心病、高血压 病、心肌炎等疾病，有丰富的临床经验。高血压 病、心肌炎等疾病，有丰富的临床经验",
            zxNum: "6578",
            jzNum: "9078",
            rateNum: "9分",
            state: "0",
          },
          {
            imgurl: require("@/images/evaluationUser.png"),
            docName: "杨丽娜",
            docType: "主任医师",
            hospType: "三甲",
            hospName: "杭州市第一人民医院",
            deptName: "心血管科门诊",
            dec: "擅长：擅长治疗心血管系统的冠心病、高血压 病、心肌炎等疾病，有丰富的临床经验。",
            zxNum: "6578",
            jzNum: "9078",
            rateNum: "9分",
            state: "0",
          },
          {
            imgurl: require("@/images/evaluationUser.png"),
            docName: "杨丽娜",
            docType: "主任医师",
            hospType: "三甲",
            hospName: "杭州市第一人民医院",
            deptName: "心血管科门诊",
            dec: "擅长：擅长治疗心血管系统的冠心病、高血压 病、心肌炎等疾病，有丰富的临床经验。",
            zxNum: "6578",
            jzNum: "9078",
            rateNum: "9分",
            state: "0",
          },
        ], */
            // attentions1: "",
            attentions: [],
            userId: "",
            finished: false,
            loading: false,
            pageNum: 1,
            origin: "",
        };
    },

    mounted() {
        var uInfo = JSON.parse(window.localStorage.getItem("userInfo")) || "";
        // this.userId = uInfo ? uInfo.userId : "";
        this.userId = window.localStorage.getItem("userId") || uInfo.userId;
        console.log("4444", this.userId);
        this.origin =
            this.$route.query.origin ||
            window.localStorage.getItem("interHosp_origin");
    },

    methods: {
        dencryptHeader: common.dencryptHeader,
        getlist() {
            let data = {
                patientId: this.userId || window.localStorage.getItem("userId"),
                isPaging: 1,
                pageNum: this.pageNum,
                pageSize: 10,
            };
            favoritePageList(data).then((res) => {
                console.log("关注列表", res);
                if (res) {
                    this.loading = false;
                    res.list.forEach((element) => {
                        element.state = 1;
                    });
                    let List = res.list;
                    this.attentions = this.attentions.concat(List);
                    if (this.pageNum >= res.pages) {
                        this.finished = true;
                    } else {
                        this.pageNum++;
                    }
                }
            });
        },
        // 改变关注状态
        changeState(event, item) {
            // 阻止冒泡
            event.stopPropagation();
            console.log("某个关注", item);
            let patientId = this.userId;
            let deptId = item.deptId;
            let doctorId = item.doctorId;
            let staffId = item.staffId;
            let unicode = item.unicode;
            // 为1 是已关注，改变后是调用取消关注，改为未关状态
            let cancel = {
                patientId,
                staffId,
                deptId,
                doctorId,
                unicode,
            };
            let yes = {
                patientId,
                staffId,
                deptId,
                doctorId,
                unicode,
            };
            if (item.state) {
                cancelFavorite(cancel).then((res) => {
                    item.state = 0;
                    console.log("取消关注", res);
                });
            } else {
                // 为0 是未关注状态，改变后是调用关注接口，改为已关注状态
                favoriteDocotor(yes).then((res) => {
                    item.state = 1;
                    console.log("关注医生", res);
                });
            }
        },
        // 跳转医生详情
        goDoctor_(item) {
            // 关注市中的医生后 在自己平台下点击医生主页  跳转到市中小程序
            if (!item.miniId) {
                this.$router.push({
                    path: "/doctor",
                    query: {
                        staffId: item.staffId,
                        deptId: item.deptId,
                        unicode: item.unicode,
                    },
                });
            } else {
                //
                my.postMessage({
                    action: "gotoMini",
                    appId: item.miniId,
                    url: item.jumpUrl,
                    authStatus: "2",
                });
            }
        },
        goDoctor(item) {
            let url =
                location.origin +
                location.pathname +
                `#/doctor?staffId=${item.staffId}&deptId=${item.deptId}&unicode=${item.unicode}`;
            // "#/doctor?staffId=" +
            // item.staffId +
            // "&deptId=" +
            // item.deptId;
            console.log("医生跳转链接", item, this.$store.state);
            // 暖心助孕市中主页进来直接跳转本项目医生主页地址

            if (this.origin == "szMini") {
                tools.jumpUrl(url, 2);
                return;
            }
            if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_zheliban_H5"
            ) {
                // 浙里办跳转
                window.location.href = url;
                return;
            }
            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                // 微信小程序
                this.gotoWX(item, url);
                return;
            }

            if (this.$store.state.isWjAliMini) {
                // 卫健小程序入口进来
                // 1.医生miniId为空,跳健康自建小程序医生主页
                if (!item.miniId) {
                    let jumpUrl = "";
                    if (item.clientId) {
                        let tempUrl =
                            item.jumpUrl.indexOf("?") > -1
                                ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                                : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                        // 小程序跳转链接拼接
                        jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            tempUrl
                        )}`;
                    } else {
                        jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            url
                        )}`;
                    }
                    console.log("医生跳转链接", jumpUrl);
                    my.postMessage({
                        action: "gotoMini",
                        appId: "2021002138635948",
                        url: jumpUrl,
                        authStatus: "2",
                    });
                } else {
                    // 医生miniId不为空，跳第三方链接
                    my.postMessage({
                        action: "gotoMini",
                        appId: item.miniId,
                        url: item.jumpUrl,
                        authStatus: "2",
                    });
                }
            } else {
                // 自建小程序入口进来
                // 医生miniId为空
                if (!item.miniId) {
                    if (item.clientId) {
                        // 有商户号  需要拿到userid后，调用auth接口跳转
                        url =
                            item.jumpUrl.indexOf("?") > -1
                                ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                                : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                    }
                    // 原医生跳转
                    // tool.jumpUrl(url, "2")
                    item.status = "2";
                    item.jumpUrl = url;
                    tools.jumpUrlManage(item);
                } else {
                    // 医生miniId不为空，跳第三方链接
                    my.postMessage({
                        action: "gotoMini",
                        appId: item.miniId,
                        url: item.jumpUrl,
                        authStatus: "2",
                    });
                }
            }
        },
        gotoWX(item, url) {
            if (!item.miniIdWx) {
                // 没有小程序id 区分卫健和自建平台
                // 自建平台
                if (item.clientId) {
                    // 2.没有miniid，有clientid
                    let url =
                        item.jumpUrl.indexOf("?") > -1
                            ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                            : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                }
                // 1.无miniid 无clientid 直接跳转 自己页面的医生主页
                item.status = "2";
                item.jumpUrl = url;
                tools.jumpUrlManage(item);
            } else {
                //  有miniid  3 4 在卫健与自建平台是一致情况
                // if (item.unicode) {
                // 3.有miniid   有unicode 跳转对应小程序拼接参数

                // 微信小程序 市一市中模式不需要考虑

                item.jumpUrlWx =
                    item.jumpUrlWx.indexOf("?") > -1
                        ? `${item.jumpUrlWx}&source=miniWechat&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                        : `${item.jumpUrlWx}?source=miniWechat&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                // }
                // alert(item.jumpUrl)
                item.authStatus = "2";
                tools.gotoMini(item);
            }
        },
    },
};
</script>
  
  <style lang="less" scoped>
ul {
    margin-bottom: 0.2rem;
}
li {
    display: flex;
    width: 3.19rem;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 0.08rem;
    margin-top: 0.12rem;
    padding: 0.13rem 0.15rem 0.12rem 0.11rem;
    img {
        display: inline-block;
        width: 0.47rem;
        height: 0.47rem;
        // border-radius: 50%;
    }
    .hdImg {
        border-radius: 50%;
    }
    .content {
        margin-left: 0.13rem;
        flex: 1;
        div {
            display: flex;
            align-items: center;
        }
        .item1 {
            justify-content: flex-end;
            div:first-child {
                font-size: 0.15rem;
                font-weight: bold;
                color: #333;
                flex: 1;
            }
            .levelName {
                color: #333333;
                font-size: 0.13rem;
                margin-left: 0.1rem;
                flex: 2;
            }
            // div:last-child{
            //   background-color: #F6F6F6;
            //   border-radius: 0.12rem;
            //   padding: 0.06rem 0.12rem;
            //   font-size: 0.11rem;
            //   color: #999999;
            // }
            img {
                width: 0.585rem;
                height: 0.24rem;
            }
        }
        .item2 {
            margin-top: 0.1rem;
            .item2_1 {
                font-size: 0.1rem;
                color: #3ebfa0;
                border: 0.01rem solid #3ebfa0;
                padding: 0.03rem 0.06rem;
                border-radius: 0.02rem;
            }
            .item2_2 {
                color: #686b73;
                font-size: 0.13rem;
                margin-left: 0.07rem;
                flex: 1;
                margin-right: 0.07rem;
                // 社区名称过长，省略号显示
                max-width: 1.7rem;
                word-break: break-all;
                text-overflow: ellipsis;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }
            .item2_3 {
                color: #686b73;
                font-size: 0.13rem;
            }
        }
        .item3 {
            color: #888888;
            font-size: 0.13rem;
            margin-top: 0.1rem;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            display: -moz-box;
            -moz-line-clamp: 2;
            -moz-box-orient: vertical;
            overflow-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
        }
        .item4 {
            color: #b3b5b9;
            font-size: 0.11rem;
            margin-top: 0.1rem;
            div {
                margin-right: 0.2rem;
            }
            span {
                color: #3ebfa0;
                margin-left: 0.05rem;
            }
        }
    }
}
.noAttention {
    color: #777777;
    font-size: 0.14rem;
    margin-top: 35vh;
    text-align: center;
    img {
        display: inline-block;
        width: 1.07rem;
        height: 1.2rem;
    }
    p {
        margin-top: 0.12rem;
    }
}
</style>
  