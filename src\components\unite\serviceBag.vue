<!--
 * @Author: your name
 * @Date: 2024-12-05 15:45:30
 * @LastEditTime: 2024-12-17 14:55:04
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 服务包展台，第一行一个大的，第二行3个，多余不显示
 * @FilePath: \h5-interhosp\src\components\unite\serviceBag.vue
-->

<template>
    <div class="serviceBag" v-if="isShow">
        <div class="title" v-show="title">{{ title }}</div>
        <div
            class="firstItem"
            :data-exposure-id="elementId"
            :data-exposure-content="datalists.childStageList[0].elementContent"
            :data-exposure-sn="1"
            @click="go(datalists.childStageList[0], 0)"
        >
            <img
                class="angleIcon"
                v-if="datalists.childStageList[0].angleIconUrl"
                v-lazy="datalists.childStageList[0].angleIconUrl"
                :key="datalists.childStageList[0].angleIconUrl"
            />
            <img
                class="iconUrl"
                v-lazy="datalists.childStageList[0].iconUrl"
                :key="datalists.childStageList[0].iconUrl"
            />
            <div>
                <div class="appName">
                    {{ datalists.childStageList[0].applicationName }}
                </div>
                <div class="appSubName">
                    {{ datalists.childStageList[0].applicationSubName }}
                </div>
            </div>
        </div>
        <div class="con_item">
            <div
                class="item"
                v-for="(item, index) in subLists"
                :key="index"
                :data-exposure-id="elementId"
                :data-exposure-content="item.elementContent"
                :data-exposure-sn="index + 2"
                @click="go(item, index + 1)"
            >
                <div>
                    <img
                        class="angleIcon"
                        v-if="item.angleIconUrl"
                        v-lazy="item.angleIconUrl"
                        :key="item.angleIconUrl"
                    />
                    <img
                        class="iconUrl"
                        v-lazy="item.iconUrl"
                        :key="item.iconUrl"
                    />
                    <div class="appName">{{ item.applicationName }}</div>
                    <div class="appSubName">{{ item.applicationSubName }}</div>
                </div>
            </div>
        </div>
    </div>
</template>
  
  <script>
import tools from "@/util/tools.js";
export default {
    name: "SmkhealthServicebag",
    props: {
        datalists: {
            type: Object,
        },
    },
    data() {
        return {
            isShow: false,
            title: "",
            lists: "",
            subLists: "",
            elementId: "",
        };
    },

    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("服务包1111111", temp);
        let tempArr = "";
        if (
            temp.stageTypeName === "服务包" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.title = temp.childStageName;
            this.lists = temp.childStageList;
            tempArr = temp.childStageList;
            //   删除子展台中首个应用
            this.elementId = temp.elementId;
            if (tempArr.length > 1) {
                // tempArr.shift()
                // this.subLists = tempArr
                // console.log('子数据', this.subLists)
                this.subLists = tempArr.slice(1, 4);
                console.log("子数据", this.subLists);
            }
        }
    },

    methods: {
        go(item, index) {
            console.log("服务包项", item, index);
            item.elementId = this.elementId;
            item.index = index;
            tools.jumpUrlManage(item);
        },
    },
};
</script>
  
  <style lang="less" scoped>
.serviceBag {
    width: 3.45rem;
    margin: 0 auto;
    margin-top: 0.1rem;
    border-radius: 0.08rem;
    background-color: #fff;
    margin-bottom: 0.1rem;
    padding-bottom: 0.1rem;
}
img[lazy="loading"] {
    display: block;
    width: 20%;
    line-height: 100%;
    margin: auto;
}
.title {
    font-size: 0.18rem;
    font-weight: 700;
    color: #000;
    /* height: 0.54rem;
    line-height: 0.54rem; */
    padding: 0.13rem 0.09rem;
}
.firstItem {
    display: flex;
    position: relative;
    box-shadow: 0px 0.015rem 0.08rem 0px rgba(136, 136, 136, 0.1);
    border-radius: 0.08rem;
    width: 3.19rem;
    height: 0.8rem;
    // margin: 0 auto;
    margin-left: 0.13rem;
    padding: 0.08rem 0.1rem;
    margin-bottom: 0.1rem;
    .angleIcon {
        width: 0.39rem;
        height: 0.2rem;
        position: absolute;
    }
    .iconUrl {
        width: 0.97rem;
        height: 0.64rem;
        border-radius: 0.05rem;
        margin-right: 0.14rem;
    }
    .appName {
        font-size: 0.14rem;
        color: #333;
    }
    .appSubName {
        width: 1.8rem;
        color: #a4a4a4;
        font-size: 0.12rem;
        margin-top: 0.05rem;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /* 超出几行省略 */
        overflow: hidden;
    }
}
.item {
    width: 1rem;
    height: 1.11rem;
    display: flex;
    padding: 0.06rem;
    box-shadow: 0px 0.015rem 0.08rem 0px rgba(136, 136, 136, 0.1);
    border-radius: 0.08rem;
    margin-left: 0.1rem;
    .angleIcon {
        width: 0.385rem;
        height: 0.2rem;
        position: absolute;
    }
    .iconUrl {
        width: 0.88rem;
        height: 0.57rem;
        border-radius: 0.05rem;
    }
    .appName {
        font-size: 0.12rem;
        color: #333;
    }
    .appSubName {
        color: #a4a4a4;
        font-size: 0.12rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 0.85rem;
    }
}
.con_item {
    display: flex;
    .item:first-child {
        margin-left: 0.13rem;
    }
}
</style>
  