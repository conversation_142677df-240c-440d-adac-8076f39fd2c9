<!--
 * @Author: your name
 * @Date: 2025-05-28 09:42:19
 * @LastEditTime: 2025-05-29 15:04:44
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: banner展台
 * @FilePath: \h5-interhosp\src\components\activity\banner.vue
-->
<template>
    <div v-show="isShow" style="margin-top: 10px">
        <van-swipe :autoplay="3000" :loop="true" indicator-color="#fff">
            <van-swipe-item v-for="(itemin, index) in commonlist" :key="index">
                <img
                    class="banner"
                    :src="itemin.iconUrl"
                    @click="jumpUrl(itemin, childStageId, childStageName)"
                    alt=""
                />
            </van-swipe-item>
        </van-swipe>
    </div>
</template>
<script>
import tools from "@/util/tools";
export default {
    props: {
        datalists: {
            type: Object,
        },
        stageName: {
            type: String,
        },
        stageId: {
            type: String,
        },
    },
    data() {
        return {
            commonlist: "",
            elementId: "",
            isShow: false,
            childStageId: "",
            childStageName: "",
        };
    },

    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("活动页banner", temp);
        if (
            temp.stageTypeName === "banner" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.commonlist = temp.childStageList;
            this.childStageId = temp.childStageId;
            this.childStageName = temp.childStageName;
        }
    },

    methods: {
        jumpUrl(item, id, name) {
            console.log("跳转参数", item);
            if (item.jumpUrl) {
                tools
                    .handleSetPoint({
                        stageId: this.stageId,
                        childStageId: id,
                        stageAppId: item.applicationId,
                        trackingContent: `${this.stageName}-${name}-${item.elementContent}`,
                        businessName: "activity",
                    })
                    .then(() => {
                        tools.jumpUrlManage(item);
                    });
            }
        },
    },
};
</script>

<style lang="less" scoped>
.banner {
    width: 100%;
    display: block;
}
</style>