<!--
 * @Author: your name
 * @Date: 2024-12-05 16:33:13
 * @LastEditTime: 2024-12-13 10:32:08
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 公告通知
 * @FilePath: \h5-interhosp\src\components\notice.vue
-->
!<template>
    <div v-if="this.isvalue !== 0">
        <div class="main" v-if="this.noLonger == 1">
            <van-notice-bar
                scrollable
                :text="msg"
                left-icon="volume"
                style="padding-left: 0.1rem"
            />
        </div>
    </div>
</template>
  <script>
import { NoticeBar } from "vant";
import { notice } from "@/api/api";
export default {
    components: {
        [NoticeBar.name]: NoticeBar,
    },
    data() {
        return {
            msg: "",
            isvalue: "",
            noLonger: "",
        };
    },

    created() {
        this.listT();
    },
    methods: {
        listT() {
            let channel = "6";
            if (
                window.localStorage.getItem("interHosp_origin") == "jktwxMini"
            ) {
                // 在健康通微信小程序内
                channel = 4;
                console.log("4444");
            } else if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                channel =
                    sessionStorage.getItem("hlw_remoteChannel") == "xhmhMini"
                        ? "10"
                        : "5";
                console.log("5555");
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_zheliban_H5"
            ) {
                channel = 3;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5"
            ) {
                // 市民卡，医疗健康
                channel = 2;
            }
            notice(channel).then((res) => {
                console.log("公告出参", res);
                if (res.list.length) {
                    this.noLonger = res.list[0].status;
                    if (this.isvalue !== 0 || this.noLonger == 1) {
                        this.msg = res.list[0].content;
                    }
                }
            });
        },
    },
};
</script>
  <style lang="less" scoped>
.van-notice-bar__wrap {
    font-size: 0.14rem !important;
    z-index: 100;
}
</style>
  