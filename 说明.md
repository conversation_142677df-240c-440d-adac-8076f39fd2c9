卫健场景：
1 没有配置小程序id   且 没有配clientid   跳转到我方小程序 打开对应的医院或者医生
2 没有配置小程序id   且 配clientid  这种情况有unicode   白皮书-h5 跳转到我方小程序   需走授权 拼接对方的授权回调地址
3 有配置小程序id   配置unicode   白皮书跳转-对方小程序  按白皮书拼接跳转
4 有配置小程序id  没有配置unicode  直接跳转到对方小程序

自建平台场景：
1 没有配置小程序id   且 没有配clientid   跳转我们自己的页面 打开对应的医院或者医生
2 没有配置小程序id   且 配clientid  这种情况有unicode   白皮书-h5   需走授权 跳转拼接对方的授权回调地址
3 有配置小程序id   配置unicode   白皮书跳转-对方小程序  按白皮书拼接跳转
4 有配置小程序id  没有配置unicode  直接跳转到对方小程序

3 和 4  配置了小程序id  跳转逻辑是一样的  

医生跳转 还需增加一个判断 origin=szMini   这个场景 h5方式跳转 