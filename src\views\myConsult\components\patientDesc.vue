<!--
 * @Author: shenpp
 * @Date: 2023-08-17
 * @LastEditTime: 2024-03-14
 * @Description: 咨询详情--病人详情描述
-->
<template>
  <div class="patient_desc">
    <div class="item">
      <div class="left">患者信息</div>
      <div class="right">
        <span>{{ data.patientName }}</span>
        <span>{{ data.patientGender == "2" ? "女" : "男" }}</span>
        <span>{{ data.patientAge }}岁</span>
      </div>
    </div>
    <!-- <div class="item">
      <div class="left">预约时间</div>
      <div class="right">{{ data.reservation }}</div>
    </div> -->
    <div class="item" v-if="data.busiType == 22">
      <div class="left">接听电话</div>
      <div class="right">{{ data.phoneConsultCalled }}</div>
    </div>
    <div class="item" v-if="data.busiType == 22">
      <div class="left">预约时间</div>
      <div class="right">{{ data.phoneConsultTime }}</div>
    </div>
    <!-- 图文、复诊的显示接诊时间 有接诊时间字段则显示 -->
    <div class="item" v-if="data.busiType !== 22 && data.openPeriod">
      <div class="left">预计接诊时间</div>
      <div class="right">{{ data.openPeriod }}</div>
    </div>
    <div class="item">
      <div class="left">病情描述</div>
      <div class="right desc">
        <div>
          {{ data.descriptions }}
        </div>
        <div class="record" v-if="data.voices">
          <img
            v-if="isPlay"
            src="./../../../images/img/record.gif"
            @click="playAudio"
          />
          <img
            v-else
            src="./../../../images/img/record.png"
            @click="playAudio"
          />
          <span>{{ data.voices.time }}</span>
          <audio
            ref="player"
            :src="data.voices.voice"
            controls
            style="display: none"
          >
            浏览器不支持音频播放。
          </audio>
        </div>
        <ul>
          <li
            v-for="item in data.pictures"
            :key="item.orderIllnessInfoId"
            @click="ImagePreview(item.content)"
          >
            <img :src="item.content" />
            <span>{{ item.type | descType }}</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { ImagePreview } from "vant";
export default {
  components: {
    [ImagePreview.Component.name]: ImagePreview.Component,
  },
  props: {
    data: {
      type: Object,
    },
  },
  data() {
    return {
      isPlay: false,
    };
  },
  filters: {
    descType(val) {
      let typeName = "";
      switch (val) {
        case "21":
          typeName = "门诊病历";
          break;
        case "22":
          typeName = "检验报告";
          break;
        case "23":
          typeName = "检查报告";
          break;
        case "24":
          typeName = "体检报告";
          break;
        case "25":
          typeName = "处方";
          break;
        case "26":
          typeName = "治疗记录";
          break;
        case "27":
          typeName = "住院病历";
          break;
        case "28":
          typeName = "医嘱";
          break;
        case "29":
          typeName = "医学影像";
          break;
        case "210":
          typeName = "病患部位";
          break;
        case "211":
          typeName = "出院小结";
          break;
        default:
          typeName = "其他";
          break;
      }
      return typeName;
    },
  },
  watch: {
    data: {
      handler() {},
      deep: true,
    },
  },
  mounted() {},
  methods: {
    ImagePreview(url) {
      ImagePreview({
        images: [url],
        closeable: true,
      });
    },
    playAudio() {
      this.$nextTick(() => {
        !this.isPlay ? this.$refs.player.play() : this.$refs.player.pause();
        this.isPlay = !this.isPlay;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.patient_desc {
  background-color: #ffffff;
  margin-top: 0.12rem;
  border-radius: 0.08rem;
  padding: 0.2rem 0.15rem 0;
}
.item {
  display: flex;
  margin-bottom: 0.2rem;
  .left {
    flex: 2;
    text-align: justify;
    color: #666666;
    font-size: 0.14rem;
  }
  .right {
    flex: 4;
    color: #333333;
    font-size: 0.14rem;
    span {
      margin-right: 0.15rem;
    }
  }
  .desc div {
    margin-bottom: 0.15rem;
    text-align: justify;
    word-break: break-all;
  }
}
.record {
  width: 1.89rem;
  box-shadow: 0.03rem 0.03rem 0.1rem rgba(186, 186, 186, 0.2);
  border-radius: 0.225rem;
  img {
    width: 1rem;
    height: 0.45rem;
    vertical-align: middle;
  }
  span {
    margin-left: 0.15rem;
  }
}
ul {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0.2rem;
  li {
    position: relative;
    text-align: center;
    margin-right: 0.1rem;
    margin-bottom: 0.1rem;
    overflow: hidden;
    height: 0.52rem;
  }
  li img {
    width: 0.52rem;
    height: 0.52rem;
    display: inline-block;
    border-radius: 0.04rem;
  }
  li span {
    display: inline-block;
    font-size: 0.1rem;
    transform: scale(0.83333);
    transform-origin: 0 0;
    position: absolute;
    z-index: 10;
    margin-right: 0 !important;
    width: 0.57rem;
    margin-left: -0.52rem;
    margin-top: 0.38rem;
    height: 0.17rem;
    line-height: 0.17rem;
    color: #ffffff;
    background-color: rgba(0, 0, 0, 0.6);
    padding-left: 0.03rem;
    padding-right: 0.02rem;
    border-bottom-left-radius: 0.04rem;
    border-bottom-right-radius: 0.04rem;
  }
}
</style>
