<!--
 * @Author: your name
 * @Date: 2024-11-20 17:12:28
 * @LastEditTime: 2024-11-20 17:28:58
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 我的页面，每行5个应用
 * @FilePath: \h5-interhosp\src\views\my\components\stageItem.vue
-->
<template>
    <div class="con_">
        <div
            class="content"
            v-for="(item, index) in data"
            :key="index"
            @click="jumpUrl(data)"
        >
            <img class="img" v-lazy="item.iconUrl" alt="" />
            <img
                class="angImg"
                v-lazy="item.angleIconUrl"
                alt=""
                v-show="item.angleIconUrl"
            />

            <h3 class="title">{{ item.applicationName }}</h3>
        </div>
    </div>
</template>

<script>
import tools from "@/util/tools";
export default {
    name: "StageItem",
    props: {
        datalists: {
            type: Object,
            default: () => {},
        },
        stageId: {
            type: String,
            default: () => "",
        },
        childStageId: {
            type: String,
            default: () => "",
        },
        position: {
            type: String,
            default: () => "",
        },
    },
    data() {
        return {
            data: [],
        };
    },
    mounted() {
        console.log("staageItem我的", this.datalists.childStageList);
        let temp = JSON.parse(JSON.stringify(this.datalists));
        this.data = temp.childStageList;
    },
    methods: {
        jumpUrl(a, b) {
            console.log("参数", a, b);
            tools
                .handleSetPoint({
                    stageId: this.stageId,
                    childStageId: this.childStageId,
                    stageAppId: a.applicationId,
                    trackingContent: this.position
                        ? `${this.position}|banner`
                        : a.applicationName,
                })
                .then(() => {
                    tools.jumpUrlManage(a, b);
                });
        },
    },
};
</script>

<style scoped>
.con_ {
    display: flex;
    width: 345px;
    /* height: 88px; */
    background-color: #fff;
    border-radius: 8px;
    margin: 0 auto;
    align-items: center;
    /* justify-content: center; */
    flex-wrap: wrap;
    padding-top: 20px;
    padding-bottom: 20px;
}
.content {
    font-size: 0;
    text-align: center;
    width: 20%;
    margin-bottom: 10px;
}
.img {
    height: 29px;
    background-size: auto 100%;
}
.angImg {
    position: absolute;
    right: 0;
    top: 0;
    height: 0.2rem;
    margin-top: -0.1rem;
}
.title {
    font-size: 12px;
    font-family: "PingFangSC";
    font-weight: 400;
    color: #333333;
    margin-top: 9px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
</style>
