<!--
 * @Author: your name
 * @Date: 2024-08-14 15:03:20
 * @LastEditTime: 2024-09-03 11:24:45
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 团队成员
-->
<template>
    <div v-if="tjList && tjList.length" class="cont">
        <div>
            <p class="title">
                团队成员 (<span>{{ teamNum }}人)</span>
            </p>
            <van-swipe class="my-swipe" :loop="false" indicator-color="#01CDA7">
                <van-swipe-item v-for="(list, index) in tjList" :key="index">
                    <div
                        class="item"
                        v-for="(ele, index) in list"
                        :key="index"
                        @click="jumpToPage(ele)"
                    >
                        <img
                            :src="
                                ele.headerUrl
                                    ? dencryptHeader(ele.headerUrl)
                                    : ele.sexCode == '1'
                                    ? require('@/images/search/man.png')
                                    : require('@/images/search/woman.png')
                            "
                            alt=""
                        />
                        <p class="name">{{ ele.doctorName }}</p>
                        <p class="level">{{ ele.levelName }}</p>
                        <!-- <p class="dept">{{ ele.deptName }}</p> -->
                    </div>
                </van-swipe-item>
            </van-swipe>
        </div>
    </div>
</template>
<script>
import common from "@/util/common";
import tools from "@/util/tools";
import { recommendDoctorList } from "@/api/api";
export default {
    data() {
        return {
            tjList: [],
            teamNum: "",
        };
    },
    created() {},
    mounted() {
        console.log("团队成员props", this.$props);
    },
    methods: {
        dencryptHeader: common.dencryptHeader,
        getData(memberList) {
            if (memberList && memberList.length) {
                var matrix = [[]];
                for (const item of memberList) {
                    if (matrix[matrix.length - 1].length < 4) {
                        matrix[matrix.length - 1].push(item);
                    } else if (matrix[matrix.length - 1].length === 4) {
                        matrix.push([item]);
                    }
                }
                this.teamNum = memberList.length;
                this.tjList = matrix;
            }
            return;
            let data = {
                unicode: unicode,
                deptId: deptId,
                doctorId: doctorId,
            };
            const that = this;
            recommendDoctorList(data).then((res) => {
                console.log("tjdoctor=--==--4444", res);
                if (res && res.length) {
                    var matrix = [[]];
                    for (const item of res) {
                        if (matrix[matrix.length - 1].length < 4) {
                            matrix[matrix.length - 1].push(item);
                        } else if (matrix[matrix.length - 1].length === 4) {
                            matrix.push([item]);
                        }
                    }
                    this.tjList = matrix;
                }
            });
        },
        jumpToPage(item) {
            console.log(item);
            let url =
                location.origin +
                location.pathname +
                `#/doctor?staffId=${item.staffId}&deptId=${item.deptId}&unicode=${item.unicode}`;
            item.status = "2";
            item.jumpUrl = url;
            tools.jumpUrlManage(item);
        },
    },
};
</script>
<style lang="less" scoped>
.cont {
    margin: 12px 16px;
    background: #ffffff;
    border-radius: 8px;

    > div {
        padding: 15px;
    }

    .title {
        font-size: 18px;
        font-weight: 500;
        color: #000000;
        line-height: 1;
        margin-bottom: 15px;
    }

    .my-swipe {
        width: 100%;

        .van-swipe-item {
            display: flex;
        }
    }

    ::v-deep .my-swipe .van-swipe__indicators {
        bottom: 0;
    }

    ::v-deep .my-swipe .van-swipe__indicator {
        width: 10px;
        height: 4px;
        border-radius: 2px;
        background-color: #dbfff8;
        opacity: 1;
    }

    :v-deep .my-swipe .van-swipe__indicator--active {
        width: 20px;
        height: 4px;
        border-radius: 2px;
    }

    ::v-deep .my-swipe .van-swipe__indicator:not(:last-child) {
        margin-right: 0;
    }

    .item {
        display: inline-block;
        overflow: hidden;
        text-align: center;
        width: 88px;

        img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-bottom: 6px;
        }

        .name {
            font-size: 14px;
            font-weight: 500;
            color: #333333;
            margin-bottom: 10px;
        }

        .level {
            font-size: 12px;
            color: #666666;
            margin-bottom: 5px;
        }

        .dept {
            font-size: 12px;
            color: #999999;
            margin-bottom: 10px;
        }
    }
}
</style>