<!--
 * @Author: your name
 * @Date: 2025-05-27 17:26:08
 * @LastEditTime: 2025-06-06 10:13:42
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 左右结构的专家医生子展台--专家医生左右滑动
 * @FilePath: \h5-interhosp\src\components\activity\leftrightDoc.vue
-->
<template>
    <div class="cont" v-show="isShow">
        <div class="title">
            <div class="titleImg"></div>
            <div class="text">{{ childStageName }}</div>
            <div class="titleImg"></div>
        </div>
        <div class="docListCon">
            <div v-for="(item, i) in commonlist" :key="i" class="docItem">
                <div class="tabName">{{ item.tabName }}</div>
                <div
                    class="docItem_"
                    v-for="(item1, i1) in item.docList"
                    :key="i1"
                    @click="jumpUrl(item1)"
                >
                    <img
                        class="touxiang"
                        v-lazy="
                            item1.avatorUrl
                                ? item1.avatorUrl
                                : require('@/images/expertAvator.png')
                        "
                        :key="item1.avatorUrl"
                    />
                    <div class="docDetail">
                        <div>
                            <span class="inline-b docName">{{
                                item1.docName
                            }}</span>
                            <span class="inline-b docLevel">{{
                                item1.levelName
                            }}</span>
                        </div>
                        <div class="middleCon">
                            <span
                                class="inline-b hospLevel"
                                v-show="item1.hospLevel"
                                >{{ item1.hospLevel }}</span
                            >
                            <span class="inline-b shenglue">{{
                                item1.orgName
                            }}</span>
                        </div>
                        <div class="dept">{{ item1.deptName }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import tools from "@/util/tools";
export default {
    props: {
        datalists: {
            type: Object,
        },
        stageName: {
            type: String,
        },
        stageId: {
            type: String,
        },
    },
    data() {
        return {
            commonlist: "",
            elementId: "",
            isShow: false,
            childStageId: "",
            childStageName: "",
        };
    },

    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("活动页专家医生左右滑动", temp);
        if (
            temp.stageTypeName === "专家医生左右滑动" &&
            temp.tabList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.commonlist = temp.tabList;
            this.childStageId = temp.childStageId;
            this.childStageName = temp.childStageName;
        }
    },

    methods: {
        jumpUrl(item, id, name) {
            console.log("跳转参数", item);
            item.elementId = this.elementId;
            // 没有jumpurl，
            item.jumpUrl = item.scheduleUrl;
            item.status = "2";
            if (item.jumpUrl) {
                tools
                    .handleSetPoint({
                        stageId: this.stageId,
                        childStageId: id,
                        stageAppId: item.applicationId,
                        trackingContent: `${this.stageName}-${name}-${item.elementContent}`,
                        businessName: "activity",
                    })
                    .then(() => {
                        tools.jumpUrlManage(item);
                    });
            }
        },
    },
};
</script>

<style scoped lang="less">
.title {
    display: flex;
    align-items: center;
    width: 345px;
    height: 46px;
    justify-content: center;
    background-color: #fff;
    margin: 0 auto;
    border-radius: 8px;
    .titleImg {
        background-image: url("@/images/titleImg.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 21px;
        height: 14px;
    }
    .text {
        margin-left: 10px;
        margin-right: 10px;
        color: 000;
        font-size: 18px;
        font-weight: bold;
    }
}
.inline-b {
    display: inline-block;
}
.docListCon {
    display: flex;
    margin-top: 15px;
    padding-right: 15px;
    width: calc(100vw - 15px);
    overflow-x: scroll;
    .docItem {
        // width: 263px;
        background-color: #fff;
        border-radius: 8px;
        margin-left: 15px;
        padding-bottom: 15px;
        margin-bottom: 10px;
        .tabName {
            font-size: 16px;
            font-weight: bold;
            margin: 15px 0 16px 15px;
        }
        .docItem_ {
            display: flex;
            padding-left: 15px;
            padding-right: 15px;
            margin-bottom: 10px;
            .touxiang {
                width: 47px;
                height: 47px;
                border-radius: 50%;
                margin-right: 13px;
            }
            .docDetail {
                color: #686b73;
                font-size: 13px;
                width: 176px;
                .docName {
                    color: #333333;
                    font-size: 15px;
                    margin-right: 10px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
                .middleCon {
                    display: flex;
                    align-items: baseline;
                }
                .shenglue {
                    // width: 48%;
                    text-overflow: ellipsis;
                    display: -webkit-inline-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 1; /* 超出几行省略 */
                    overflow: hidden;
                }
                .docLevel {
                    color: #333333;
                    font-size: 13px;
                }
                .hospLevel {
                    border: 1px solid #3ebfa0;
                    border-radius: 2px;
                    color: #3ebfa0;
                    font-size: 10px;
                    padding: 3px;
                    margin-right: 7px;
                }
                .dept {
                    color: #888888;
                    margin-top: 8px;
                }
            }
        }
    }
}
</style>