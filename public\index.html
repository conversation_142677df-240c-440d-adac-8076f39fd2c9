<!--
 * @Author: your name
 * @Date: 2021-11-08 15:09:05
 * @LastEditTime: 2021-12-24 11:21:09
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \health-hz\index.html
-->

<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <link
      href="https://cdnweb11.96225.com/favi.ico"
      mce_href="images/favicon.ico"
      rel="bookmark"
      type="image/x-icon"
    />
    <link
      href="https://cdnweb11.96225.com/favi.ico"
      mce_href="images/favicon.ico"
      rel="icon"
      type="image/x-icon"
    />
    <link
      href="https://cdnweb11.96225.com/favi.ico"
      mce_href="images/favicon.ico"
      rel="shortcut icon"
      type="image/x-icon"
    />

    <title></title>
    <script
      type="text/javascript"
      src="https://static.96225.com/eventtracking/bury.min.js"
    ></script>
    <!-- 抖音js -->
    <script src="https://lf1-cdn-tos.bytegoofy.com/goofy/developer/jssdk/jssdk-1.2.0.js"></script>
  </head>
  <% if (process.env.VUE_APP_RUN_ENV==='TEST' ||
  process.env.VUE_APP_RUN_ENV==='stabletest' ) { %>
  <script type="text/javascript" src="./static/vconsole.min.js"></script>
  <script>
    var vConsole = new VConsole();
    console.log("TEST");
  </script>
  <% }%>
  <script>
    function setRem() {
      var html = document.documentElement;
      // var windowWidth = html.clientWidth > 768 ? 768:document.documentElement.clientWidth
      var windowWidth = html.clientWidth;
      html.style.fontSize = windowWidth / 3.75 + "px";
    }
    function loadScript(url) {
      var script = document.createElement("script");
      script.type = "text/javascript";
      script.src = url;
      document.head.appendChild(script);
    }
    /**
     * @param {String}  errorMessage   错误信息
     * @param {String}  scriptURI      出错的文件
     * @param {Long}    lineNumber     出错代码的行号
     * @param {Long}    columnNumber   出错代码的列号
     * @param {Object}  errorObj       错误的详细信息，Anything
     */
    /* window.onerror = function(errorMessage, scriptURI, lineNumber,columnNumber,errorObj) {
                    console.log("错误信息：" , errorMessage);
                    console.log("出错文件：" , scriptURI);
                    console.log("出错行号：" , lineNumber);
                    console.log("出错列号：" , columnNumber);
                    console.log("错误详情：" , errorObj);
                } */
    window.addEventListener(
      "error",
      (e) => {
        // console.log("addEventListener error", e);
      },
      true
    );

    if (navigator.userAgent.indexOf("AliApp") > -1) {
      document.writeln(
        '<script src="https://appx/web-view.min.js"' +
          ">" +
          "<" +
          "/" +
          "script>"
      );
      document.writeln(
        '<script src="https://gw.alipayobjects.com/as/g/h5-lib/alipayjsapi/3.1.1/alipayjsapi.min.js"' +
          ">" +
          "<" +
          "/" +
          "script>"
      );
    } else if (
      navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
    ) {
      document.writeln(
        '<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"' +
          ">" +
          "<" +
          "/" +
          "script>"
      );
    } else if (navigator.userAgent.indexOf("smkVersion") > -1) {
      // 测试https://wechat-test.iconntech.com/filesys/group1/M00/00/00/wBRgNWFFrx2AQqZ9AADPMaz-siw5847.js
      //   生产https://open.iconntech.com/filesys/group1/M00/00/5E/wDIMGWFDC--AVFhUAADOSgQkvzA4741.js
      //   if (
      //     "<%= process.env.VUE_APP_RUN_ENV %>" == "TEST" ||
      //     "<%= process.env.VUE_APP_RUN_ENV %>" == "stabletest"
      //   ) {
      //     // 测试
      //     // loadScript(
      //     //   "https://wechat-test.iconntech.com/filesys/group1/M00/00/00/wBRgNWFFrx2AQqZ9AADPMaz-siw5847.js"
      //     // );
      //     loadScript(
      //       "https://open.iconntech.com/filesys/group1/M00/00/5E/wDIMGWFDC--AVFhUAADOSgQkvzA4741.js"
      //     );
      //   } else {
      //     loadScript(
      //       "https://open.iconntech.com/filesys/group1/M00/00/5E/wDIMGWFDC--AVFhUAADOSgQkvzA4741.js"
      //     );
      //   }

      console.log("wwwww", window);
      function goBury_ceshi() {
        //测试服appId和businessId
        window.bury.init({
          autoTrack: false, //是否打开自动点击事件埋点，默认true打开，关闭传false
          appId: "@h5buryzuixin924", //必传，向埋点系统后台申请
          businessId: "8ec3d75da4ac4a1b90768aad88034a3d", //必传，向埋点系统后台申请
        });
        //   window.bury.init({
        //     autoTrack: false, //是否打开自动点击事件埋点，默认true打开，关闭传false
        //     appId: "291501593554108416", //必传，向埋点系统后台申请
        //     businessId: "B36678", //必传，向埋点系统后台申请
        //   });
        window.bury.send("config", {
          _bus_channel: "smkapp", // 业务渠道
          _service_zone: "330100",
          // _business_id: "291501593554108416_B36678", // 正式 appid_businessid
          _business_id: "@h5buryzuixin924_8ec3d75da4ac4a1b90768aad88034a3d", //测试 appid_businessid
        });
      }
      function goBury() {
        // 生产环境
        window.bury.init({
          autoTrack: false, //是否打开自动点击事件埋点，默认true打开，关闭传false
          appId: "291501593554108416", //必传，向埋点系统后台申请
          businessId: "B36678", //必传，向埋点系统后台申请
        });
        window.bury.send("config", {
          _bus_channel: "smkapp", // 业务渠道
          _service_zone: "330100",
          _business_id: "291501593554108416_B36678", // 正式 appid_businessid
        });
        getUser();
      }
      // 市民卡用户信息埋点
      function getUser() {
        let uInfo = null;
        uInfo = JSON.parse(window.sessionStorage.getItem("userInfo")) || "";
        let smkUId = uInfo.smkUId || sessionStorage.getItem("smkUId");
        if (uInfo) {
          bury.send("config", {
            _login_id: smkUId, //原市民卡、城市大脑和健康通统一用户id
          });
        }
      }
    }
    document.addEventListener(
      "DOMContentLoaded",
      function () {
        setRem();
        window.onresize = function () {
          // reported();
          setRem();
        };
      },
      false
    );
    window.addEventListener(
      "load",
      function () {
        if (navigator.userAgent.indexOf("smkVersion") > -1) {
          // 调用市民卡埋点
          console.log("HUANJING环境", "<%= process.env.VUE_APP_RUN_ENV %>");
          if (
            "<%= process.env.VUE_APP_RUN_ENV %>" == "TEST" ||
            "<%= process.env.VUE_APP_RUN_ENV %>" == "stabletest"
          ) {
            // 测试环境埋点
            // goBury_ceshi();
            goBury();
          } else {
            // 生产环境埋点
            goBury();
          }
        }
      },
      false
    );
  </script>

  <body>
    <div id="app"></div>
    <!-- built files will be auto injected -->
    <!-- <script type="text/javascript" src="//res.wx.qq.com/open/js/jweixin-1.6.0.js"></script> -->
  </body>
</html>
