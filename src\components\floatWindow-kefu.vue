<!--Author: 郭元扬
ProjectDescription: 悬浮窗
CreateTime: 2023-06-16
UpdateTime: 2023-06-16-->

<template>
    <div
        class="float-window"
        ref="floatWindow"
        :style="{ left: left + 'px', top: top + 'px' }"
    >
        <van-overlay :show="show" @click="changStatus">
            <div class="menu" ref="menu">
                <div v-show="status === 'open'">
                    <img
                        :src="require(`@/images/float1/${item.name}.png`)"
                        alt=""
                        v-for="item of this.tabs"
                        :key="item.name"
                        @click="jump(item)"
                    />
                </div>
                <img class="closeImg" :src="imgUrl1" alt="" />
            </div>
        </van-overlay>
        <div
            class="jia"
            @touchstart="onTouchStart"
            @touchmove="onTouchMove"
            @touchend="onTouchEnd"
            @click="changStatus"
            ref="jia"
        >
            <img v-show="status === 'close'" class="jia" :src="imgUrl" alt="" />
        </div>
    </div>
</template>

<script>
import Vue from "vue";
import { Overlay } from "vant";
import tools from "@/util/tools";
Vue.use(Overlay);
export default {
    props: {
        active: {
            type: String,
            default: () => "jigou",
        },
        unicode: {
            type: String,
            default: () => "",
        },
    },
    data() {
        return {
            imgUrl: require("../images/float1/open.png"),
            imgUrl1: require("../images/float1/close.png"),
            startX: 0,
            startY: 0,
            offsetX: 0,
            offsetY: 0,
            left: 0,
            fixedLeft: 0,
            top: 400,
            isScrolling: false,
            status: "close",
            rotate: {},
            tabs: [
                {
                    name: "jigou",
                    path: "/hosporg",
                },
                {
                    name: "xiaoxi",
                },
                {
                    name: "geren",
                    path: "/mine",
                },
                {
                    name: "kefu",
                },
            ],
            show: false,
        };
    },
    mounted() {
        console.log("window.innerWidth", window.innerWidth);
        console.log(
            "this.$refs.floatWindow.offsetWidth",
            this.$refs.floatWindow
        );
        this.fixedLeft =
            window.innerWidth - this.$refs.floatWindow.offsetWidth - 12;
        this.left = this.fixedLeft;
        this.top =
            window.innerHeight -
            document.querySelector(".float-window").clientHeight -
            130;
        for (const item of this.tabs) {
            if (item.name === this.active) {
                item.name = `${this.active}2`;
            }
        }
    },
    methods: {
        jump(val) {
            if (val.name === "xiaoxi") {
                this.$router.push({
                    path: "/talk",
                    query: {
                        orgId: window.localStorage.getItem("orgId"),
                    },
                });
            } else if (val.name === "kefu") {
                // https://www.hfi-health.com:28181/AIChat/#/?AIOrigin=wjwxMini&merchantId=12330100470116614F
                let item = {
                    jumpUrl: `${location.origin}/${
                        process.env.VUE_APP_AI
                    }/#/?AIOrigin=${sessionStorage.getItem(
                        "hlw_remoteChannel"
                    )}&merchantId=${this.unicode}`,
                    status: "0",
                };
                tools.jumpUrlManage(item);
            } else {
                this.$emit("jump", val.name);
            }
        },
        onTouchStart(e) {
            this.startX = e.touches[0].clientX;
            this.startY = e.touches[0].clientY;
            this.offsetX = this.left;
            this.offsetY = this.top;
            this.isScrolling = false;
        },
        onTouchMove(e) {
            this.status = "close";
            const x = e.touches[0].clientX - this.startX + this.offsetX;
            const y = e.touches[0].clientY - this.startY + this.offsetY;
            const maxX =
                window.innerWidth - this.$refs.floatWindow.offsetWidth - 12;
            const maxY =
                window.innerHeight - this.$refs.floatWindow.offsetHeight;
            const minX = -this.$refs.menu.offsetWidth;
            this.left = x < minX ? minX : x > maxX ? maxX : x;
            this.top = y < 0 ? 0 : y > maxY ? maxY : y;
            if (
                Math.abs(e.touches[0].clientX - this.startX) > 5 ||
                Math.abs(e.touches[0].clientY - this.startY) > 5
            ) {
                this.isScrolling = true;
            }
        },
        onTouchEnd(e) {
            this.left = this.fixedLeft;
        },
        changStatus() {
            this.show = !this.show;
            if (this.status === "open") {
                this.status = "close";
            } else {
                this.status = "open";
            }
        },
    },
    watch: {
        status(val) {},
    },
};
</script>

<style lang="less" scoped>
.float-window {
    position: fixed;
    display: flex;
    z-index: 999;
    .menu {
        margin-right: 0.12rem;
        position: absolute;
        bottom: 2.3rem;
        left: 0.16rem;
        div {
            display: flex;
            justify-content: space-around;
            align-items: center;
            img {
                width: 0.79rem;
                height: 0.7rem;
                margin-right: 0.095rem;
            }
        }
        .closeImg {
            width: 0.16rem;
            height: 0.16rem;
            position: absolute;
            right: 0.16rem;
            margin-top: 0.54rem;
        }
    }
    .jia {
        width: 0.63rem;
        height: 0.89rem;
        background: 100% 100% no-repeat;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
