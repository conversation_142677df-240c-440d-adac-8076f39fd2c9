<!--
 * @Author: your name
 * @Date: 2024-12-05 15:51:51
 * @LastEditTime: 2025-02-10 17:01:45
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 专家医生
 * @FilePath: \h5-interhosp\src\components\unite\expert.vue
-->
<template>
    <div class="main" v-if="this.isshow == 2">
        <div class="title">{{ tabtitle.childStageName }}</div>
        <div class="remi">{{ tabtitle.childStageSubName }}</div>
        <div>
            <div class="cont">
                <ul>
                    <li
                        @click="btnclick(index)"
                        :class="{ active: index == num }"
                        v-for="(item, index) in tabtitle.tabList"
                        :key="index"
                    >
                        {{ item.tabName }}
                    </li>
                </ul>
            </div>
            <div class="cont-sec">
                <div
                    class="sec-main"
                    v-for="(item1, index1) in tabtitle.tabList[num].docList"
                    :key="index1"
                    @click="go(item1, index1)"
                    :data-exposure-id="tabtitle.elementId"
                    :data-exposure-content="item1.elementContent"
                    :data-exposure-sn="index1"
                >
                    <div class="main-left">
                        <img
                            v-lazy="
                                item1.avatorUrl
                                    ? item1.avatorUrl
                                    : require('@/images/expertAvator.png')
                            "
                            :key="item1.avatorUrl"
                            alt=""
                        />
                    </div>
                    <div class="main-right">
                        <p class="right-one">
                            <span>{{ item1.docName }}</span
                            ><span>{{ item1.levelName }}</span>
                        </p>

                        <p class="right-three">{{ item1.orgName }}</p>
                        <p class="right-two-p">
                            <span class="right-two">{{ item1.deptName }}</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
  <script>
import tools from "@/util/tools.js";
import common from "@/util/common";
export default {
    props: {
        datalists: {
            type: Object,
        },
    },
    data() {
        return {
            num: 0,
            isshow: "", //是否打开table
            tabtitle: [
                {
                    childStageList: [],
                },
            ],
            elementId: "",
        };
    },
    created() {},
    methods: {
        dencryptHeader: common.dencryptHeader,
        btnclick(index) {
            this.num = index;
        },
        // 点击跳转
        go(item, index) {
            item.elementId = this.elementId;
            // 没有jumpurl，
            item.index = index;
            item.jumpUrl = item.scheduleUrl;
            item.status = "2";
            tools.jumpUrlManage(item);
        },
    },
    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("expert1111111", temp);
        if (temp.stageTypeName == "专家医生") {
            this.elementId = temp.elementId;
            this.tabtitle = temp;
            this.isshow = temp.hasTab;
        }
    },
};
</script>
  <style lang="less" scoped>
.main {
    background-color: #fff;
    margin: 0 auto;
    border-radius: 0.08rem;
    overflow: hidden;
    padding-bottom: 0.03rem;
    margin: 0 auto;
    width: 3.45rem;
    margin-bottom: 0.1rem;
    img[lazy="loading"] {
        display: block;
        width: 20%;
        line-height: 100%;
        margin: auto;
    }
    .title {
        font-size: 0.18rem;
        font-weight: 700;
        color: #000;
        height: 0.4rem;
        line-height: 0.4rem;
        // background-image: linear-gradient(#f0fffc, #fff);
        padding-left: 0.09rem;
        padding-top: 0.05rem;
    }
    .remi {
        color: #888888;
        padding-top: 0.06rem;
        padding-bottom: 0.1rem;
        font-size: 0.11rem;
        margin: 0 0.1024rem;
    }
    .cont::-webkit-scrollbar {
        display: none;
    }
    .cont {
        margin: 0 0.1024rem;
        display: flex;
        overflow-y: hidden;
        overflow-x: scroll;
        padding-bottom: 0.1rem;
        ul {
            display: flex;
            li {
                float: left;
                margin-right: 0.08rem;
                text-align: center;
                line-height: 0.25rem;
                width: 0.82rem;
                height: 0.25rem;
                border-radius: 0.13rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 0.13rem;
                color: #666666;
                background: #f8f8f8;
            }
        }
        .active {
            float: left;
            margin-right: 0.08rem;
            text-align: center;
            width: 0.82rem;
            height: 0.25rem;
            line-height: 0.25rem;
            border-radius: 0.13rem;
            font-size: 0.13rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #fff;
            background: linear-gradient(#01cca6, #20d7b5);
            // border: 1px solid #01cca6;
        }
    }
    .cont-sec {
        display: flex;
        // justify-content: space-between;
        overflow-x: scroll;
        margin-left: 0.13rem;
        .sec-main {
            width: 0.97rem;
            // height: 0.8rem;
            border-radius: 0.08rem;
            float: left;
            // display: flex;
            margin-bottom: 0.09rem;
            padding: 0.06rem 0.05rem !important;
            // background: red;
            box-shadow: 0px 0.015rem 0.08rem 0px rgba(136, 136, 136, 0.1);
            margin-right: 0.1rem;
            .main-left {
                flex: 2;
                margin-right: 0.05rem;
                margin-bottom: 0.1rem;
                img {
                    width: 0.4rem;
                    height: 0.4rem;
                    border-radius: 0.2rem;
                    vertical-align: middle;
                }
            }
            .main-right {
                flex: 8;
                .right-one {
                    :first-child {
                        display: inline-block;
                        vertical-align: middle;
                        font-weight: 550;
                        color: #333333;
                        // width: 0.5rem;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        font-size: 0.13rem !important;
                        margin-right: 0.05rem;
                    }
                    :nth-child(2) {
                        width: 0.4rem;
                        display: inline-block;
                        vertical-align: middle;
                        color: #666;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        font-size: 0.1rem !important;
                    }
                }
                .right-two {
                    padding: 0.01rem 0.01rem;
                    text-align: center;
                    line-height: 0.13rem;
                    font-size: 0.09rem !important;
                }
                .right-two-p {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    // padding: 0.003rem 0.003rem;
                    color: #01cda7;
                    max-width: 0.85rem;
                    display: inline-block;
                    border: 1px solid #01cda7;
                    line-height: 0.7;
                }
                .right-three {
                    width: 0.9rem;
                    display: inline-block;
                    font-size: 0.1rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    color: #333;
                }
            }
        }
    }
    .doctor {
        text-align: center;
        height: 200px;
        border: 1px solid red;
    }
}
</style>
  