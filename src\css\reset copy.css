/* 浏览器重置 start */
body,
p,
ul,
ol,
li,
dl,
dt,
dd,
h1,
h2,
h3,
h4,
h5,
form,
fieldset,
legend,
input,
select,
textarea,
button,
th,
td,
menu,
article,
pre,
xmp,
plaintext,
listing {
  margin: 0;
  padding: 0;
}

pre,
xmp,
plaintext,
listing {
  white-space: normal;
}

article,
aside,
dialog,
figure,
footer,
header,
hgroup,
nav,
section,
select,
time {
  display: block;
}

h1,
h2,
h3,
h4,
h5,
h6,
input,
textarea,
select,
button,
label {
  font-size: 100%;
  vertical-align: middle;
}

ul,
dl,
ol {
  list-style: none;
}

img,
fieldset,
input[type="submit"] {
  border: none;
}

input {
  outline: none;
  background: transparent;
  vertical-align: top;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

button {
  cursor: pointer;
  border: none;
}

textarea {
  word-wrap: break-word;
  resize: none;
}

/* 去除IE下拉菜单箭头 */
select::-ms-expand {
  display: none;
}

/* 去除IE下radio、checkbox默认样式 */
::-ms-check {
  display: none;
}

/* 去除输入框、文本域默认边框和IOS4下的内阴影 */
input,
textarea,
select {
  border: none;
  outline: none;
}

/* 去除input,textarea在IPHONE上的阴影  */
/*input,textarea{-webkit-appearance: none;}*/
/* 设置Gecko内核下输入框属性placeholder中值的文件颜色 */
input:-moz-placeholder {
  color: #d2d5d5;
}

input:-webkit-placeholder {
  color: #d2d5d5;
}

/* 设置webkit内核下输入框属性placeholder中值的文件颜色 */
::-webkit-input-placeholder {
  color: #d2d5d5;
}

body {
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  /* 设置点击链接时遮罩层为全透明 */
  overflow: hidden;
  min-height: 100%;
  font-family: Hiragino Sans GB, "pingFangSC", 'Helvetica Neue', Helvetica, 'HanHei SC', 'Hiragino Sans GB', 'Microsoft Yahei', 'WenQuanYi Micro Hei', Arial, sans-serif;
  color: #242424;
  -webkit-font-smoothing: antialiased;
  font-size: 14px;
  font-size: 0.14rem;
  /* 抗锯齿 */
}

/* 设置webkit内核下文字大小不小于12px */
html,
body {
  -webkit-text-size-adjust: none;
  width: 100%;
  min-height: 100%;
  overflow-x: hidden;
  height: 100%;
}

/* 禁止长按链接、按钮、输入框出现系统默认弹框菜单 */
a,
button,
input {
  -webkit-touch-callout: none;
  outline: none;
}

/*a,a:visited{text-decoration:none;-webkit-touch-callout:none;color:#242424;}*/
/* 微信内容展示页CSS */
blockquote {
  border-style: solid;
  margin: 0;
  padding: 15px;
}

/* 防止不同语言之间对行间距的修改 */
/* 浏览器重置 end */
/* 防止网页被插入iframe广告 */
/*iframe{display: none;opacity: 0;}*/
/**{box-sizing:border-box;}*/


*:not(input) {
  user-select: none;
}

.main {
  position: relative;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}


.clear {
  height: 0px;
  clear: both;
  font-size: 0px;
  line-height: 0px;
  zoom: 1;
}

body {
  background: rgba(256, 256, 256, 0);
}


/* .icon.icon-loading:before{
    content: "";
    display: block;
    background:url("../images/loading.gif") no-repeat center;
    background-size: 100%;
    width: 0.26rem;
    height: 0.26rem;
    margin: 0rem auto;
} */



/* .icon.icon-suc:before{
    content: "";
    display: block;
    background:url("../images/suc.png") no-repeat center;
    background-size: 100%;
    width: 0.4rem;
    height: 0.4rem;
    margin: 0.1rem auto;
} */


.mint-toast {
  background: rgba(17, 17, 17, 0.7);
}

.mint-toast-text {
  /*padding-top: 0 !important;*/
  font-size: 0.13rem !important;
}


.sucTip .mint-toast-text {
  font-size: 0.14rem !important;
  padding-bottom: 10px;
}






/*.sureLayer .layui-m-layerbtn span[yes] {
    color: #40AFFE;
    font-size: 0.16rem;
}*/


.buleBtn .layui-m-layerbtn span[yes] {
  color: #40AFFE;
  font-size: 0.16rem;
}

.buleBtn .layui-m-layerbtn span[no] {
  color: #999;
  font-size: 0.16rem;
}


.layui-m-layerchild.loadingT {
  background-color: rgba(48, 48, 48, 0.7);
}

/*@media only screen and (device-width: 375px) and (device-height:812px) and (-webkit-device-pixel-ratio:3) {
    #ktFooter {
        padding-bottom:34px; 
    }

    .main:before{
        content: "";
        display: inline-block;
        width: 100%;
        height:24px;
    }
}*/
input[type="search"] {
  caret-color: #363636;
}

input[type="search"]::-webkit-search-cancel-button {
  display: none;
  color: red;
}

/*包含以下四种的链接*/
a {
  text-decoration: none;
}

/*正常的未被访问过的链接*/
a:link {
  text-decoration: none;
}

/*已经访问过的链接*/
a:visited {
  text-decoration: none;
}

/*鼠标划过(停留)的链接*/
a:hover {
  text-decoration: none;
}

/* 正在点击的链接*/
a:active {
  text-decoration: none;
}

.cZ {
  color: #fc5670 !important;
}

.cRed {
  color: rgba(235, 87, 80, 1) !important;
}

.c548 {
  color: rgba(54, 58, 68, 1) !important;
}

.c54 {
  color: rgba(54, 54, 54) !important;
}

.c141 {
  color: rgba(141, 141, 141) !important;
}

.c102 {
  color: rgba(102, 102, 102) !important;
}

.c51 {
  color: rgba(51, 51, 51, 1) !important;
}

.c99 {
  color: #999999 !important;
}

.c122 {
  color: rgba(122, 122, 122, 1) !important;
}

.c153 {
  color: rgba(153, 153, 153, 1) !important;
}

.c62 {
  color: rgba(62, 191, 160, 1) !important;
}

.c155 {
  color: rgba(155, 155, 155, 1);
}

.c191 {
  color: rgba(191, 191, 191, 1) !important;
}

.c69 {
  color: rgba(69, 69, 69, 1) !important;
}

.c76 {
  color: rgba(76, 76, 76, 1) !important;
}

.c188 {
  color: rgba(188, 188, 188, 1) !important;
}

.c94 {
  color: rgba(94, 94, 94, 1) !important;
}

.c85 {
  color: rgba(85, 85, 85, 1) !important;
}

.c256 {
  color: rgba(256, 256, 256, 1) !important;
}

.c203 {
  color: rgba(203, 203, 203, 1) !important
}

.c6a {
  color: #6a6a6a !important;
}

.cA8 {
  color: #A8A8A8;
}

.c167 {
  color: rgba(167, 167, 167, 1);
}

.f11 {
  font-size: 0.11rem;
}

.f12 {
  font-size: 0.12rem;
}

.f13 {
  font-size: 0.13rem;
}

.f14 {
  font-size: 0.14rem;
}

.f15 {
  font-size: 0.15rem !important;
}

.f16 {
  font-size: 0.16rem;
}

.f17 {
  font-size: 0.17rem;
}

.f18 {
  font-size: 0.18rem;
}

.f20 {
  font-size: 0.2rem;
}

.noBg {
  background: none;
}

.bgWhite {
  background-color: white !important;
}

.bg246 {
  background-color: rgba(246, 247, 248, 1);
}

.bg248 {
  background-color: rgba(248, 248, 248, 1)
}

.bg62 {
  background-color: rgba(62, 191, 160, 1)
}

.bg244 {
  background-color: rgba(244, 244, 244, 1)
}

.mid {
  width: 3.43rem;
  height: 0.9rem;
  background: url("../images/banner_md.png") no-repeat center;
  background-size: 100%;
  margin: 0.16rem auto;
}

.notice {
  width: 3.43rem;
  height: 0.9rem;
  background: url("../images/notice.png") no-repeat center;
  background-size: 100%;
  margin: 0.16rem auto;
}

.white {
  color: white;
}

.w100 {
  width: 100%;
}

.h40 {
  height: 0.4rem;
}

.h05 {
  height: 0.05rem;
}

.h10 {
  height: 0.1rem;
}

.h100 {
  height: 100%;
}

.h45 {
  height: 0.45rem;
}

.h50 {
  height: 0.5rem;
}

.h55 {
  height: 0.55rem;
}

.h35 {
  height: 0.35rem;
}

.mb5 {
  margin-bottom: 0.05rem;
}

.mb15 {
  margin-bottom: 0.15rem;
}

.mt5 {
  margin-top: 0.05rem;
}

.mt8 {
  margin-top: 0.08rem;
}

.mt10 {
  margin-top: 0.1rem;
}

.mt15 {
  margin-top: 0.15rem;
}

.mt16 {
  margin-top: 0.16rem;
}

.mt20 {
  margin-top: 0.2rem;
}

.mt30 {
  margin-top: 0.3rem;
}

.mt40 {
  margin-top: 0.4rem;
}

.mt80 {
  margin-top: 0.8rem;
}

.mr40 {
  margin-right: 0.4rem;
}

.mr10 {
  margin-right: 0.1rem;
}

.mr16 {
  margin-right: 0.16rem;
}

.mr26 {
  margin-right: 0.26rem;
}

.mr28 {
  margin-right: 0.28rem;
}

.mr36 {
  margin-right: 0.36rem;
}

.ml5 {
  margin-left: 0.05rem;
}

.ml16 {
  margin-left: 0.16rem;
}

.mr20 {
  margin-right: 0.2rem;
}

.ml10 {
  margin-left: 0.1rem;
}

.ml5 {
  margin-left: 0.05rem;
}

.ml12 {
  margin-left: 0.12rem;
}

.mr8 {
  margin-right: 0.08rem;
}

.ml20 {
  margin-left: 0.2rem;
}

.ml26 {
  margin-left: 0.26rem;
}

.mb10 {
  margin-bottom: 0.1rem;
}

.mb16 {
  margin-bottom: 0.16rem;
}

.mb20 {
  margin-bottom: 0.2rem;
}

.mb30 {
  margin-bottom: 0.3rem;
}

.mb40 {
  margin-bottom: 0.4rem;
}

.mb50 {
  margin-bottom: 0.5rem;
}

.mb60 {
  margin-bottom: 0.6rem;
}

.mb100 {
  margin-bottom: 1rem;
}

.pb20 {
  padding-bottom: 20px;
}

.pb30 {
  padding-bottom: 30px;
}

.pb50 {
  padding-bottom: 0.5rem;
}

.pl15 {
  padding-left: 0.15rem;
}

.pr15 {
  padding-right: 0.15rem;
}

.pr {
  position: relative;
}


.mint-loadmore-text {
  font-size: 0.12rem;
}

.z2 {
  z-index: 2;
}

.tipSpan_banner {
  background: rgba(255, 255, 255, 0.12);
  padding: 0.06rem 0.2rem;
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 0.3rem;
}

.alignL {
  text-align: left;
}

.fw600 {
  font-weight: 600;
}

.fw400 {
  font-weight: 400;
}



.bg245 {
  background-color: rgba(245, 245, 245, 1);
}

.f36 {
  font-size: 0.36rem;
}

.flex {
  display: flex;
  justify-content: center;
  justify-items: center;
  align-items: center;
}

.cy {
  width: 0.8rem;
  height: 0.9rem;
  /* max-width: 25%; */
  margin: 0.04rem;

}

.yygh {
  background: url("../images/yygh.png") no-repeat center;
  background-size: 100%;
}

.qtjj {
  background: url("../images/qtjj.png") no-repeat center;
  background-size: 100%;
}

.tjbg {
  background: url("../images/tjbg.png") no-repeat center;
  background-size: 100%;
}


.wdyy {
  background: url("../images/wdyy.png") no-repeat center;
  background-size: 100%;
}

.jkda {
  background: url("../images/jkda.png") no-repeat center;
  background-size: 100%;
}

.tyyjs {
  background: url("../images/tyyjs.png") no-repeat center;
  background-size: 100%;
}

.mzsc {
  background: url("../images/mzsc.png") no-repeat center;
  background-size: 100%;
}

.gbtj {
  background: url("../images/gbtj.png") no-repeat center;
  background-size: 100%;
}

.yxbg {
  background: url("../images/yxbghr.png") no-repeat center;
  background-size: 100%;
}

.bgcx {
  background: url("../images/bgcx.png") no-repeat center;
  background-size: 100%;
}

.wd {
  width: 0.36rem;
  height: 0.36rem;
}

.tyyjs {
  background: url("../images/tyyjs.png") no-repeat center;
  background-size: 100%;
}

.hlwyy {
  background: url("../images/hlwyy.png") no-repeat center;
  background-size: 100%;
}

.jkcf {
  background: url("../images/jkcf.png") no-repeat center;
  background-size: 100%;
}

.jkpg {
  background: url("../images/jkpg.png") no-repeat center;
  background-size: 100%;
}

.pdjh {
  background: url("../images/pdjh.png") no-repeat center;
  background-size: 100%;
}

.zxqh {
  background: url("../images/zxqh.png") no-repeat center;
  background-size: 100%;
}

.ymcc {
  background: url("../images/ymcc.png") no-repeat center;
  background-size: 100%;
}

.hky {
  background: url("../images/hky.png") no-repeat center;
  background-size: 100%;
}

.cloudMap {
  background: url("../images/cloudMap.png") no-repeat center;
  background-size: 100%;
}

.dzjkz {
  background: url("../images/dzjkz.png") no-repeat center;
  background-size: 100%;
}

.xlhz {
  background: url("../images/xlhz.png") no-repeat center;
  background-size: 100%;
}

.szyx {
  background: url("../images/szyx.png") no-repeat center;
  background-size: 100%;
}

.jkdk {
  background: url("../images/jkdk.png") no-repeat center;
  background-size: 100%;
}

.sxjy {
  background: url("../images/sxjy.png") no-repeat center;
  background-size: 100%;
}

.userMg {
  background: url("../images/userMg.png") no-repeat center;
  background-size: 100%;
}

.sblpzd {
  background: url("../images/sblpzd.png") no-repeat center;
  background-size: 100%;
}

.hsjc {
  background: url("../images/hsyy.png") no-repeat center;
  background-size: 100%;
}

.hsjg {
  background: url("../images/hsjg.png") no-repeat center;
  background-size: 100%;
}

.hsbg {
  background: url("../images/hsjc.png") no-repeat center;
  background-size: 100%;
}

.xgjz {
  background: url("../images/xgjz.png") no-repeat center;
  background-size: 100%;
}

.jkk {
  background: url("../images/jkk.png") no-repeat center;
  background-size: 100%;
}

.sbk {
  background: url("../images/sbk.png") no-repeat center;
  background-size: 100%;
}

.fC {
  flex-direction: column;
  align-self: flex-start;
}

.top {
  background: url("../images/banner_mh.png") no-repeat center;
  background-size: 100%;
  width: 100%;
  height: 0.97rem;
}

.wdyyDiv p {
  font-size: 0.14rem;
  margin-top: 0.08rem;
  width: max-content;
  text-align: center;
}

.wdyyDiv div.fC {
  margin-bottom: 0.16rem;
  width: 25%;
}

.fiveDiv .fC {
  width: 20%;
  margin-bottom: 0.12rem;
}

.fiveDiv p {
  font-size: 0.12rem;
  width: max-content;
  text-align: center;
}

.fiveDiv .wd {
  width: 0.27rem;
  height: 0.27rem;
}

.fiveDiv .wd img {
  width: 0.27rem;
  height: 0.27rem;
}

.elder .f18 {
  font-size: 0.22rem;
}

.elder .f16 {
  font-size: 0.2rem;
}


.elder .wdyyDiv div.fC {
  width: 33.3%;
}

.elder .fC p {
  font-size: 0.2rem;
}

.elder .yygh {
  background: url("../images/yyghB.png") no-repeat center;
  background-size: 100%;
}

.elder .qtjj {
  background: url("../images/qtjjB.png") no-repeat center;
  background-size: 100%;
}

.elder .tjbg {
  background: url("../images/tjbgB.png") no-repeat center;
  background-size: 100%;
}


/* .elder .wdyy {
  background: url("../images/wdyyB.png") no-repeat center;
  background-size: 100%;
} */

.elder .jkda {
  background: url("../images/jkdaB.png") no-repeat center;
  background-size: 100%;
}

.elder .tyyjs {
  background: url("../images/tyyjsB.png") no-repeat center;
  background-size: 100%;
}


.elder .mzsc {
  background: url("../images/mzscB.png") no-repeat center;
  background-size: 100%;
}

.elder .gbtj {
  background: url("../images/gbtjB.png") no-repeat center;
  background-size: 100%;
}

.elder .bgcx {
  background: url("../images/bgcxB.png") no-repeat center;
  background-size: 100%;
}


.footer {
  /* height: 0.9rem; */
  width: 100%;
  font-size: 0.12rem;
  color: rgba(104, 107, 115, 1);
  text-align: center;
  padding-top: 0.34rem;
  padding-bottom: 0.34rem;
  /* position: fixed;
  bottom: 0; */
}

.elder .footer {
  font-size: 0.16rem;
}

.footer p {
  width: 100%;
}


.dkf {
  background: #f3f4f5;
  /* line-height: 0.9rem; */
  text-align: center;
  display: flex;
  align-items: center;
}




.midSwip.mint-swipe {
  width: 100%;
  height: 1rem;
  background: linear-gradient(0deg, #F6F6F6 0%, #FFFFFF 100%);
}

.midSwip .mint-swipe-indicators {
  bottom: 0rem;
}


.midSwip .mint-swipe-indicator.is-active {
  background-color: #fff;
  opacity: 1;
  width: 11px;
  height: 4px;
  border-radius: 2px;
}

.midSwip .mint-swipe-indicator {
  /* background: #eeeeee; */
  width: 4px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.5);
  opacity: 0.6;
}

.midBanner {
  width: 3.55rem;
  height: 0.9rem;
  margin: 0.1rem auto;
}


.cyImg {
  max-width: 100%;
  max-height: 100%;
  margin: 0 auto;
  display: block;
}

/* .midBanner .cyImg{
  margin: 0 auto;
  display: block;
} */
.TipBar .van-notice-bar {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  height: 0.35rem;
  padding: 0 0.16rem;
  color: #ed6a0c;
  font-size: 0.12rem;
  line-height: 0.35rem;
  background-color: #fffbe8;
}

.TipBar .van-icon-volume-o:before {
  content: "" !important;
  background: url('../images/noticeIcon.png');
  width: 0.12rem;
  height: 0.12rem;
  background-size: 100%;
}

.TipBar .van-notice-bar {
  font-size: 0.12rem;
}

.elder .TipBar .van-icon-volume-o:before {
  /* content: "" !important; */
  /* background: url('../images/noticeIcon.png'); */
  width: 0.16rem;
  height: 0.16rem;
  /* background-size: 100%; */
}

.elder .TipBar .van-notice-bar {
  font-size: 0.16rem;
}

.topThreeBox {
  position: relative;
  width: 100%;
  height: 1.37rem;
  overflow: hidden;
}

.topThree {
  position: absolute;
  width: 1.31rem;
  height: 1.37rem;
  /* background: #FFFFFF; */
  /* box-shadow: 0px 0px 0.11rem 0px rgba(67,176,156,0.14);
  border-radius: 0.13rem;
  flex-direction: column; */
}


.titleYy {
  color: rgba(0, 0, 0, 1);
  font-size: 0.18rem;
  font-weight: 600;
  width: 100%;
  height: 0.54rem;
  position: relative;
}

.titleYy p {
  position: absolute;
  top: 0;
  height: 0.54rem;
  line-height: 0.54rem;
  left: 0.1rem;
}

.arr {
  width: 0.05rem;
  height: 0.36rem;
  background: url("../assets/arr.png") no-repeat center;
  background-size: 100%;
  margin-right: 0.1rem;
}

.ptYy .left {
  width: 0.36rem;
  height: 0.36rem;
  margin-left: 0.02rem;
}

.ptYy .middle {
  min-width: 0.9rem;
  width: max-content;
  font-size: 0.16rem;
}

.elder .ptYy .middle{
  font-size: 0.18rem;
}

.jqqd {
  width: 1.73rem;
  height: 0.91rem;
  position: absolute;
  bottom: 0rem;
  right: 0.1rem;
}

.helpIcon {
  width: 0.13rem;
  height: 0.13rem;
  display: inline-block;
  background: url("../assets/help.png") no-repeat center;
  background-size: 100%;
  margin-right: 0.08rem;
}

.jkgl {
  width: 3.55rem;
  padding-bottom: 0.1rem;
  margin: 0px auto;
  border-radius: 0.08rem;
  overflow: hidden;
}

.help {
  font-size: 0.14rem;
}

.elder .help {
  font-size: 0.18rem;
}

.jbIcon {
  position: absolute;
  top: -0.06rem;
  right: 0.1rem;
  width: 0.29rem;
  height: 0.16rem;
}

.elder .fiveDiv div.fC {
  width: 33.3%;
}