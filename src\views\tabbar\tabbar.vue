<template>
    <div class="tabbar">
        <!-- <router-view></router-view> -->
        <router-view />
        <van-tabbar
            v-if="!thirdFlag"
            v-model="active"
            :active-color="$store.state.primaryColor"
            inactive-color="#333333"
            @change="onChange"
            :before-change="beforeChange"
        >
            <van-tabbar-item name="home">
                <span>首页</span>
                <template #icon="props">
                    <img :src="props.active ? home.active : home.inactive" />
                </template>
            </van-tabbar-item>
            <van-tabbar-item v-if="!isfromWj" name="consult">
                <span>咨询</span>
                <template #icon="props">
                    <img
                        :src="props.active ? consult.active : consult.inactive"
                    />
                </template>
            </van-tabbar-item>
            <!-- <van-tabbar-item name="shop" v-if="!$store.state.isWjAliMini">
                <span>商城</span>
                <template #icon="props">
                    <img :src="props.active ? shop.active : shop.inactive" />
                </template>
            </van-tabbar-item> -->
            <!-- <van-tabbar-item name="physical" v-if="!$store.state.isWjAliMini">
                <span>体检</span>
                <template #icon="props">
                    <img
                        :src="
                            props.active ? physical.active : physical.inactive
                        "
                    />
                </template>
            </van-tabbar-item> -->
            <van-tabbar-item name="talk" v-if="isfromWj">
                <span>会话</span>
                <template #icon="props">
                    <img :src="props.active ? talk.active : talk.inactive" />
                </template>
            </van-tabbar-item>
            <van-tabbar-item name="mine">
                <span>我的</span>
                <template #icon="props">
                    <img :src="props.active ? mine.active : mine.inactive" />
                </template>
            </van-tabbar-item>
        </van-tabbar>
    </div>
</template>

<script>
import Vue from "vue";
import { Tabbar, TabbarItem } from "vant";
import common from "@/util/common";

Vue.use(Tabbar);
Vue.use(TabbarItem);
export default {
    data() {
        return {
            active: "home",
            home: {
                active: require("@/images/img/home1.png"),
                inactive: require("@/images/img/home.png"),
            },
            consult: {
                active: require("@/images/img/ask1.png"),
                inactive: require("@/images/img/ask.png"),
            },
            shop: {
                active: require("@/images/img/home1.png"),
                inactive: require("@/images/img/home.png"),
            },
            physical: {
                active: require("@/images/img/home1.png"),
                inactive: require("@/images/img/home.png"),
            },
            talk: {
                active: require("@/images/img/talk1.png"),
                inactive: require("@/images/img/talk.png"),
            },
            mine: {
                active: require("@/images/img/mine1.png"),
                inactive: require("@/images/img/mine.png"),
            },
            thirdFlag: false,
            isfromWj: false,
        };
    },
    created() {
        console.log("监听路由", this.active);
        console.log(
            "监听路由this.$store.state.isWjAliMin",
            this.$store.state.isWjAliMin
        );
        if (sessionStorage.getItem("alifrom") == "WjAlimin") {
            // 卫健过来 展示会话 不显示咨询
            this.isfromWj = true;
        }
        if (this.$store.state.isWjAliMini) {
            this.active = "home";
        } else {
            this.active = this.$route.name;
            console.log(this.active, "监听路由变化");
        }

        console.log("===---000009999000---", this.$route.name, this.active);
        this.thirdFlag =
            window.localStorage.getItem("orgId") || common.getUrlParam("orgId");
        let origin = common.getUrlParam("origin");
        if (!window.localStorage.getItem("interHosp_origin")) {
            window.localStorage.setItem("interHosp_origin", origin);
        }
        if (origin && origin == "wjAliMini") {
            // let primaryColor = "#376af5";
            this.$store.commit("saveIsWjAliMini", true);
            // this.$store.commit("savePrimaryColor", primaryColor);
        }
        document.documentElement.style.setProperty(
            "--primary-color",
            this.$store.state.primaryColor
        );

        // if (this.$store.state.isWjAliMini) {
        //   this.home.active = require("@/images/img/home2.png");
        //   this.mine.active = require("@/images/img/mine2.png");
        // }
    },
    watch: {
        $route: function () {
            console.log("watch里面", this.$route.name);
            if (this.$store.state.isWjAliMini) {
                this.active = "home";
            } else {
                this.active = this.$route.name;
                console.log(this.active, "监听路由变化");
            }
        },
    },

    methods: {
        async onChange(active) {
            console.log("参数", active);
            if (this.$store.state.isWjAliMini) {
                if (active == "home") return;
                //卫健 跳转小程序
                let miniId = "2021002138635948";
                let url = location.origin + location.pathname + `#/${active}`;
                let jumpUrl = `/pages/index/index?returnURL=${url}`;
                my.postMessage({
                    action: "gotoMini",
                    appId: miniId,
                    url: jumpUrl,
                    authStatus: "1",
                });
            } else {
                if (
                    active == "home" &&
                    navigator.userAgent.indexOf("AliApp") > -1
                ) {
                    if (
                        window.sessionStorage.getItem("appSource") &&
                        window.sessionStorage.getItem("appSource") === "XsmhMin"
                    ) {
                        // 跳回萧山门户
                        let miniId = "2021004166653329";
                        let jumpUrl = `pages/index/index`;
                        my.postMessage({
                            action: "gotoMini",
                            appId: miniId,
                            url: jumpUrl,
                            authStatus: "0",
                        });
                        return;
                    } else {
                        // 在我司小程序里点击首页跳转卫健小程序首页
                        let miniId = "2021005100630784";
                        // let url = location.origin + location.pathname + `#/home?origin=wjAliMini`;
                        let jumpUrl = `pages/secondPage/secondPage`;
                        my.postMessage({
                            action: "gotoMini",
                            appId: miniId,
                            url: jumpUrl,
                            authStatus: "0",
                        });
                        return;
                    }
                }
                // // 先获取用户信息
                // if (!this.$store.state.uInfo) {
                //     await this.$store.dispatch("getLogin");
                // }
                if (this.active == "talk") {
                    this.$router.push({ path: "/talk" });
                } else if (this.active == "consult") {
                    console.log("11234333", process.env.VUE_APP_IHOSP);
                    // 跳转互联网医院的 咨询搜索页面
                    let hosp = process.env.VUE_APP_IHOSP || "h5InterHosp";
                    // 默认市民卡，
                    let origin = "";
                    if (
                        this.$route.query.origin ||
                        window.localStorage.getItem("interHosp_origin")
                    ) {
                        origin =
                            this.$route.query.origin ||
                            window.localStorage.getItem("interHosp_origin");
                    }
                    window.location.href =
                        location.origin +
                        location.pathname +
                        "#/searchDoctorList?type=ask&from=home";
                } else if (this.active == "shop") {
                    window.location.href =
                        "https://zhenxuan.96225.com/skMallMobile/#/mall?serviceCode=jiankang1215&flowServiceCode=jiankangFlow&approachNo=jtjiankang";
                } else if (this.active == "physical") {
                    window.location.href =
                        "https://wx.helianwap.com/mall/exam-list.html?hlwholesource=hzsmk&tradeSource=hzsmk";
                } else {
                    this.$router.replace({ name: this.active });
                }
            }
        },
        // false 阻止tab切换
        beforeChange(e) {
            console.log("beforeChange", e);
            if (this.$store.state.isWjAliMini) {
                if (e == "mine") {
                    let miniId = "2021002138635948";
                    let url = location.origin + location.pathname + `#/mine`;
                    let jumpUrl = `/pages/index/index?returnURL=${url}`;
                    my.postMessage({
                        action: "gotoMini",
                        appId: miniId,
                        url: jumpUrl,
                        authStatus: "1",
                    });
                } else {
                    return false;
                }
            } else {
                return true;
            }
        },
    },
};
</script>

<style scoped>
.tabbar {
    height: 100%;
}
::v-deep .van-tabbar {
    height: 66px;
}
::v-deep .van-hairline--top-bottom::after,
.van-hairline-unset--top-bottom::after {
    border-width: 0;
}
</style>
