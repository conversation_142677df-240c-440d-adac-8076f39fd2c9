<!--
 * @Author: your name
 * @Date: 2025-05-28 09:36:00
 * @LastEditTime: 2025-05-28 14:59:16
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 三联展台
 * @FilePath: \h5-interhosp\src\components\activity\3lian.vue
-->
<template>
    <div v-show="isShow" class="Mod">
        <div
            class="Mod-item"
            v-for="(item, index) in commonlist"
            :key="index"
            @click="jumpUrl(item, childStageId, childStageName)"
        >
            <img class="ModHead" v-lazy="item.iconUrl" alt="" />
            <div>
                <p>{{ item.applicationName }}</p>
                <p class="sub">{{ item.applicationSubName }}</p>
            </div>
            <!-- <div
                class="divider"
                v-if="index != item.childStageList.length - 1"
            ></div> -->
        </div>
    </div>
</template>
<script>
import tools from "@/util/tools";
export default {
    name: "H5Interhosp3lian",
    props: {
        datalists: {
            type: Object,
        },
        stageName: {
            type: String,
        },
        stageId: {
            type: String,
        },
    },
    data() {
        return {
            commonlist: "",
            elementId: "",
            isShow: false,
            childStageId: "",
            childStageName: "",
        };
    },

    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("活动三联展台", temp);
        if (
            temp.stageTypeName === "互联网医院三联展台" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.commonlist = temp.childStageList;
            this.childStageId = temp.childStageId;
            this.childStageName = temp.childStageName;
        }
    },

    methods: {
        jumpUrl(item, id, name) {
            console.log("跳转参数", item);
            if (item.jumpUrl) {
                tools
                    .handleSetPoint({
                        stageId: this.stageId,
                        childStageId: id,
                        stageAppId: item.applicationId,
                        trackingContent: `${this.stageName}-${name}-${item.elementContent}`,
                        businessName: "activity",
                    })
                    .then(() => {
                        tools.jumpUrlManage(item);
                    });
            }
        },
    },
};
</script>
<style scoped>
.Mod {
    background: #ffffff;
    margin: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20px 0 15px;
    border-bottom: 1px solid #f1f1f1;
}
.Mod-item {
    font-size: 0;
    text-align: center;
    position: relative;
    flex: 1;
    flex-shrink: 0;
}
.ModHead {
    width: 55px;
    background-size: 100% auto;
    margin-bottom: 9px;
}
.Mod .sub {
    font-size: 12px;
    font-family: "Source Han Sans SC";
    font-weight: 400;
    color: #999999;
    margin-top: 6px;
    line-height: 1;
}
.divider {
    content: "";
    width: 1px;
    height: 38px;
    background: #f1f1f1;
    position: absolute;
    right: 1px;
    top: 50%;
    transform: translateY(-50%);
}
</style>