<template>
    <div class="consultItem">
        <div class="hospTitle">
            <div class="hospName">{{ data.hospname }}</div>
            <div
                :class="
                    ['0', '1', '2'].indexOf(data.orderStatus) > -1
                        ? 'consultStatus'
                        : 'consultStatusDefault'
                "
            >
                {{ data.orderStatus | orderStatus }}
            </div>
        </div>
        <div class="doctorInfo">
            <div class="docImg">
                <img
                    :src="
                        data.photo
                            ? data.photo
                            : require('@/images/img/default_head.png')
                    "
                    alt=""
                />
            </div>
            <div class="docBody">
                <div class="topInfo">
                    <div class="docName">{{ data.doctorName }}</div>
                    <div
                        :class="
                            data.busiType == 21
                                ? 'twConsult'
                                : data.busiType == 22
                                ? 'dhConsult'
                                : 'fzConsult'
                        "
                    >
                        {{ data.busiType | busiType }}
                    </div>
                    <div class="teamType" v-if="data.isTeamOrder == 1">
                        团队
                    </div>
                </div>
                <div class="bottomInfo">
                    <div class="docLevel" v-if="data.isTeamOrder != 1">
                        {{ data.doctorTitleName }}
                    </div>
                    <div class="divider" v-if="data.isTeamOrder != 1">|</div>
                    <div class="deptName">{{ data.deptName }}</div>
                </div>
            </div>
        </div>
        <div class="consultInfo">
            <div class="consultName">
                <div class="titleName">患者姓名:</div>
                <div class="content">{{ data.patientName }}</div>
            </div>
            <div class="applyTime">
                <div class="titleName">申请时间:</div>
                <div class="content">{{ data.createTime }}</div>
            </div>
        </div>
        <div
            :class="
                data.orderStatus == 1 && data.busiType == 22
                    ? 'oprateBtn'
                    : 'oprateBtn flexRight'
            "
            v-if="['0', '1', '2', '3', '4'].indexOf(data.orderStatus) > -1"
        >
            <div
                class="phoneTip"
                v-if="data.orderStatus == 1 && data.busiType == 22"
            >
                请留意<span>13024294878</span>的电话来电
            </div>
            <!-- orderSource=1才是我们系统的订单，第三方订单不显示下方按钮 -->
            <div v-show="data.orderSource == 1" class="btnList">
                <div
                    class="cancelBtn"
                    @click.stop="cancelConsult"
                    v-if="['0', '1'].indexOf(data.orderStatus) > -1"
                >
                    取消咨询
                </div>
                <div
                    class="cancelBtn"
                    @click.stop="toEvaluation"
                    v-if="['3'].indexOf(data.orderStatus) > -1"
                >
                    去评价
                </div>
                <div
                    class="cancelBtn"
                    @click.stop="cancelConsult"
                    v-if="
                        ['2'].indexOf(data.orderStatus) > -1 &&
                        data.busiType != 22
                    "
                >
                    结束咨询
                </div>
                <div
                    class="cancelBtn"
                    style="margin-left: 0.1rem"
                    @click.stop="reConsultation"
                    v-if="
                        ['3', '4'].indexOf(data.orderStatus) > -1 &&
                        data.busiType != 22 &&
                        data.isTeamOrder == 0
                    "
                >
                    再次咨询
                </div>
                <div
                    class="consultBtn"
                    @click.stop="toConsult"
                    v-if="
                        (data.orderStatus == '1' &&
                            data.isTeamOrder == 0 &&
                            data.busiType != 22) ||
                        (data.orderStatus == '2' && data.busiType != 22) ||
                        (data.orderStatus == '3' && data.busiType != 22)
                    "
                >
                    进入咨询
                </div>
                <div
                    class="consultBtn"
                    @click.stop="toPayOrder"
                    v-if="['0'].indexOf(data.orderStatus) > -1"
                >
                    立即支付
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { confirmOrder, submitOrderAgainCheck } from "@/api/consult";
import { query, finish, cancel } from "@/api/consult.js";
import common from "@/util/common.js";
import util from "@/util/util.js";
import { Dialog } from "vant";
import tools from "@/util/tools";
export default {
    props: {
        data: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {};
    },
    filters: {
        orderStatus(value) {
            let status = "";
            switch (value) {
                case "0":
                    status = "待支付";
                    break;
                case "1":
                    status = "待接单";
                    break;
                case "2":
                    status = "咨询中";
                    break;
                case "3":
                    status = "待评价";
                    break;
                case "4":
                    status = "已结束";
                    break;
                case "51":
                    status = "已取消";
                    break;
                case "52":
                    status = "已取消";
                    break;
                case "53":
                    status = "已取消";
                    break;
                case "54":
                    status = "已取消";
                    break;
                case "6":
                    status = "已拒绝";
                    break;
                case "7":
                    status = "已退回";
                    break;
                case "8":
                    status = "退款成功";
                    break;
                default:
                    status = "其他";
                    break;
            }
            return status;
        },
        busiType(value) {
            let status = "";
            switch (value) {
                case "1":
                    status = "在线复诊";
                    break;
                case "2":
                    status = "咨询";
                    break;
                case "21":
                    status = "图文咨询";
                    break;
                case "22":
                    status = "电话咨询";
                    break;
                case "3":
                    status = "处方";
                    break;
                default:
                    status = "";
                    break;
            }
            return status;
        },
    },
    methods: {
        dencryptHeader: common.dencryptHeader,
        cancelConsult() {
            const that = this;
            this.$dialogBox({
                title:
                    this.data.orderStatus == "2"
                        ? "结束咨询"
                        : "确定取消咨询吗?",
                content:
                    this.data.orderStatus == "2"
                        ? "我的问题已解决"
                        : this.data.orderStatus == "1"
                        ? "一天内取消次数不超过3次"
                        : "",
                confirmTxt: this.data.orderStatus == "2" ? "结束" : "确认取消",
                cancelTxt: "再问问",
                cancelCallback: function () {},
                confirmCallback: function () {
                    that.finishiCancel();
                },
            });
        },
        finishiCancel() {
            this.checkOrderStatus((status) => {
                const data = {
                    orderNo: this.data.orderNo,
                };
                if (status == "2") {
                    finish(data).then(() => {
                        this.$router.push({
                            path: "/consultEvaluation",
                            query: {
                                orderNo: this.data.orderNo,
                            },
                        });
                    });
                } else if (status == "0" || status == "1") {
                    cancel(data).then((res) => {
                        if (res) {
                            this.$emit("refreshData");
                        }
                    });
                } else {
                    util.showToast("该订单状态已变更");
                    this.$emit("refreshData");
                }
            });
        },
        toEvaluation() {
            this.$router.push({
                path: "/consultEvaluation",
                query: {
                    orderNo: this.data.orderNo,
                },
            });
        },
        reConsultation() {
            submitOrderAgainCheck({
                orderInfoId: this.data.orderInfoId,
            }).then((res) => {
                if (res.success !== 1) {
                    Dialog.alert({
                        message: res.respDesc,
                        confirmButtonColor: "#00CCA6",
                        confirmButtonText: "好的",
                        className: "checkDialog",
                    });
                } else {
                    if (res.value && res.value.orderNo) {
                        const token = window.localStorage.getItem("token");
                        const IMId = this.data.imId;
                        let originStr =
                            location.hostname.indexOf("10.100.10") > -1
                                ? "https://jsbceshi.hfi-health.com:18188"
                                : location.origin;
                        const url = `${originStr}/${process.env.VUE_APP_TALK}/#/chat-detail?id=${IMId}&userType=2&token=${token}&orderNo=${res.value.orderNo}`;
                        console.log("会话地址:", url);
                        window.location.href = url;
                        return;
                    }
                    let url =
                        location.origin +
                        location.pathname +
                        `#/consultService?module=imageConsultApply&staffId=${this.data.staffId}&deptId=${this.data.deptId}&unicode=${this.data.unicode}&latestOrderNo=${this.data.orderNo}`;
                    tools.jumpUrl(url, "2");
                }
            });
        },
        toConsult() {
            const uInfo =
                JSON.parse(window.localStorage.getItem("userInfo")) || {};
            const token = window.localStorage.getItem("token");
            // const IMId = this.$md5(uInfo.paperNo);
            const IMId = this.data.imId;
            const orderNo = this.data.orderNo;
            // const certNum = this.data.certNum;
            // const doctorCertNum = this.data.doctorCertNum;
            let originStr =
                location.hostname.indexOf("10.100.10") > -1
                    ? "https://jsbceshi.hfi-health.com:18188"
                    : location.origin;
            const url = `${originStr}/${process.env.VUE_APP_TALK}/#/chat-detail?id=${IMId}&userType=2&token=${token}&orderNo=${orderNo}`;
            console.log("会话地址:", url);
            window.location.href = url;
        },
        toPayOrder() {
            console.log(this.data);
            this.checkOrderStatus((status) => {
                if (status == 0) {
                    // // 优惠券功能增加后，直接跳转到确认订单页
                    // let orderInfo = {
                    //     hospName: this.data.hospName,
                    //     orderNo: this.data.orderNo,
                    //     busiType: this.data.busiType,
                    //     unicode: this.data.unicode,
                    // };
                    // this.$router.push({
                    //     path: "/consultServiceInfo",
                    //     query: orderInfo,
                    // });
                    // return;
                    let logTraceId = new Date().getTime();
                    // let merchantId = "2021050866"; // 测试数据
                    //   let alipayUserId = this.$store.state.alipayUserId
                    //     ? this.$store.state.alipayUserId
                    //     : window.localStorage.getItem("alipayUserId");
                    let alipayUserId =
                        window.localStorage.getItem("alipayUserId");
                    let remark2Obj = {};
                    if (navigator.userAgent.indexOf("AliApp") > -1) {
                        remark2Obj = {
                            openid: alipayUserId,
                        };
                    } else if (
                        navigator.userAgent
                            .toLowerCase()
                            .indexOf("micromessenger") > -1
                    ) {
                        remark2Obj = {
                            openid: localStorage.getItem("wxOpenid"),
                            appid: window.localStorage.getItem("wxAppid"),
                        };
                    }
                    console.log("***", remark2Obj);

                    let data = {
                        orderNo: this.data.orderNo,
                        merchantId: this.data.merchantId,
                        unicode: this.data.unicode,
                        amount: this.data.totalAmount + "",
                        logTraceID: logTraceId,
                        callBackUrl:
                            location.origin +
                            location.pathname +
                            "#/myConsult?orderNo=" +
                            this.data.orderNo,
                        remark2: JSON.stringify(remark2Obj), //str
                    };
                    console.log("确认订单传参----", data);
                    confirmOrder(data).then((res) => {
                        if (res) {
                            if (
                                window.localStorage.getItem("localId") &&
                                this.data.busiType == "21"
                            ) {
                                // 图文咨询和有localid的情况下，给收银台传localid进行收银台埋点
                                window.location.href = `${
                                    res.cashierUrl
                                }&localid=${window.localStorage.getItem(
                                    "localId"
                                )}`;
                            } else {
                                window.location.href = res.cashierUrl;
                            }
                        }
                    });
                } else {
                    util.showToast("该订单已超时未支付，请重新咨询");
                    this.$emit("refreshData");
                }
            });
        },
        checkOrderStatus(callback) {
            const data = {
                orderNo: this.data.orderNo,
            };
            query(data).then((res) => {
                callback(res.orderStatus);
            });
        },
    },
};
</script>
<style lang="less" scoped>
.consultItem {
    width: 3.45rem;
    // height: 1.805rem;
    font-family: PingFang SC;
    background: #ffffff;
    border-radius: 0.08rem;
    margin: auto;
    margin-bottom: 0.12rem;
    position: relative;
    top: 0.12rem;
    padding: 0.145rem 0.11rem 0.16rem 0.105rem;

    .hospTitle {
        padding-bottom: 0.15rem;
        border-bottom: 1px solid #e8e9ec;
        display: flex;
        justify-content: space-between;

        .hospName {
            font-size: 0.14rem;
            font-weight: 400;
            color: #333333;
        }

        .consultStatus,
        .consultStatusDefault {
            font-size: 0.14rem;
            font-weight: 400;
            color: #fe963a;
        }

        .consultStatusDefault {
            color: #999999;
        }
    }

    .doctorInfo {
        display: flex;
        justify-content: left;
        margin-top: 0.13rem;

        .docImg {
            img {
                display: block;
                width: 0.47rem;
                height: 0.47rem;
            }
        }

        .docBody {
            margin-left: 0.125rem;

            .topInfo {
                display: flex;
                align-items: center;

                .docName {
                    font-size: 0.15rem;
                    font-weight: 500;
                    color: #333333;
                }

                .twConsult,
                .dhConsult,
                .fzConsult,
                .teamType {
                    width: 0.54rem;
                    height: 0.16rem;
                    line-height: 0.16rem;
                    background: linear-gradient(
                        90deg,
                        #3fe5cf 0%,
                        #26c8b3 100%
                    );
                    border-radius: 0.02rem;
                    font-size: 0.1rem;
                    font-family: PingFang SC;
                    font-weight: 400;
                    color: #ffffff;
                    text-align: center;
                    margin-left: 0.1rem;
                }
                .dhConsult {
                    background: linear-gradient(
                        90deg,
                        #ffcb7f 0%,
                        #ffb45a 100%
                    );
                }
                .fzConsult {
                    background: linear-gradient(
                        90deg,
                        #4cbcfb 0%,
                        #5e8dfa 100%
                    );
                }
                .teamType {
                    background: #fe963a;
                    width: 0.3rem;
                }
            }

            .bottomInfo {
                font-size: 0.13rem;
                font-weight: 400;
                color: #666666;
                display: flex;
                margin-top: 0.12rem;

                .divider {
                    margin: 0 0.1rem;
                }
            }
        }
    }

    .consultInfo {
        margin-top: 0.15rem;
        padding-bottom: 0.05rem;
        border-bottom: 1px solid #e8e9ec;

        .consultName,
        .applyTime {
            display: flex;
            justify-content: space-between;
            font-size: 0.14;
            margin-bottom: 0.1rem;

            .titleName {
                font-weight: 400;
                color: #888888;
            }

            .content {
                font-weight: 400;
                color: #333333;
            }
        }
    }

    .oprateBtn {
        margin-top: 0.16rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .phoneTip {
            span {
                color: #fe963a;
            }
        }
        .btnList {
            display: flex;
            justify-content: right;
        }
        .cancelBtn,
        .consultBtn {
            width: 0.77rem;
            height: 0.28rem;
            border: 1px solid #e8e9ec;
            border-radius: 0.14rem;
            line-height: 0.28rem;
            text-align: center;
        }

        .consultBtn {
            background: #01cda7;
            color: #ffffff;
            margin-left: 0.1rem;
        }
    }
    .flexRight {
        display: flex;
        justify-content: right;
    }
}
</style>
