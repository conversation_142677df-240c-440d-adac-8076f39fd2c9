<template>
  <div class="evaContainer">
    <div class="hospContnet">
      <img src="./../../images/img/hosp.png" alt="">
      <div class="hospName">{{ list.hospname }}</div>
    </div>
    <div class="docInfo">
      <img :src="list.photo" alt="">
      <div class="rightContent">
        <div class="docName">{{ list.doctorName }}</div>
        <div class="deptInfo">
          <div>{{ list.doctorTitleName }}</div>
          <div>{{ list.deptName }}</div>
        </div>
      </div>
    </div>
    <div class="evaContent">
      <div class="evaStar">
        <div class="evaTitle">综合评价</div>
        <van-rate v-model="starValue" color="#FE963A" size="30px" gutter="14px" :disabled="disabled" />
      </div>
      <div class="evaInput">
        <van-field
          v-model="content"
          :disabled="disabled"
          rows="8"
          autosize
          type="textarea"
          placeholder="谈谈您本次就诊对医生的印象吧~"
          maxlength="200"
          show-word-limit
        />
      </div>
    </div>
    <div class="submitBtn" @click="submitEva">提交</div>
  </div>
</template>
<script>
import { Rate, Field } from 'vant';
import { query, orderEvaluate } from '@/api/consult.js' 
import common from '@/util/util.js'
export default {
  components: {
    [Rate.name]: Rate,
    [Field.name]: Field,
  },
  data () {
    return {
      disabled: false,
      starValue: 0,
      content: '',
      list: {}
    }
  },
  mounted() {
    this.query();
  },
  methods: {
    query () {
      const data = {
        orderNo: this.$route.query.orderNo
      }
      query(data).then((res) => {
        this.list = res;
        if( this.list.orderStatus == 4 ) {
          this.disabled = true
          common.showToast("该订单已评价，请勿重复评价");
        }
      });
    },
    submitEva() {
      if (this.disabled) {
        common.showToast("该订单已评价，请勿重复评价");
        return
      }
      if (this.starValue == 0) {
        common.showToast("请为该医生进行星级评定");
        return;
      }
      const that = this
      const data = {
        orderNo: this.$route.query.orderNo,
        score: this.starValue * 2,
        content: this.content,
        logTraceID: common.generateUUID()
      }
      orderEvaluate(data).then(res => {
        if (res) {
          this.$dialogBox({
            title: '提交成功',
            content: '',
            confirmTxt: '返回首页',
            cancelTxt: '',
            cancelCallback: function () {},
            confirmCallback: function () {
              that.$router.push({
                path: '/home'
              })
            },
          });
        }
      })
    },
    
  }
}
</script>
<style lang="less" scoped>
.evaContainer {
  font-family: PingFang SC;
  padding: 0.12rem 0.15rem 0 0.15rem;
  .hospContnet {
    background-color: #fff;
    border-radius: 0.08rem;
    padding: 0.2rem 0.15rem;
    display: flex;
    align-items: center;
    img {
      display: block;
      width: 0.165rem;
      height: 0.16rem;
    }
    .hospName {
      font-size: 0.16rem;
      font-weight: bold;
      color: #333333;
      margin-left: 0.095rem;
    }
  }
  .docInfo {
    background-color: #ffffff;
    margin-top: 0.12rem;
    border-radius: 0.08rem;
    padding: 0.2rem 0.15rem;
    display: flex;
    img {
      display: block;
      width: 0.6rem;
      height: 0.6rem;
      border-radius: 50%;
    }
    .rightContent {
      margin-left: 0.15rem;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .docName {
        font-size: 0.21rem;
        font-weight: bold;
        color: #333333;
      }
      .deptInfo {
        display: flex;
        font-size: 0.14rem;
        font-weight: 400;
        color: #888888;
        div {
          margin-right: 0.1rem;
        }
      }
    }
  }
  .evaContent {
    background-color: #fff;
    padding: 0.15rem;
    margin-top: 0.12rem;
    .evaStar {
      border-bottom: 1px solid #E8E9EC;
      display: flex;
      align-items: center;
      padding-bottom: 0.16rem;
      .evaTitle {
        margin-right: 0.125rem;
      }
    }
    .evaInput {
      margin-top: 0.145rem;
      /deep/ .van-cell{
        padding: 0;
      }
    }
  }
  .submitBtn {
    position: absolute;
    bottom: 0.45rem;
    width: 3.45rem;
    height: 0.4rem;
    background: #01CDA7;
    border-radius: 0.2rem;
    font-size: 0.16rem;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 0.4rem;
    text-align: center;
  }
}
</style>