import fetch from "./http";
import util from "../util/util";

// 查询未读消息数量
export const queryUnReadMessageCount = data => {
    return fetch(
        "/inthos/messagePush/queryUnReadMessageCount",
        JSON.stringify(data),
        "post"
    )
        .then(response => {
            if (response.success == 1) {
                return response.value
            } else {
                return ''
            }
        })
        .catch(err => {
            return ''
        });
};

/*
查询消息推送列表
入参
userId
displayLocationType（显示位置，0=消息中心，1=电话咨询业务通知，2=患者评价，3=团队业务通知）
*/
export const queryMessageList = data => {
    return fetch(
        "/inthos/messagePush/queryMessageList",
        JSON.stringify(data),
        "post"
    )
        .then(response => {
            if (response.success == 1) {
                return response.value
            } else {
                return ''
            }
        })
        .catch(err => {
            return ''
        });
};

// 将消息读取状态改为已读  入参：messageId
export const updateReadStatus = data => {
    return fetch(
        "/inthos/messagePush/updateReadStatus",
        JSON.stringify(data),
        "post"
    )
        .then(response => {
            if (response.success == 1) {
                return response.value
            } else {
                return ''
            }
        })
        .catch(err => {
            return ''
        });
};

// 一键已读
export const updateAllReadStatus = data => {
    return fetch(
        "/inthos/messagePush/updateAllReadStatus",
        JSON.stringify(data),
        "post"
    )
        .then(response => {
            if (response.success == 1) {
                setTimeout(() => {
                    util.showToast('已将所有消息标为已读')
                }, 300)
                return response.value
            } else {
                setTimeout(() => {
                    util.showToast('暂无未读消息')
                }, 300)
            }
        })
        .catch(err => {
            return ''
        });
};
