<!--
 * @Author: your name
 * @Date: 2025-05-28 09:38:05
 * @LastEditTime: 2025-06-05 14:43:32
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 多行应用展台
 * @FilePath: \h5-interhosp\src\components\activity\moreLine.vue
-->
<template>
    <div v-show="isShow">
        <div class="Mod-stage pd-b0 conStyle">
            <div
                class="stageList"
                v-for="(itemin, index) in commonlist"
                :key="index"
            >
                <stage-item
                    :data="itemin"
                    :stageId="stageId"
                    :childStageId="childStageId"
                    position="首页"
                ></stage-item>
            </div>
        </div>
    </div>
</template>

<script>
import StageItem from "./../../views/home/<USER>/stageItem.vue";
import tools from "@/util/tools";
export default {
    name: "H5InterhospMoreLine",
    components: {
        StageItem,
    },
    props: {
        datalists: {
            type: Object,
        },
        stageName: {
            type: String,
        },
        stageId: {
            type: String,
        },
    },
    data() {
        return {
            commonlist: "",
            elementId: "",
            isShow: false,
            childStageId: "",
            childStageName: "",
        };
    },

    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("活动页多行应用", temp);
        if (
            temp.stageTypeName === "多行应用" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.commonlist = temp.childStageList;
            this.childStageId = temp.childStageId;
            this.childStageName = temp.childStageName;
        }
    },

    methods: {
        jumpUrl(item, id, name) {
            console.log("跳转参数", item);
            if (item.jumpUrl) {
                tools
                    .handleSetPoint({
                        stageId: this.stageId,
                        childStageId: id,
                        stageAppId: item.applicationId,
                        trackingContent: `${this.stageName}-${name}-${item.elementContent}`,
                        businessName: "activity",
                    })
                    .then(() => {
                        tools.jumpUrlManage(item);
                    });
            }
        },
    },
};
</script>
<style scoped>
.conStyle {
    background-color: #fff;
    width: 325px;
    border-radius: 8px;
    margin: 0 auto;
    margin-bottom: 10px;
}
.Mod-stage {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    padding: 15px 10px 8px;
    margin-top: 10px;
}
.pd-b0 {
    padding-bottom: 0;
}
.stageList {
    flex-shrink: 0;
    width: 20%;
    margin-bottom: 15px;
}
</style>