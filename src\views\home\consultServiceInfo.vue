<template>
    <div>
        <div class="backImg"></div>
        <div class="ModContainer">
            <div class="card flexsty fctn">
                <img
                    class="hospIcon"
                    src="@/images/img/zx_hospIcon.png"
                    alt=""
                />
                <p class="hospName">{{ hospName }}</p>
            </div>
            <div class="card briefInfo">
                <img
                    class="headImg"
                    :src="
                        data.photo
                            ? data.photo
                            : data.sexCode == '1'
                            ? require('@/images/search/man.png')
                            : require('@/images/search/woman.png')
                    "
                    alt=""
                />
                <div>
                    <p class="doctorName">{{ data.doctorName }}</p>
                    <span v-if="data.isTeamOrder != '1'">{{
                        data.doctorTitleName
                    }}</span>
                    <span>{{ data.deptName }}</span>
                </div>
            </div>
            <div class="card">
                <div class="flexsty">
                    <p class="title">就诊人</p>
                    <p class="descTxt">
                        {{ plaintextName || data.patientName }}
                    </p>
                </div>
                <div class="flexsty">
                    <p class="title">健康档案</p>
                    <p class="descTxt">
                        {{
                            data.patientDocVisible == "1"
                                ? "医生可见"
                                : "医生不可见"
                        }}
                    </p>
                </div>
                <div v-if="data.busiType == '22'">
                    <div class="flexsty">
                        <p class="title">接听电话</p>
                        <p class="descTxt">{{ data.phoneConsultCalled }}</p>
                    </div>
                    <div class="flexsty">
                        <p class="title">预约时间</p>
                        <p class="descTxt">{{ data.phoneConsultTime }}</p>
                    </div>
                </div>
                <div class="flexsty">
                    <p class="title">病情描述</p>
                    <div>
                        <p
                            class="descTxt"
                            v-if="plaintextDesc || data.descriptions"
                        >
                            {{ plaintextDesc || data.descriptions }}
                        </p>
                        <div class="voiceInfo flexsty fctn" v-if="data.voices">
                            <div
                                class="waveform"
                                @click="playVoice"
                                v-if="!this.isPlaying"
                            >
                                <img src="@/images/img/record.png" alt="" />
                            </div>
                            <div class="waveform" v-else>
                                <img src="@/images/img/record.gif" alt="" />
                            </div>
                            <p class="voiceTxt">{{ data.voices.time }}</p>
                        </div>
                        <div
                            class="picList"
                            v-if="data.pictures && data.pictures.length > 0"
                        >
                            <div
                                class="pici"
                                v-for="(file, index) in data.pictures"
                                :key="index"
                                @click="imagePreview(file.content)"
                            >
                                <img :src="file.content" alt="" srcset="" />
                                <div class="cover">
                                    {{ file.type | filtersPicTypeName }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-show="busiType == 21" class="card">
                <div @click="getCoupon" class="flex-btwn-c">
                    <p class="title nobtom">选择优惠券</p>
                    <p v-if="youhui" class="coupon_money">- ¥ {{ youhui }}</p>
                    <p v-else style="color: #999" class="coupon_money">
                        暂无可用优惠券
                    </p>
                    <img
                        class="coupon_img"
                        src="./../../images/coupon_right.png"
                    />
                </div>
            </div>
            <div class="card">
                <div class="flex-btwn-c">
                    <p class="title">支付类型</p>
                    <p class="descTxt">{{ typeName }}</p>
                </div>
                <div class="flex-btwn-c">
                    <p class="title">总金额</p>
                    <p>
                        <span class="descTxt">¥ {{ data.totalAmount }}</span>
                    </p>
                </div>
                <div
                    v-show="data.discountAmount == '0' || data.discountAmount"
                    class="flex-btwn-c"
                >
                    <p class="title">优惠抵扣</p>
                    <p>
                        <span class="amount"
                            >- ¥ {{ data.discountAmount }}</span
                        >
                    </p>
                </div>
                <div
                    v-show="data.calAmount == '0' || data.calAmount"
                    class="flex-btwn-c"
                >
                    <p class="title nobtom">应付金额</p>
                    <p>
                        <span class="descTxt">¥ {{ data.calAmount }}</span>
                    </p>
                </div>
            </div>
            <div class="card flex-btwn-c confirm">
                <div>
                    咨询服务费：<span class="amt"
                        >¥{{ data.calAmount || data.totalAmount }}</span
                    >
                </div>
                <div class="btn" @click="submit">确认订单</div>
            </div>
        </div>
        <van-popup v-model="couponShow" position="bottom" round>
            <div class="couponCon">
                <p>优惠券详情</p>

                <div class="noCoupon">
                    <van-list
                        v-model="loading"
                        :finished="finished"
                        @load="getlist"
                        :offset="10"
                        finished-text=""
                    >
                        <change-coupon
                            :couponList.sync="couponList"
                            @beChanged="beChanged"
                        ></change-coupon>
                    </van-list>
                    <div v-show="couponList.length == 0" class="noCoupon">
                        <img src="./../../images/coupon/nodata.png" />
                        <div>暂无可用优惠券</div>
                    </div>
                </div>

                <div @click="confirmCoupon" class="btn-">
                    <div>确定</div>
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script>
import {
    consultOrderQuery,
    confirmOrder,
    discount,
    orderSubmit,
} from "@/api/consult";
import { queryList } from "@/api/coupon.js";
import { optionConsultPicType } from "@/util/dict";
import cryptoJS from "crypto-js";
import { ImagePreview } from "vant";
import common from "@/util/common";
import util from "@/util/util";
import tools from "@/util/tools";
import Vue from "vue";
import { Popup, List } from "vant";
import changeCoupon from "./components/changeCoupon.vue";

Vue.use(Popup);

export default {
    components: {
        changeCoupon,
        [List.name]: List,
    },
    data() {
        return {
            hospName: "",
            data: {
                doctorName: "",
                deptName: "",
                levelName: "",
                patientName: "",
                descriptions: "",
                voices: "",
                pictures: "",
                merchantId: "",
                totalAmount: "",
                orderNo: "",
                busiType: "",
            },
            //1-复诊 21-图文咨询 22-电话咨询 3-处方
            typeName: "",
            typeList: [
                {
                    val: "1",
                    label: "复诊",
                },
                {
                    val: "21",
                    label: "图文咨询",
                },
                {
                    val: "22",
                    label: "电话咨询",
                },
                {
                    val: "3",
                    label: "处方",
                },
            ],
            isPlaying: false,
            unicode: "",
            busiType: "",
            youhui: "",
            finished: false,
            loading: false,
            pageNum: 1,
            couponShow: false,
            couponList: [],
            couponList_: [
                {
                    id: "11",
                    name: "11",
                    couponName: "三八节活动三八节活动三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.01.31 19:10-2024.02.02 23:59",
                    couponType: "1",
                    minThreshold: "30",
                    discount: "5",
                },
                {
                    id: "11",
                    name: "11",
                    couponName:
                        "三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动三八节活动",
                    channel: "仅在微信端-杭州健康通使用",
                    remark: "ceshi",
                    time: "2024.06.25至2024.08.31",
                    couponType: "2",
                    discount: "5",
                },
                {
                    id: "11",
                    couponName: "三八节活动三八节活动",
                    name: "11",
                    time: "2024.06.25至2024.08.31",
                    remark: "",
                    couponType: "3",
                    discount: "5",
                },
                {
                    id: "11",
                    couponName: "11",
                    remark: "ddddddddd",
                    couponType: "0",
                    discount: "5",
                },
            ],
            beUsedCoupon: "",
            // 计算优惠券接口 返回的优惠券列表
            discountCouponCodesList: "",
            plaintextName: "",
            plaintextDesc: "",
        };
    },
    created() {
        this.data = this.$route.query;
        console.log("query参数", this.data);
        this.busiType = this.data.busiType;
        this.hospName = this.data.hospName;
        this.unicode = this.data.unicode;
        let filterI = this.typeList.filter((item) => {
            return item.val == this.data.busiType;
        });
        this.typeName = filterI[0] ? filterI[0].label : "";
        if (sessionStorage.getItem("submit_msg_data")) {
            // 有上个页面(提交订单页)的缓存
            this.data = JSON.parse(sessionStorage.getItem("submit_msg_data"));
            let b = JSON.parse(sessionStorage.getItem("submit_plaintext"));
            this.plaintextName = b.patientName;
            this.plaintextDesc = b.descriptions;
            console.log("提交订单入参", this.data);
            console.log("缓存字段", b);
        } else {
            // this.getInfo();
        }
        if (this.data.busiType == "21") {
            // 只有图文咨询才可以调用优惠券
            // 计算优惠券接口
            this.discountMoney();
        }
    },
    mounted() {},
    methods: {
        dencryptHeader: common.dencryptHeader,

        getInfo() {
            let logTraceId = new Date().getTime();
            let params = {
                orderNo: this.data.orderNo,
                logTraceID: logTraceId,
            };
            const that = this;
            consultOrderQuery(params).then((res) => {
                // 根据订单号查询提交订单时的信息
                console.log("99999", res);
                if (res) {
                    that.data = res;
                    if (res.busiType == "21") {
                        // 只有图文咨询才可以调用优惠券
                        // 计算优惠券接口
                        this.discountMoney();
                    }
                }
            });
        },

        playVoice() {
            let audio = document.createElement("audio");
            audio.src = this.data.voices.voice;
            this.isPlaying = true;
            let that = this;
            audio.addEventListener(
                "ended",
                function () {
                    console.log("播放结束");
                    that.isPlaying = false;
                },
                false
            );
            audio.addEventListener(
                "error",
                function (error) {
                    console.log(`播放错误${error}`);
                    that.isPlaying = false;
                },
                false
            );
            audio.play();
        },
        imagePreview(url) {
            ImagePreview({
                images: [url],
                closeable: true,
                showIndex: false,
            });
        },
        // 提交订单
        submit() {
            const that = this;
            let data = this.data;
            let backurl =
                location.origin + location.pathname + "#/myConsult?orderNo=";
            data.callBackUrl = backurl;
            // 优惠券的传参
            if (this.data.discountAmount) {
                data.discountAmount = this.data.discountAmount;
                data.couponKeys = this.discountCouponCodesList;
                data.calAmount = this.data.calAmount;
            }
            orderSubmit(data).then((response) => {
                console.log("%%%%%#######%%%%%%####", response);
                let res = response.value;
                if (response.success == 1) {
                    if (res) {
                        tools
                            .handleSetPoint({
                                trackingContent: `${this.data.doctorName}|${
                                    this.busiType == "21"
                                        ? "图文咨询"
                                        : "电话咨询"
                                }|提交订单`,
                                orgId: this.data.unicode,
                                orgName: this.data.hospName,
                            })
                            .then(() => {
                                if (
                                    window.localStorage.getItem("localId") &&
                                    this.data.busiType == "21"
                                ) {
                                    sessionStorage.removeItem(
                                        "submit_plaintext"
                                    );
                                    sessionStorage.removeItem(
                                        "submit_msg_data"
                                    );
                                    // 图文咨询和有localid的情况下，给收银台传localid进行收银台埋点
                                    window.location.replace(
                                        `${
                                            res.cashierUrl
                                        }&localid=${window.localStorage.getItem(
                                            "localId"
                                        )}`
                                    );
                                } else {
                                    // window.location.href = res.cashierUrl;
                                    window.location.replace(res.cashierUrl);
                                }
                            });
                    } else {
                        let message =
                            response.respDesc ||
                            "网络连接超时，请稍后再试~";
                        util.showToast(message);
                    }
                } else {
                    if (res) {
                        if (res.orderStatus == "0") {
                            // 有待支付订单
                            let orderNo = res.orderNo;
                            let busiType = res.busiType;
                            let consultUrl =
                                location.origin +
                                location.pathname +
                                `#/myConsult?orderNo=${orderNo}`;
                            if (busiType == "1") {
                                // 复诊详情
                                // https://www.hfi-health.com:28181/iHospFollowup/#/furDetail?orderNo=18bd720acd22d39d
                                consultUrl = `${location.origin}/${process.env.VUE_APP_FOLLOW}/#/furDetail?orderNo=${orderNo}`;
                            }
                            this.$dialogBox({
                                title: "",
                                content: response.respDesc,
                                confirmTxt: "去查看",
                                cancelTxt: "取消",
                                cancelCallback: function () {},
                                confirmCallback: function () {
                                    tools.jumpUrl(consultUrl, "2");
                                },
                            });
                        } else if (
                            res.orderStatus == "1" ||
                            res.orderStatus == "2"
                        ) {
                            // 有会话订单
                            this.$dialogBox({
                                title: "",
                                content: response.respDesc,
                                confirmTxt: "确定",
                                cancelTxt: "取消",
                                cancelCallback: function () {},
                                confirmCallback: function () {
                                    let uInfo =
                                        JSON.parse(
                                            window.localStorage.getItem(
                                                "userInfo"
                                            )
                                        ) || "";
                                    let token = uInfo.token;
                                    let imUserId = res.imUserId;
                                    let orderNo = res.orderNo;
                                    let originStr = location.origin;
                                    let talkUrl = `${originStr}/${process.env.VUE_APP_TALK}/#/chat-detail?id=${imUserId}&userType=2&token=${token}&orderNo=${orderNo}`; //id 用户的imid
                                    console.log("------", talkUrl);
                                    tools.jumpUrl(talkUrl, "2");
                                },
                            });
                        } else {
                            let message =
                                response.respDesc ||
                                "网络连接超时，请稍后再试~";
                            util.showToast(message);
                        }
                    } else {
                        let message =
                            response.respDesc ||
                            "网络连接超时，请稍后再试~";
                        util.showToast(message);
                    }
                }
            });
        },
        confirmOrder() {
            let logTraceId = new Date().getTime();
            //   let alipayUserId = this.$store.state.alipayUserId
            //     ? this.$store.state.alipayUserId
            //     : window.localStorage.getItem("alipayUserId");
            let alipayUserId;
            if (window.localStorage.getItem("alipayUserId")) {
                alipayUserId = window.localStorage.getItem("alipayUserId");
            } else {
                // alert(
                //   "获取用户信息异常：" +
                //     window.localStorage.getItem("alipayUserId") +
                //     "&" +
                //     this.$store.state.alipayUserId
                // );
                // return;
            }

            // alipayUserId = "2088702714255878";
            let remark2Obj = {};
            if (navigator.userAgent.indexOf("AliApp") > -1) {
                remark2Obj = {
                    openid: alipayUserId,
                };
            } else if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                remark2Obj = {
                    openid: localStorage.getItem("wxOpenid"),
                    appid: window.localStorage.getItem("wxAppid"),
                };
            }
            console.log("***", remark2Obj);

            let backurl =
                location.origin +
                location.pathname +
                "#/myConsult?orderNo=" +
                this.data.orderNo;

            let data = {
                orderNo: this.data.orderNo,
                merchantId: this.data.merchantId,
                unicode: this.unicode,
                // 总金额
                amount: this.data.totalAmount + "",
                logTraceID: logTraceId,
                callBackUrl: backurl,
                remark2: JSON.stringify(remark2Obj), //str
            };
            // 优惠券的传参
            if (this.data.discountAmount) {
                data.discountAmount = this.data.discountAmount;
                data.couponKeys = this.discountCouponCodesList;
                data.calAmount = this.data.calAmount;
            }
            /**
             * "discountAmount": 优惠金额,
             * calAmount 实付金额
             * "couponKeys": [
             * {
             * "activityId": 40,
             * "couponCode": "877b58f909914c0a869cfb4710da40bd"
             * }
             * ]
             * */
            console.log("确认订单传参----", data);
            confirmOrder(data).then((res) => {
                if (res) {
                    if (
                        window.localStorage.getItem("localId") &&
                        this.data.busiType == "21"
                    ) {
                        // 图文咨询和有localid的情况下，给收银台传localid进行收银台埋点
                        /* window.location.href = `${
                            res.cashierUrl
                        }&localid=${window.localStorage.getItem("localId")}`; */
                        window.location.replace(
                            `${
                                res.cashierUrl
                            }&localid=${window.localStorage.getItem("localId")}`
                        );
                    } else {
                        // window.location.href = res.cashierUrl;
                        window.location.replace(res.cashierUrl);
                    }
                }
            });
        },
        // 获取优惠券的列表
        getlist() {
            console.log("查找优惠券···");
            let data = {
                userId: window.localStorage.getItem("userId"),
                couponStatus: "1",
                pageNum: this.pageNum,
                pageSize: 10,
                unicode: this.data.unicode,
                deptId: this.data.deptId,
                staffId: this.data.staffId,
                totalAmount: this.data.totalAmount,
            };
            queryList(data).then((res) => {
                console.log("查找优惠券接口", res);
                // this.couponList = res.value.list;
                debugger;
                // return;
                debugger;
                if (res) {
                    debugger;
                    this.loading = false;
                    let List = res.list;
                    this.couponList = this.couponList.concat(List);
                    // 计算优惠券接口的优惠券列表出参，要在优惠券列表为选中状态
                    this.couponList.forEach((ele, index) => {
                        this.discountCouponCodesList.forEach((e, i) => {
                            console.log(ele.couponCode, ele.activityId);
                            console.log(e.couponCode, e.activityId);
                            console.log(index);
                            if (
                                e.couponCode == ele.couponCode &&
                                e.activityId == ele.activityId
                            ) {
                                ele.used = 1;
                                console.log("一样的", index);
                                this.beUsedCoupon = ele;
                            }
                        });
                    });
                    debugger;
                    if (this.pageNum >= res.pages) {
                        this.finished = true;
                    } else {
                        this.pageNum++;
                    }
                }
            });
        },
        // 优惠券弹窗显示
        getCoupon() {
            this.couponShow = true;
        },
        // 选择优惠券，单选点击 选中的used=1，未选中其余都是0
        beChanged(item) {
            console.log("传参", item);
            this.couponList.forEach((element) => {
                if (
                    element.couponCode == item.couponCode &&
                    element.activityId == item.activityId
                ) {
                    element.used = !element.used;
                } else {
                    element.used = 0;
                }
            });
            this.couponList = [...this.couponList];
            // 选中才可以赋值，
            if (item.used) {
                this.beUsedCoupon = item;
            } else {
                this.beUsedCoupon = "";
            }
        },
        // 计算优惠券金额
        discountMoney(couponKeys, showModal) {
            let data = {
                userId: localStorage.getItem("userId"),
                // 总金额
                amount: this.data.totalAmount,
                unicode: this.data.unicode,
                deptId: this.data.deptId,
                staffId: this.data.staffId,
            };
            /**
             * couponKeys
             * 1.刚进入确认订单页时，不需要传couponKeys，后端自行计算最优的优惠券金额
             * 2.点击查券列表后，需要将选择的优惠券code传给后端，需要传couponKeys参数，后端根据传参计算优惠金额
             */
            if (couponKeys) {
                data.couponKeys = couponKeys;
            }
            discount(data).then((res) => {
                console.log("计算优惠券", res);
                if (res) {
                    // discountAmount：优惠金额
                    // calAmount：应付金额
                    this.data.discountAmount = res.discountAmount;
                    this.youhui = res.discountAmount.toString();
                    this.discountCouponCodesList = res.couponKeys;
                    this.data.calAmount = res.calAmount;
                    if (showModal == "1") {
                        // 无优惠券的情况，直接隐藏弹窗
                        this.couponShow = false;
                    }
                }
            });
        },
        confirmCoupon() {
            console.log(
                "确认优惠券",
                this.beUsedCoupon,
                this.beUsedCoupon.couponCode
            );
            if (this.couponList.length == 0) {
                // 无优惠券的情况，直接隐藏弹窗
                this.couponShow = false;
                return;
            } else {
                // 隐藏弹窗，选中优惠券后调用计算优惠券接口
                // 选中的优惠券
                if (this.beUsedCoupon) {
                    let t = [
                        {
                            activityId: this.beUsedCoupon.activityId,
                            couponCode: this.beUsedCoupon.couponCode,
                        },
                    ];
                    this.discountMoney(t, "1");
                } else {
                    // 取消选中的优惠券
                    this.youhui = "";
                    this.data.discountAmount = "";
                    this.data.calAmount = "";
                    this.discountCouponCodesList = "";
                    this.couponShow = false;
                }
            }
        },
    },
    filters: {
        filtersPicTypeName(val) {
            let fel = optionConsultPicType.filter((ele) => {
                return ele.value == val;
            });
            return fel[0] ? fel[0].text : "";
        },
    },
};
</script>
<style lang="less" scoped>
.backImg {
    height: 232px;
    background: url("@/images/img/com_back.png");
    background-size: 100% auto;
}
.ModContainer {
    background-color: transparent;
    margin-top: -232px;
}
.card {
    background: #ffffff;
    border-radius: 8px;
    margin: 12px 15px;
    padding: 15px;
}
.hospIcon {
    width: 17px;
    height: 16px;
    margin-right: 10px;
}
.hospName {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
}
.briefInfo {
    display: flex;
    align-items: center;
    padding: 17px 15px;
    .headImg {
        width: 60px;
        height: 60px;
        border-radius: 30px;
        margin-right: 15px;
    }
    .doctorName {
        font-size: 21px;
        font-weight: 500;
        color: #333333;
    }
    span {
        font-size: 14px;
        color: #888888;
        display: inline-block;
        margin-right: 6px;
    }
}

.title {
    width: 70px;
    font-size: 14px;
    color: #666666;
    margin-right: 20px;
    margin-bottom: 16px;
    flex-shrink: 0;
}
.descTxt {
    font-size: 14px;
    color: #333333;
    margin-bottom: 15px;
    word-break: break-all;
}
.voiceInfo {
    width: 189px;
    box-sizing: border-box;
    height: 46px;
    padding: 0 20px;
    background: #ffffff;
    box-shadow: 2px 2px 10px 0px rgba(186, 186, 186, 0.2);
    border-radius: 23px;
    margin-bottom: 15px;
    div {
        font-size: 0;
        width: 76px;
        margin-right: 20px;
        flex-shrink: 0;
        img {
            width: 100%;
        }
    }
    .voiceTxt {
        flex-shrink: 0;
        font-size: 13px;
        font-weight: bold;
        color: #333333;
        width: 40px;
        text-align: center;
    }
}
.picList {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .pici {
        width: 52px;
        height: 52px;
        position: relative;
        border-radius: 4px;
        background: #01cda7;
        margin-right: 5px;
        margin-bottom: 5px;
        overflow: hidden;
    }
    img {
        width: 100%;
        height: 100%;
    }
    .cover {
        position: absolute;
        bottom: 0;
        height: 17px;
        line-height: 17px;
        width: 100%;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 0px 0px 4px 4px;
        color: #fff;
        font-size: 10px;
        text-align: center;
    }
}
.amount {
    font-size: 14px;
    color: #fe963a;
}
.confirm {
    font-size: 12px;
    color: #333333;
    .amt {
        font-size: 20px;
        font-weight: bold;
        color: #fa541c;
    }
    .btn {
        width: 128px;
        height: 40px;
        line-height: 40px;
        background: #01cda7;
        border-radius: 20px;
        font-size: 16px;
        color: #ffffff;
        text-align: center;
    }
}
.nobtom {
    margin-bottom: 0;
}
.flexsty {
    display: flex;
}
.fctn {
    align-items: center;
}
.flex-btwn-c {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.coupon_money {
    flex: 1;
    text-align: right;
    margin-right: 0.08rem;
    color: #fe963a;
    font-size: 0.14rem;
}
.coupon_img {
    width: 0.06rem;
    height: 0.09rem;
}
.couponCon {
    p {
        background: linear-gradient(to bottom, #e6f5f4 0%, #fff 100%);
        padding-top: 0.3rem;
        color: #333333;
        font-size: 0.16rem;
        font-weight: bold;
        padding-left: 0.15rem;
    }
    .btn- {
        padding-bottom: 0.3rem;
        padding-top: 0.12rem;
        div {
            width: 3.45rem;
            height: 0.4rem;
            border-radius: 0.2rem;
            background-color: #01cda7;
            margin: 0 auto;
            line-height: 0.4rem;
            color: #fff;
            font-size: 0.18rem;
            text-align: center;
        }
    }
}
.noCoupon {
    width: 100%;
    text-align: center;
    margin: 0 auto;
    height: 45vh;
    overflow-y: scroll;
    /deep/ .van-list__finished-text {
        line-height: 1;
    }
    img {
        width: 1.07rem;
        height: 0.8rem;
        margin-top: 1rem;
    }
    div {
        color: #777777;
        font-size: 0.14rem;
        margin-top: 0.2rem;
    }
}
</style>
