<template>
    <div class="main">
        <!-- <button v-show="loginShow" @click="login">授权登录</button> -->
        <!-- <div v-show="uInfo">{{ uInfo.name }}{{ uInfo.paperNum }}</div> -->
        <div v-show="loginShow">
            <img class="bgImg1" src="@/images/img/auth_bg1.png" alt="" />
            <img class="bgImg2" src="@/images/img/auth_bg2.png" alt="" />
            <div class="loginBtn" @click="login">
                <img src="@/images/img/auth_btn.png" alt="" />
            </div>
        </div>
    </div>
</template>

<script>
import common from "../util/common";
import tools from "../util/tools";
import { userAuthorize, SyCheckCertNum } from "@/api/api";

export default {
    name: "index",
    data() {
        return {
            loginShow: false,
            url: "",
            authStatus: "0",
            uInfo: "",
            //   ss: "https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hzsdsrmyy/index?module=NewHospitalIndex&organId=2000140&source=jinTou-zfb-hzsdsrmyy&uInfo=",
            clientId: "",
        };
    },
    computed: {
        isWjAliMini() {
            return this.$store.state.isWjAliMini;
        },
    },
    created() {},
    mounted() {
        console.log("auth created 222222", this.isWjAliMini);
        // authStatus
        // 0 游客
        // 1 登录：获取用户信息
        // 2 弱实名：获取用户信息和token
        // 3 强实名：获取用户信息和token
        let authStatus = decodeURIComponent(common.getUrlParam("authStatus"));
        let url = decodeURIComponent(common.getUrlParam("rUrl"));
        if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
            url = decodeURIComponent(url);
        }
        // 存在clientid市儿童，需要授权得到userid后，调用ahth接口跳转
        console.log("clienturl", common.getUrlParam_(url, "clientId"));
        this.clientId = common.getUrlParam_(url, "clientId");
        console.log("11111111", this.clientId);
        // this.loginShow = authStatus !== "0";
        if (authStatus === "0" || window.localStorage.getItem("userInfo")) {
            // 游客模式
            this.loginShow = false;
        } else {
            this.loginShow = true;
        }
        this.authStatus = authStatus;
        // alert(authStatus)
        this.url = url;
        if (common.getUrlParam_(url, "origin") == "aksu") {
            window.sessionStorage.setItem(
                "hlw_remoteChannel",
                "health_hospital_aksu_mini"
            );
        }
        // 注意：为了修复市三小程序直接跳转到机构主页,不出现悬浮导航  增加这个判断-begin
        if (url.indexOf("hosporg?unicode=123301004701166305") > -1) {
            window.localStorage.setItem("orgId", "47011663033010211A1001");
        }
        // -end
        // 提供市府医务室的二维码，改造，auth页面通过是否有rUrl=sfyws来判断是否要跳转市府医务室
        if (url == "sfyws") {
            this.jumpUrlsyyws();
            return;
        }

        console.log("auth页面url", url);
        this.login();
    },
    methods: {
        async login() {
            let item = {
                appType: "",
                jumpUrl: this.url,
                status: this.authStatus,
                clientId: this.clientId,
            };
            if (this.url.indexOf("alipays://platformapi") > -1) {
                // 直接跳转打开第三方小程序，例如浙里办
                item.appType = "3";
            }

            tools.jumpUrlManage(item, 1);
            // tools.jumpUrlManage(item);
        },
        async jumpUrlsyyws(status = "1") {
            // 市一医务室 需要实现免登，用户信息以query传递，小程序方在app.vue的onLaunch中接收
            if (status !== 0 && !this.$store.state.uInfo) {
                if (!(await this.$store.dispatch("getLogin"))) {
                    Toast({
                        message: "用户信息获取失败",
                        className: "LTip",
                    });
                    return;
                }
            }
            let userId = window.localStorage.getItem("userId");
            if (navigator.userAgent.indexOf("AliApp") > -1 && userId) {
                await SyCheckCertNum(userId).then((res) => {
                    // 是白名单 true，不是 false，布尔值
                    console.log("获取市一白名单", res);
                    console.log("获取市一白名单", res !== true, res === true);

                    if (res === true) {
                        let userinfo =
                            sessionStorage.getItem("encrUserMini") ||
                            localStorage.getItem("encrUserMini");
                        let queryData = "uInfo=" + userinfo + "&isSFVIP=true";

                        let item = {};
                        // 新市府医务室地址
                        item.jumpUrl = `alipays://platformapi/startapp?appId=2021002193686965&query=${encodeURIComponent(
                            queryData
                        )}&page=subpages%2FfuZhenPY%2FFuZhenPYList%3FisSFVIP%3D1`;

                        // syInfirmaryUrl 市一医务室，需要跳转到市一的小程序
                        // item.jumpUrl = url;
                        item.appType = "3";
                        item.miniId = "2021002193686965";
                        item.status = status;
                        tools.jumpUrlManage(item, 1);
                        return;
                    } else {
                        // 不在白名单内，不跳转
                        alert("对不起，您没有该权限！");
                    }
                });
            }
        },
    },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less" scoped>
.main {
    background-color: #d7f1ed;
}
.bgImg1 {
    display: block;
    width: 100%;
    height: 50vh;
}
.bgImg2 {
    display: block;
    width: 100%;
    height: 50vh;
}
.loginBtn {
    position: absolute;
    bottom: 0.225rem;
    left: 50%;
    transform: translateX(-50%);
    width: 3.2rem;
    img {
        width: 100%;
        background-size: 100% auto;
        display: block;
    }
}
</style>
