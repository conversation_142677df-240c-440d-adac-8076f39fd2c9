/*
 * @Author: Activity H5 Project
 * @Date: 2025-01-01
 * @LastEditTime: 2025-04-12
 * @Description: 杭韵e家活动页面状态管理
 */

import Vue from "vue";
import Vuex from "vuex";
import auth from '../utils/auth'

Vue.use(Vuex);

export default new Vuex.Store({
    state() {
        return {
            // 用户相关
            userInfo: null,
            isLoggedIn: false,
            token: '',
            
            // 活动相关
            bannerList: [],
            featuredList: [],
            categoryList: [],
            activityList: [],
            
            // 搜索相关
            searchHistory: [],
            hotSearchList: [],
            
            // 应用配置
            appConfig: {
                title: "杭韵e家",
                primaryColor: "#1989fa",
                theme: "default"
            },
            
            // 位置信息
            location: {
                latitude: "",
                longitude: "",
                address: ""
            }
        };
    },
    
    mutations: {
        // 用户相关
        SET_USER_INFO(state, userInfo) {
            state.userInfo = userInfo;
            state.isLoggedIn = !!userInfo;
        },
        
        CLEAR_USER_INFO(state) {
            state.userInfo = null;
            state.isLoggedIn = false;
        },
        
        // Token相关
        SET_TOKEN(state, token) {
            state.token = token;
            auth.setToken(token);
        },
        
        CLEAR_TOKEN(state) {
            state.token = '';
            auth.removeToken();
        },
        
        // 活动相关
        SET_BANNER_LIST(state, list) {
            state.bannerList = list;
        },
        
        SET_FEATURED_LIST(state, list) {
            state.featuredList = list;
        },
        
        SET_CATEGORY_LIST(state, list) {
            state.categoryList = list;
        },
        
        SET_ACTIVITY_LIST(state, list) {
            state.activityList = list;
        },
        
        ADD_ACTIVITY_LIST(state, list) {
            state.activityList.push(...list);
        },
        
        // 搜索相关
        SET_SEARCH_HISTORY(state, history) {
            state.searchHistory = history;
        },
        
        ADD_SEARCH_HISTORY(state, keyword) {
            const history = state.searchHistory.filter(item => item !== keyword);
            history.unshift(keyword);
            state.searchHistory = history.slice(0, 10);
        },
        
        CLEAR_SEARCH_HISTORY(state) {
            state.searchHistory = [];
        },
        
        SET_HOT_SEARCH_LIST(state, list) {
            state.hotSearchList = list;
        },
        
        // 位置相关
        SET_LOCATION(state, location) {
            state.location = { ...state.location, ...location };
        },
        
        // 应用配置
        SET_APP_CONFIG(state, config) {
            state.appConfig = { ...state.appConfig, ...config };
        }
    },
    
    actions: {
        // 用户相关
        async login({ commit }, loginData) {
            try {
                // TODO: 调用登录API
                // const response = await api.login(loginData);
                const userInfo = loginData; // 模拟登录成功返回的用户信息
                
                commit('SET_USER_INFO', userInfo);
                localStorage.setItem('userInfo', JSON.stringify(userInfo));
                
                // 保存用户ID
                if (userInfo.userId || userInfo.id) {
                    const userId = userInfo.userId || userInfo.id;
                    localStorage.setItem('userId', userId);
                }
                
                return userInfo;
            } catch (error) {
                throw error;
            }
        },
        
        logout({ commit }) {
            commit('CLEAR_USER_INFO');
            commit('CLEAR_TOKEN');
            localStorage.removeItem('userInfo');
            localStorage.removeItem('userId');
        },
        
        // 从本地存储恢复用户信息
        restoreUserInfo({ commit }) {
            const userInfo = localStorage.getItem('userInfo');
            if (userInfo) {
                commit('SET_USER_INFO', JSON.parse(userInfo));
            }
        },
        
        // 获取登录状态
        getLogin({ dispatch }) {
            // 检查是否已登录
            const token = localStorage.getItem('access_token') || sessionStorage.getItem('access_token');
            const userInfo = localStorage.getItem('userInfo');
            
            if (token && userInfo) {
                return Promise.resolve(true);
            } else {
                // 未登录，跳转到登录页面
                if (window.location.hash !== '#/login') {
                    window.location.href = '#/login';
                }
                return Promise.reject(new Error('未登录'));
            }
        },
        
        // 重置Token
        resetToken({ commit }) {
            commit('CLEAR_TOKEN');
            return Promise.resolve();
        },
        
        // 设置Token
        setToken({ commit }, token) {
            commit('SET_TOKEN', token);
            return Promise.resolve();
        },
        
        // 活动相关
        async fetchBannerList({ commit }) {
            try {
                // TODO: 调用API获取banner列表
                // const response = await api.getBannerList();
                const bannerList = []; // 模拟数据
                
                commit('SET_BANNER_LIST', bannerList);
                return bannerList;
            } catch (error) {
                throw error;
            }
        },
        
        async fetchFeaturedList({ commit }) {
            try {
                // TODO: 调用API获取精选活动列表
                const featuredList = [];
                
                commit('SET_FEATURED_LIST', featuredList);
                return featuredList;
            } catch (error) {
                throw error;
            }
        },
        
        async fetchCategoryList({ commit }) {
            try {
                // TODO: 调用API获取活动分类列表
                const categoryList = [];
                
                commit('SET_CATEGORY_LIST', categoryList);
                return categoryList;
            } catch (error) {
                throw error;
            }
        },
        
        async fetchActivityList({ commit }, params) {
            try {
                // TODO: 调用API获取活动列表
                const activityList = [];
                
                if (params.page === 1) {
                    commit('SET_ACTIVITY_LIST', activityList);
                } else {
                    commit('ADD_ACTIVITY_LIST', activityList);
                }
                
                return activityList;
            } catch (error) {
                throw error;
            }
        },
        
        // 搜索相关
        addSearchHistory({ commit, state }, keyword) {
            commit('ADD_SEARCH_HISTORY', keyword);
            localStorage.setItem('searchHistory', JSON.stringify(state.searchHistory));
        },
        
        clearSearchHistory({ commit }) {
            commit('CLEAR_SEARCH_HISTORY');
            localStorage.removeItem('searchHistory');
        },
        
        restoreSearchHistory({ commit }) {
            const history = localStorage.getItem('searchHistory');
            if (history) {
                commit('SET_SEARCH_HISTORY', JSON.parse(history));
            }
        },
        
        async fetchHotSearchList({ commit }) {
            try {
                // TODO: 调用API获取热门搜索
                const hotSearchList = [
                    '理财讲座',
                    '亲子研学', 
                    '健康体检',
                    '微信支付礼',
                    '金融必知必晓'
                ];
                
                commit('SET_HOT_SEARCH_LIST', hotSearchList);
                return hotSearchList;
            } catch (error) {
                throw error;
            }
        },
        
        // 位置相关
        updateLocation({ commit }, location) {
            commit('SET_LOCATION', location);
        },
        
        // 应用配置
        updateAppConfig({ commit }, config) {
            commit('SET_APP_CONFIG', config);
        }
    },
    
    getters: {
        // 用户相关
        isLoggedIn: state => state.isLoggedIn,
        userInfo: state => state.userInfo,
        token: state => state.token,
        
        // 活动相关
        bannerList: state => state.bannerList,
        featuredList: state => state.featuredList,
        categoryList: state => state.categoryList,
        activityList: state => state.activityList,
        
        // 搜索相关
        searchHistory: state => state.searchHistory,
        hotSearchList: state => state.hotSearchList,
        
        // 应用配置
        appConfig: state => state.appConfig,
        primaryColor: state => state.appConfig.primaryColor,
        
        // 位置信息
        location: state => state.location
    }
});
