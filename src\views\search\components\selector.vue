<template>
  <div>
    <!-- 一级-专业大类 -->
    <van-tree-select
      :items="largeClass"
      height="55vw"
      :main-active-index="largeClassIndex"
      :active-id="activeId"
      @click-nav="onLargeClassClickNav"
      @click-item="onClickItem"
    >
      <!-- 二级-专业小类 和 细分专业 -->
      <van-tree-select
        :items="smallClass"
        :main-active-index="smallClassIndex"
        :active-id="activeId"
        @click-nav="onSmallClassClickNav"
        @click-item="onClickItem"
        slot="content"
      />
    </van-tree-select>
  </div>
</template>

<script>
import { TreeSelect } from "vant";
export default {
  options: {
    // 指定所有_开头的数据字段为纯数据字段
    // 纯数据字段：data中既不会展示在界面上，也不会传递给其他组件的字段
    // 好处：提升页面性能
    pureDataPattern: /^_/,
    // 设置shared这样就可以修改子组件样式了
    styleIsolation: "shared",
  },
  data() {
    return {
      largeClass: [
        {
          // 导航名称
          text: "工学",
        },
        {
          text: "管理学",
        },
      ],

      // 二级小类
      smallClass: [
        {
          // 导航名称
          text: "计算机类",
          // 该导航下所有的可选项
          children: [
            {
              // 名称
              text: "软件工程",
              // id，作为匹配选中状态的标识
              id: 1,
            },
            {
              text: "网络工程",
              id: 2,
            },
          ],
        },
        {
          // 导航名称
          text: "自动化类",
          // 该导航下所有的可选项
          children: [
            {
              // 名称
              text: "自动化类",
              // id，作为匹配选中状态的标识
              id: 3,
            },
            {
              text: "邮政工程",
              id: 4,
            },
          ],
        },
      ],

      // 专业大类选中项的索引
      largeClassIndex: 0,
      // 专业小类选中项的索引
      smallClassIndex: 0,
      // 	右侧选中项的 id
      activeId: null,
    };
  },
  components: { [TreeSelect.name]: TreeSelect },
  methods: {
    onLargeClassClickNav({
      // 把接收到的参数结构赋值
      detail = {},
    }) {
      console.log(detail);
    },

    // 点击专业小类时，触发的函数
    onSmallClassClickNav({ detail = {} }) {
      console.log(detail);
    },

    // 点击专业名时，触发的函数
    onClickItem({ detail = {} }) {
      console.log(detail);
      const activeId = this.data.activeId === detail.id ? null : detail.id;
    },
  },
};
</script>

<style></style>
