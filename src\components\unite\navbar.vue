<!--
 * @Author: your name
 * @Date: 2024-12-05 10:20:13
 * @LastEditTime: 2025-05-29 15:07:57
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 一行5个 共2行，多余滚动
 * @FilePath: \h5-interhosp\src\components\unite\navbar.vue
-->
<template>
    <div class="con_top" v-show="isShow">
        <van-swipe class="my-swipe" :loop="false" indicator-color="#01CDA7">
            <van-swipe-item
                v-for="(newNavlists, Index) in customList"
                :key="Index"
            >
                <ul class="ul">
                    <li
                        v-for="(item, index) in newNavlists"
                        :key="index"
                        :data-exposure-id="elementId"
                        :data-exposure-content="item.elementContent"
                        :data-exposure-sn="index + 1"
                        @click="go(item, index)"
                    >
                        <img
                            class="icon"
                            v-lazy="item.iconUrl"
                            :key="item.iconUrl"
                            alt=""
                        />
                        <img
                            class="angleIconUrl"
                            :src="item.angleIconUrl"
                            :key="index"
                            alt=""
                        />
                        <p>{{ item.applicationName }}</p>
                    </li>
                </ul>
            </van-swipe-item>
        </van-swipe>
    </div>
</template>
    <script>
import tools from "@/util/tools.js";
import Vue from "vue";
import { Swipe, SwipeItem } from "vant";
Vue.use(Swipe).use(SwipeItem);

export default {
    props: {
        datalists: {
            type: Object,
        },
    },
    components: {
        "mt-swipe": Swipe,
        "mt-swipe-item": SwipeItem,
    },
    data() {
        return {
            navlists: null,
            isShow: false,
            elementId: "",
            customList: [[]],
        };
    },
    created() {},
    mounted() {
        console.log(this.datalists);
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("navbar1111111", temp);
        var lists = null;
        if (
            temp.stageTypeName === "导航栏" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.navlists = temp.childStageList;
            this.customList = [[]];
            for (const item of this.navlists) {
                if (this.customList[this.customList.length - 1].length < 10) {
                    this.customList[this.customList.length - 1].push(item);
                } else if (
                    this.customList[this.customList.length - 1].length === 10
                ) {
                    this.customList.push([item]);
                    console.log(item, "item");
                }
            }
            console.log(this.customList);
        }
    },
    methods: {
        go(item, index) {
            console.log("object点击");
            item.elementId = this.elementId;
            item.index = index;
            tools.jumpUrlManage(item);
        },
    },
};
</script>
    <style scoped>
.con_top {
    /* height: 1.76rem; */
    width: 3.45rem;
    background-color: #fff;
    margin: 0 auto;
    margin-bottom: 0.1rem;
    /* border-radius: 0 0 0.08rem 0.08rem; */
    border-radius: 0.08rem;
    margin-top: 0.1rem;
}
img[lazy="loading"] {
    display: block;
    width: 20%;
    line-height: 100%;
    margin: auto;
}
.ul {
    width: 3.31rem;
    margin: 0.2rem auto;
    display: flex;
    background-color: white;
    flex-wrap: wrap;
}
.ul li {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 20%;
}
a {
    width: 100%;
    height: 100%;
}
.icon {
    width: 0.38rem;
    height: 0.38rem;
}
li p {
    max-width: 0.6rem;
    font-size: 0.12rem;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    margin: 0.07rem 0 0.16rem 0;
    color: #333333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.ul li:nth-child(3),
.ul li:nth-child(6) {
    margin-right: 0;
}
.angleIconUrl {
    position: absolute;
    top: -0.08rem;
    left: 0.42rem;
    /* width: 100%; */
    height: 0.16rem;
}
.con_top >>> .mint-swipe-indicator.is-active {
    width: 0.1rem;
    height: 0.04rem;
    opacity: 1;
}
.con_top >>> .mint-swipe-indicator {
    opacity: 0.2;
    background: #01cda7;
    width: 0.06rem;
    height: 0.04rem;
    border-radius: 0.02rem;
}
.con_top >>> .mint-swipe-indicators {
    bottom: 0.06rem;
}

.con_top >>> .my-swipe .van-swipe__indicators {
    bottom: 0.1rem !important;
}
.con_top >>> .van-swipe__indicators {
    bottom: 0.15rem;
}
.con_top >>> .van-swipe__indicator {
    opacity: 0.2;
    background: #01cda7;
    width: 0.06rem;
    height: 0.04rem;
    border-radius: 0.02rem;
}
.con_top >>> .van-swipe__indicator.van-swipe__indicator--active {
    width: 0.1rem;
    height: 0.04rem;
    opacity: 1;
}
</style>