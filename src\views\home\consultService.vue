<template>
    <div>
        <div class="backImg"></div>
        <div class="ModContainer">
            <div class="card briefInfo">
                <img
                    class="headImg"
                    :src="
                        data.headerUrl
                            ? dencryptHeader(data.headerUrl)
                            : data.sexCode == '1'
                            ? require('@/images/search/man.png')
                            : require('@/images/search/woman.png')
                    "
                    alt=""
                />
                <div>
                    <p class="doctorName">{{ data.doctorName }}</p>
                    <span v-if="data.teamFlag != '1'">{{
                        data.levelName
                    }}</span>
                    <span>{{ data.deptName }}</span>
                </div>
            </div>
            <div class="card pd-b5">
                <div class="flex-btwn" @click="addPeople">
                    <p class="title mg-b20">选择就诊人</p>
                    <img class="addImg" src="@/images/img/zx_add.png" alt="" />
                </div>
                <div class="people">
                    <div
                        v-for="(item, index) in peopList"
                        :key="index"
                        :class="
                            item.userId == selectPeople.userId ? 'active' : ''
                        "
                        @click="handleSelectP(item)"
                    >
                        {{ item.decodeName }}
                    </div>
                </div>
            </div>
            <div class="phoneInfo" v-if="busiType == '22'">
                <van-cell-group inset :border="false">
                    <van-field
                        v-model="receivePhone"
                        type="tel"
                        label="接听电话"
                        placeholder="请输入接听电话"
                        maxlength="11"
                        input-align="right"
                        size="large"
                        required
                    />
                    <van-cell
                        :value="timeShow ? timeShow : '请选择'"
                        is-link
                        size="large"
                        @click="showTimeSel"
                    >
                        <template #title>
                            <p><span class="required">*</span>预约时间</p>
                        </template>
                    </van-cell>
                </van-cell-group>
            </div>
            <div class="card" style="padding-right: 0">
                <div class="flex-btwn">
                    <p class="title">
                        <span class="required">*</span><span>健康档案</span>
                        <img
                            src="@/images/img/cTips.png"
                            alt=""
                            id="hcard"
                            @click="showTips = !showTips"
                        />
                    </p>
                    <van-radio-group
                        v-model="patientDocVisible"
                        direction="horizontal"
                    >
                        <van-radio
                            name="1"
                            checked-color="#01CDA7"
                            icon-size="18px"
                        >
                            医生可见
                        </van-radio>
                        <van-radio
                            name="0"
                            checked-color="#01CDA7"
                            icon-size="18px"
                        >
                            医生不可见
                        </van-radio>
                    </van-radio-group>
                </div>
                <div class="cardTips" v-show="showTips">
                    授权医生查看健康档案即允许医生查看
                    您在杭州市级医院及社区卫生服务中心的就诊信息，包括个人基本信息、门诊
                    信息、住院信息、体检信息等。
                </div>
            </div>
            <div class="card disease-info">
                <p class="title mg-b10">
                    <span class="required">*</span>病症信息
                </p>
                <van-field
                    v-model="diseaseTxt"
                    rows="3"
                    autosize
                    type="textarea"
                    maxlength="90"
                    :placeholder="serviceSetting.diseaseDesc"
                    show-word-limit
                />
                <p v-show="$store.state.noINZLB" class="titsml">语音留言</p>
                <div v-show="$store.state.noINZLB">
                    <div v-if="!(voiceInfo && voiceInfo.url)">
                        <div class="voiceImg" @click="startRecord" />
                    </div>
                    <div v-else class="voiceInfo flexsty fctn">
                        <div @click="playVoice" v-if="!isPlaying">
                            <img
                                class="wave"
                                src="@/images/img/record.png"
                                alt=""
                            />
                        </div>
                        <div v-else>
                            <img
                                class="wave"
                                src="@/images/img/record.gif"
                                alt=""
                            />
                        </div>
                        <p class="voiceTxt">{{ voiceInfo.voiceTime }}</p>
                        <img
                            class="cancel"
                            @click="clearVoiceInfo"
                            src="@/images/img/popup_close.png"
                            alt=""
                        />
                    </div>
                </div>
                <p class="titsml">上传图片（最多8张）</p>
                <p class="prompt">
                    {{ serviceSetting.uploadPicDesc }}
                    <!-- 上传化验单、检查资料、处方单、患处照片或其他疾病描述相关资料（最多支持8张）
          <span>照片仅医生与自己可见 </span> -->
                </p>
                <van-uploader
                    v-model="fileList"
                    multiple
                    :max-count="8"
                    :before-read="beforeRead"
                    :before-delete="deleteImgFile"
                >
                    <img
                        class="uploadImg"
                        src="@/images/img/zx_uploadImg.png"
                        alt=""
                    />
                    <template #preview-cover="{ file }">
                        <div class="preview-cover van-ellipsis">
                            {{ file.name }}
                        </div>
                    </template>
                </van-uploader>
            </div>
            <div class="declare" v-if="serviceSetting.consultDesc">
                <img src="@/images/img/zx_notice.png" alt="" />
                <div class="declare-info">
                    {{ serviceSetting.consultDesc }}
                    <!-- <p>咨询说明：</p>
          <p>1、医生会在您提交咨询申请后的24小时内处理。</p>
          <p>
            2、医生处理了您的咨询申请后，您可在24小时内医生进行沟通，到达时间后咨询单会自动结束；
          </p>
          <p>3、医生的回复内容仅为建议，具体诊疗请前往医院进行。</p> -->
                </div>
            </div>
            <!-- 电话 显示通话时长 -->
            <div v-if="busiType == '22' && callTimeLimit" class="callTime">
                {{ `通话时长最长${callTimeLimit}分钟` }}
            </div>
            <!-- 图文、复诊 显示接诊时间 -->
            <!-- v-if="(busiType == '21' || busiType == '1') && callTimeLimit" -->
            <div
                v-if="(busiType == '21' || busiType == '1') && reception_time"
                class="callTime"
            >
                <span style="color: #333333">预计接诊时间：</span>
                {{ reception_time }}
            </div>
            <div class="protocol" v-if="serviceSetting.agreementFlag">
                <van-checkbox
                    v-model="checked"
                    checked-color="#01CDA7"
                    icon-size="18px"
                >
                    我已阅读并同意
                </van-checkbox>
                <span class="cont" @click="showProtocol = true">
                    {{ serviceSetting.agreementTitle }}
                </span>
            </div>
            <div class="confirm" @click="orderSubmit">提交订单</div>
        </div>
        <msg-box
            ref="picType"
            :visible.sync="picVisible"
            title="选择图片类型"
            confirmTxt="提交"
            cancelTxt="取消"
            @confirm="confirmPicType"
        >
            <template #content>
                <van-picker
                    :columns="picTypeColumns"
                    :default-index="picDefaultIndex"
                    @change="picTypeChange"
                />
            </template>
        </msg-box>
        <msg-box
            ref="dhTime"
            :visible.sync="dhTimeVisible"
            title="预约时间"
            confirmTxt="提交"
            cancelTxt="取消"
            @confirm="confirmDhTime"
        >
            <template #content>
                <p style="color: #fe963a; text-align: center">
                    患者可预约7天内的医生号源
                </p>
                <van-picker
                    :columns="dhList"
                    :default-index="timeDefaultIndex"
                    @change="dhTimeChange"
                />
            </template>
        </msg-box>
        <!-- <msg-box
      ref="noticeType"
      :visible.sync="noticeVisible"
      :title="noticeObj.popTitleName"
      confirmTxt="同意并继续"
      cancelTxt="不同意并退出"
      @cancel="handleAllowNotice(0)"
      @confirm="handleAllowNotice(1)"
    >
      <template #content>
        <div class="noticeSty">
          <p v-html="noticeObj.popContent"></p>
        </div>
      </template>
    </msg-box> -->
        <FurPopup
            :furTipShow.sync="noticeVisible"
            :title="noticeObj.popTitleName"
            :moduleData="noticeObj.popContent"
            @confirm-tip="handleAllowNotice(1)"
            @cancel-tip="handleAllowNotice(0)"
        ></FurPopup>
        <recorder-view
            @ref="recorderRef"
            :visible.sync="visibleRecorder"
            @refresh="getVoiceData"
        ></recorder-view>
        <van-overlay :show="showProtocol">
            <div class="protocolContainer">
                <div class="colse" @click="showProtocol = false">
                    <img src="@/images/img/popup_close.png" alt="" />
                </div>
                <div v-html="serviceSetting.agreementContent"></div>
            </div>
        </van-overlay>
    </div>
</template>
<script>
import MsgBox from "@/components/msgBox.vue";
import FurPopup from "@/components/FurPopup.vue";
import RecorderView from "./components/recorder.vue";
import Vue from "vue";
import {
    Field,
    Uploader,
    Picker,
    CellGroup,
    Cell,
    Checkbox,
    Overlay,
} from "vant";
Vue.use(Field)
    .use(Uploader)
    .use(Picker)
    .use(CellGroup)
    .use(Cell)
    .use(Checkbox)
    .use(Overlay);
import {
    orderSubmit,
    getLinkFamilyList,
    appQueryPatientNoticePop,
    appQueryOrgDescSetting,
    getAppointTime,
    getReceptionTime,
    consultOrderQuery,
} from "@/api/consult";
import {
    doctorByUnicodeDeptIdStaffId,
    getOssSign,
    ploadfilesOss,
} from "@/api/api";
import { optionConsultPicType } from "@/util/dict";
import util from "@/util/util";
import cryptoJS from "crypto-js";
import common from "@/util/common";
import tools from "@/util/tools";
import moment from "moment";
import Recorder from "js-audio-recorder";

export default {
    components: {
        MsgBox,
        RecorderView,
        FurPopup,
    },
    data() {
        return {
            picTypeColumns: optionConsultPicType,
            picTypeTmp: {},
            picDefaultIndex: 0,
            picVisible: false,
            noticeVisible: false,
            visibleRecorder: false,
            showProtocol: false,
            checked: false,
            showTips: false,
            data: {},
            peopList: [],
            selectPeople: {},
            patientDocVisible: "", // 患者档案可见 0-否 1-是
            receivePhone: "",
            dhList: [],
            dhTimeVisible: false,
            timeDefaultIndex: 0,
            timeDefaultIndexTmp: 0,
            timeShow: "",
            phoneConsultTime: "",
            diseaseTxt: "",
            voiceInfo: {},
            fileList: [],
            currentFile: "",
            declareInfo: "",
            uInfo: {},
            staffId: "",
            deptId: "",
            unicode: "",
            source: "miniAlipay", //miniAlipay-支付宝小程序 miniWechat-微信小程序zjzwfw-浙里办
            busiType: "21", //1-复诊 21-图文咨询 22-电话咨询 3-处方
            orderType: "2", //订单类型 1-医保订单 2-自费订单
            branchCode: "",
            merchantId: process.env.VUE_APP_MERCHANTID,
            fPathImg: "",
            illnessList: [],
            isPlaying: false,
            noticeObj: {
                popTitleName: "患者须知",
                popContent: "",
                frequency: "",
            },
            serviceSetting: {
                diseaseDesc:
                    "请输入病情描述，如发病时间、主要病症、治疗经过、目前状况等，最少输入2个字符",
                uploadPicDesc: "",
                consultDesc: "",
                agreementFlag: "",
                agreementTitle: "",
                agreementContent: "",
            },
            weekList: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
            callTimeLimit: "",
            // 医生接诊时间
            reception_time: "",
        };
    },

    created() {
        // 有带入latestOrderNo说明入口从再来一单进入
        if (this.$route.query.latestOrderNo) {
            consultOrderQuery({
                orderNo: this.$route.query.latestOrderNo,
            }).then((res) => {
                // 根据订单号查询提交订单时的信息
                console.log("99999", res);
                if (res) {
                    this.patientDocVisible = res.patientDocVisible;
                    this.diseaseTxt = res.descriptions;
                    for (const item of res.pictures) {
                        this.fileList.push({
                            ...item,
                            url: item.content,
                            typeName: this.picTypeColumns.filter(
                                (it) => it.value === item.type
                            )[0].text,
                            file: {
                                name: this.picTypeColumns.filter(
                                    (it) => it.value === item.type
                                )[0].text,
                            },
                        });
                    }
                    this.voiceInfo = {
                        ...res.voices,
                        url: res.voices.voice,
                        voiceTime: res.voices.time,
                    };
                }
            });
        }
        this.uInfo = JSON.parse(window.localStorage.getItem("userInfo")) || "";
        if (this.uInfo.paperNo || this.uInfo.paperNum) {
            this.uInfo.paperNo = this.encryptAES(
                this.uInfo.paperNo || this.uInfo.paperNum
            );
        } else {
            // 用户信息缺失

            util.openDialogAlert(
                "提示",
                "检测到用户信息缺失,请重新登录~",
                () => {
                    util.loginOut();
                },
                "好的"
            );
            return;
        }

        // if (!this.$cookies.get("notice")) {
        //   this.$cookies.set("notice", "consult", -1);
        //   this.noticeVisible = true;
        // }
        this.staffId = this.$route.query.staffId;
        this.deptId = this.$route.query.deptId;
        this.unicode = this.$route.query.unicode;

        let module = this.$route.query.module;
        if (module == "imageConsultApply") {
            this.busiType = "21";
        } else if (module == "mobileConsultApply") {
            this.busiType = "22";
        }
        if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
            this.source = "miniWechat";
        } else if (
            sessionStorage.getItem("hlw_remoteChannel") == "health_zheliban_H5"
        ) {
            this.source = "zjzwfw";
        }

        this.uInfo.paperType = this.uInfo.paperId;
        this.uInfo.tel = this.uInfo.phone;
        this.receivePhone = this.uInfo.tel;
        let uInfoName = this.uInfo.name;
        this.uInfo.decodeName = uInfoName;
        this.uInfo.name = this.encryptAES(uInfoName);

        this.selectPeople = this.uInfo;
        this.peopList = [this.uInfo];
        console.log(this.uInfo);
        this.picTypeTmp = this.picTypeColumns[this.picDefaultIndex];

        this.getLinkFamilyList();
        this.getOrgServiceSetting();
    },
    mounted() {},
    beforeRouteLeave(to, from, next) {
        if (to.name !== "consultServiceInfo") {
            this.$vnode.parent.componentInstance.cache = {};
            this.$vnode.parent.componentInstance.keys = [];
        }
        console.log("totoooooooooooooo", to);
        localStorage.removeItem("appQPNoticePop");

        if (this.$refs.recorderRef) {
            this.$refs.recorderRef.beforeDestroy();
        }
        if (window.recorder) {
            window.recorder.stop();
            window.recorder = null;
        }

        if (navigator.userAgent.indexOf("AliApp") > -1) {
            my.postMessage({
                action: "record",
                status: "stopRecord",
            });
        }
        next();
    },
    methods: {
        dencryptHeader: common.dencryptHeader,
        showTimeSel() {
            this.dhTimeVisible = true;
        },
        toProtocolPage() {
            this.$router.push({
                path: "/protocol",
            });
        },
        async getOrgServiceSetting() {
            let resd = await this.getDoctorData();
            if (resd) {
                if (this.busiType == "22") {
                    this.getAppointTime();
                }
                if (resd.unicode) {
                    let platform = "1";
                    if (
                        navigator.userAgent
                            .toLowerCase()
                            .indexOf("micromessenger") > -1
                    ) {
                        platform = "3";
                    } else if (
                        sessionStorage.getItem("hlw_remoteChannel") ==
                        "health_zheliban_H5"
                    ) {
                        platform = "4";
                    }
                    let data = {
                        busiType: this.busiType,
                        platform,
                        unicode: this.data.unicode,
                    };
                    this.appQueryPatientNoticePop(data);
                    this.appQueryOrgDescSetting(data);
                    // 接诊时间接口的入参
                    console.log("this", this);
                    let reception_data = {
                        busiType: this.busiType,
                        deptId: this.data.deptId,
                        doctorId: this.data.doctorId,
                        unicode: this.data.unicode,
                    };
                    this.openPeriodOfDayByDoctor(reception_data);
                }
            }
        },
        // 获取医生的接诊时间
        openPeriodOfDayByDoctor(reception_data) {
            getReceptionTime(reception_data).then((res) => {
                console.log("获取接诊时间", res);
                this.reception_time = res;
            });
        },
        appQueryPatientNoticePop(data) {
            appQueryPatientNoticePop(data).then((res) => {
                if (res && res.length) {
                    this.noticeObj = res[0];
                    // frequency，弹窗频率，1-每天只弹一次，2-每次进入弹出
                    let fre = this.noticeObj.frequency;
                    if (fre == 1) {
                        let cookieName = `notice${this.busiType}${this.data.unicode}${this.data.deptId}${this.data.doctorId}`;
                        console.log("&&&&&&%%%%%&&&&&$$$$$-----", cookieName);
                        if (!this.$cookies.get(cookieName)) {
                            this.noticeVisible = true;
                        }
                    } else {
                        if (!localStorage.getItem("appQPNoticePop")) {
                            this.noticeVisible = true;
                        }
                    }
                }
            });
        },
        appQueryOrgDescSetting(data) {
            appQueryOrgDescSetting(data).then((res) => {
                if (res && res.length) {
                    this.serviceSetting = res[0];
                }
            });
        },
        getAppointTime() {
            const data = {
                doctorId: this.data.doctorId,
                deptId: this.data.deptId,
                unicode: this.data.unicode,
            };
            getAppointTime(data).then((res) => {
                console.log("-----~~~~", res);
                if (res) {
                    this.callTimeLimit = res.callTimeLimit;
                    let nearTimeArr = res.appointTime ? res.appointTime : [];
                    let dhList = [];
                    nearTimeArr.forEach((item) => {
                        let dateStr = item.split(" ")[0];
                        let timeStr = item.split(" ")[1];
                        if (dateStr && timeStr) {
                            let wd = moment(dateStr).isoWeekday();
                            console.log("-----~~~~---", wd);
                            let lstr =
                                moment(dateStr).format("MM月DD日") +
                                `(${this.weekList[wd - 1]})` +
                                " " +
                                timeStr;
                            let vstr = item.replace(/~/i, "-");
                            let obj = {
                                text: lstr,
                                value: vstr,
                            };
                            dhList.push(obj);
                        }
                        this.dhList = dhList;
                    });
                }
            });
        },
        handleAllowNotice(type) {
            this.noticeVisible = false;
            if (type) {
                let cookieName = `notice${this.busiType}${this.data.unicode}${this.data.deptId}${this.data.doctorId}`;
                var t = new Date();
                t.setDate(t.getDate() + 1);
                t.setHours(0);
                t.setMinutes(0);
                t.setSeconds(0);
                console.log("~~~~```````~~~~", t);
                this.$cookies.set(cookieName, "consult", {
                    expires: t,
                });
            } else {
                window.history.go(-1);
            }
        },
        beforeRead(file) {
            console.log("-----", file);
            var fileList = [];
            if (!file.length) {
                fileList.push(file);
            } else {
                fileList = file;
            }
            for (const item of fileList) {
                if (!/^image\/.*$/i.test(item.type)) {
                    util.showToast("请选择图片");
                    return;
                }
            }
            this.picVisible = true;
            this.currentFile = fileList;
        },
        deleteImgFile(file, detail) {
            console.log("*******", file, detail);
            let index = detail.index;
            const that = this;
            this.$dialogBox({
                title: "确定删除这张照片吗？",
                content: "",
                confirmTxt: "确认",
                cancelTxt: "取消",
                cancelCallback: function () {},
                confirmCallback: function () {
                    that.fileList.splice(index, 1);
                    that.$forceUpdate();
                },
            });
        },
        picTypeChange(picker, value, index) {
            console.log("------", value, index);
            this.picDefaultIndex = index;
            this.picTypeTmp = value;
        },
        confirmPicType() {
            let pictypeTxt = this.picTypeTmp.text;
            let pictypeVal = this.picTypeTmp.value;
            // let file = this.currentFile;
            for (const file of this.currentFile) {
                let n_ = new Date().getTime() + file.name;
                let data = {
                    attachmentName: n_,
                };
                const that = this;
                getOssSign(data).then((r) => {
                    if (r) {
                        console.log(r);
                        var fromData = new FormData();
                        fromData.append("name", n_);
                        fromData.append("key", r.key);
                        fromData.append("OSSAccessKeyId", r.accessId);
                        fromData.append("policy", r.policy);
                        fromData.append("success_action_status", "200");
                        fromData.append("signature", r.signature);
                        fromData.append("file", file);
                        ploadfilesOss(fromData, r.host).then(() => {
                            let fPathImg =
                                "https://cdnweb11.96225.com/" + r.key;
                            console.log("ddddvoicePath", fPathImg);
                            that.fPathImg = fPathImg;
                            // that.fPathImg =
                            //   "https://cdnweb11.96225.com/inthis_test/20230822/9404ca044fcd41be89660b850ccb325a/1692695939208headbg.png";

                            let file = {
                                url: that.fPathImg,
                                type: pictypeVal,
                                typeName: pictypeTxt,
                                file: new File([], pictypeTxt, {}),
                            };
                            if (that.fileList.length === 8) {
                                util.showToast("上传图片（最多8张）");
                                return;
                            }
                            that.fileList.push(file);
                        });
                    }
                });
            }
        },
        dhTimeChange(picker, value, index) {
            console.log("------", value, index);
            this.timeDefaultIndexTmp = index;
        },
        confirmDhTime() {
            if (this.dhList && this.dhList.length) {
                let index = this.timeDefaultIndexTmp;
                console.log("---999---", index, this.dhList[index]);

                let timeTmp = this.dhList[index].value;
                this.timeShow = timeTmp.substring(5);
                this.timeDefaultIndex = this.timeDefaultIndexTmp;
                this.phoneConsultTime = timeTmp;
            }
        },
        addPeople() {
            // from=interhosp
            localStorage.setItem("appQPNoticePop", 1);
            window.location.href = location.origin + "/childManage/#/add";
        },
        handleSelectP(val) {
            this.selectPeople = val;
            this.receivePhone = val.tel;
        },
        startRecord() {
            console.log("sssssssss$$$¥¥¥¥¥$$$");
            let that = this;
            if (navigator.userAgent.indexOf("AliApp") > -1) {
                that.visibleRecorder = true;
            } else {
                // 非支付宝内
                Recorder.getPermission().then(
                    () => {
                        that.visibleRecorder = true;
                    },
                    (error) => {
                        console.log(`H5录音error${error}`);
                        that.$dialogBox({
                            title: "",
                            content: `如需使用上传语音功能，请同时开启以下麦克风权限\n1、请允许${location.hostname}使用你的麦克风\n2、请在手机设置中打开APP的麦克风权限`,
                            confirmTxt: "我知道了",
                            cancelTxt: "",
                            cancelCallback: function () {},
                            confirmCallback: function () {},
                        });
                    }
                );
            }
        },
        getVoiceData(val) {
            console.log("rrrrrrrccccccddddd", val);
            this.voiceInfo = val;
        },
        playVoice() {
            let audio = document.createElement("audio");
            // audio.src =
            //   "https://cdnweb11.96225.com/inthis_test/20230824/49148aabf8594722ad6b57cb0b3f1b0f/2389i23478934781692862446251.mp3";
            audio.src = this.voiceInfo.url;
            this.isPlaying = true;
            audio.play();
            let that = this;
            audio.addEventListener(
                "ended",
                function () {
                    console.log("播放结束");
                    that.isPlaying = false;
                },
                false
            );
            audio.addEventListener(
                "error",
                function (error) {
                    console.log(`播放错误${error}`);
                    that.isPlaying = false;
                },
                false
            );
        },
        clearVoiceInfo() {
            this.voiceInfo = {};
            this.isPlaying = false;
        },
        getDoctorData() {
            let data = {
                staffId: this.staffId,
                deptId: this.deptId,
                unicode: this.unicode,
            };
            return new Promise((resolve, reject) => {
                doctorByUnicodeDeptIdStaffId(data).then((res) => {
                    console.log("医生详细信息=====", res);
                    if (res) {
                        this.data = res;
                        resolve(res);
                    } else {
                        resolve("");
                    }
                });
            });
        },
        getLinkFamilyList() {
            let muserId = localStorage.getItem("userId") || this.uInfo.userId;
            let data = {
                mainUserId: muserId,
                // mainUserPaperNo: "", //主账号身份证号（AES加密，与mainUserId至少传一个）
                desensitize: false, //是否脱敏真/假默认脱敏
                encrypt: true, //是否加密真/假默认不加密
            };
            let that = this;
            getLinkFamilyList(data).then((res) => {
                console.log("getLinkFamilyList=====", res);
                let list = [];
                if (res) {
                    list = res;
                    list.forEach((item) => {
                        let decodeName = that.dencryptAES(item.name);
                        item.decodeName = decodeName;
                        console.log("2222222", decodeName);
                    });
                }
                this.peopList = this.peopList.concat(list);
            });
        },
        // 与后台交互的加密
        encryptAES(content) {
            let aesKey = "jfdjk670qEH5lm3b"; // 后台
            let sKey = cryptoJS.enc.Utf8.parse(aesKey);
            let sContent = cryptoJS.enc.Utf8.parse(content);
            let encrypted = cryptoJS.AES.encrypt(sContent, sKey, {
                mode: cryptoJS.mode.ECB,
                padding: cryptoJS.pad.Pkcs7,
            });
            // return encrypted.ciphertext.toString()
            return encrypted.toString();
        },
        // 后台交互解密
        dencryptAES(content) {
            let aesKey = "jfdjk670qEH5lm3b";
            var key = cryptoJS.enc.Utf8.parse(aesKey);
            // var datahex = cryptoJS.enc.Hex.parse(content);
            // var dataBase64 = cryptoJS.enc.Base64.stringify(datahex);
            var decrypted = cryptoJS.AES.decrypt(content, key, {
                mode: cryptoJS.mode.ECB,
                padding: cryptoJS.pad.Pkcs7,
            });
            // console.log(decrypted,'00',dataBase64)
            return decrypted.toString(cryptoJS.enc.Utf8);
        },

        orderSubmit() {
            if (!this.uInfo.paperNum) {
                // 用户信息缺失

                util.openDialogAlert(
                    "提示",
                    "检测到用户信息缺失,请重新登录~",
                    () => {
                        util.loginOut();
                    },
                    "好的"
                );

                return;
            }
            this.illnessList = [];
            if (!this.diseaseTxt || this.diseaseTxt.length < 2) {
                util.showToast("请填写病情描述信息至少2个字符");
                return false;
            }
            if (
                this.busiType == "22" &&
                !/^1[3-9]\d{9}$/.test(this.receivePhone)
            ) {
                util.showToast("请输入正确的手机号码");
                return false;
            }
            if (this.busiType == "22" && !this.phoneConsultTime) {
                util.showToast("请选择预约时间");
                return false;
            }
            if (this.patientDocVisible == "") {
                util.showToast("请选择医生是否可查看健康档案");
                return false;
            }
            if (this.serviceSetting.agreementFlag && !this.checked) {
                let agreementTitle = this.serviceSetting.agreementTitle
                    ? this.serviceSetting.agreementTitle
                    : "";
                util.showToast(`请阅读并勾选同意${agreementTitle}`);
                return false;
            }
            // 病情描述
            if (this.diseaseTxt) {
                let illDesc = {
                    type: "11", //11-文字
                    content: this.diseaseTxt,
                    typeOrder: "0",
                };
                this.illnessList.push(illDesc);
            }
            // 语音留言
            if (this.voiceInfo && this.voiceInfo.url) {
                let illVoice = {
                    type: "12", //12-语音
                    content: this.voiceInfo.url,
                    time: this.voiceInfo.voiceTime,
                    typeOrder: "0",
                };
                this.illnessList.push(illVoice);
            }
            if (this.fileList && this.fileList.length > 0) {
                this.fileList.forEach((item) => {
                    let picEl = {
                        type: item.type,
                        content: item.url,
                        typeOrder: "0",
                    };
                    this.illnessList.push(picEl);
                });
            }
            // this.illnessList = [
            //   {
            //     type: "1",
            //     content:
            //       "测试测试测测测测测测测测测测测测测测测测测试测试测测测测测测测测测",
            //     typeOrder: "0",
            //   },
            //   {
            //     type: "23",
            //     content:
            //       "https://cdnweb11.96225.com/inthis_test/20230822/9404ca044fcd41be89660b850ccb325a/1692695939208headbg.png",
            //     typeOrder: "0",
            //   },
            //   {
            //     type: "24",
            //     content:
            //       "https://cdnweb11.96225.com/inthis_test/20230822/9404ca044fcd41be89660b850ccb325a/1692695939208headbg.png",
            //     typeOrder: "0",
            //   },
            // ];
            let logTraceId = new Date().getTime();
            let alipayUserId;
            if (window.localStorage.getItem("alipayUserId")) {
                alipayUserId = window.localStorage.getItem("alipayUserId");
            } else {
                // alert(
                //   "获取用户信息异常：" +
                //     window.localStorage.getItem("alipayUserId") +
                //     "&" +
                //     this.$store.state.alipayUserId
                // );
                // return;
            }
            let remark2Obj = {};
            if (navigator.userAgent.indexOf("AliApp") > -1) {
                remark2Obj = {
                    openid: alipayUserId,
                };
            } else if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                remark2Obj = {
                    openid: localStorage.getItem("wxOpenid"),
                    appid: window.localStorage.getItem("wxAppid"),
                };
            }
            console.log("***", remark2Obj);
            // let backurl =
            //   location.origin + location.pathname + "#/myConsult?orderNo=";

            let data = {
                orderType: this.orderType,
                busiType: this.busiType,
                source: this.source,
                branchCode: this.branchCode,
                merchantId: this.merchantId,

                remark2: JSON.stringify(remark2Obj), //str
                // callBackUrl: backurl,

                userId: localStorage.getItem("userId") || this.uInfo.userId,
                name: this.uInfo.name,
                gender: this.uInfo.genderCode,
                phone: this.uInfo.phone,
                certtypeCode: this.uInfo.paperId,
                certNum: this.uInfo.paperNo,

                patientName: this.selectPeople.name,
                patientGender: this.selectPeople.gender,
                patientCerttypeCode: this.selectPeople.paperType,
                patientCertNum: this.selectPeople.paperNo,
                patientUserId:
                    this.selectPeople.userId || this.selectPeople.id || "",
                // teamFlag 团队标识 0-医生 1-团队';
                // isTeamOrder 1团队单，0个人单，不传这个字段默认0
                isTeamOrder: this.data.teamFlag,

                patientDocVisible: this.patientDocVisible,
                phoneConsultCalled: this.receivePhone,
                phoneConsultTime: this.phoneConsultTime,

                imUserId: this.data.imUserId,
                staffId: this.data.staffId,
                doctorName: this.data.doctorName,
                doctorCerttypeCode: "01",
                doctorCertNum: this.data.doctorId,
                doctorTitleName: this.data.levelName,
                doctorAvatorUrl: this.data.headerUrl,
                deptId: this.data.deptId,
                deptName: this.data.deptName,
                unicode: this.data.unicode,
                hospname: this.data.hospName,

                url: `${location.origin}/${process.env.VUE_APP_DOC}/#/myConsult?orderNo=`, // "https://www.hfi-health.com:28181/iDocPrepro/#/myConsult?orderNo=", //医生订单详情
                totalAmount:
                    this.busiType == "21"
                        ? this.data.graphicServiceFee
                        : this.data.phoneServiceFee,
                list: this.illnessList,
                orderStatus: "",
                logTraceID: logTraceId,
            };

            const that = this;
            if (this.patientDocVisible == "0") {
                this.$dialogBox({
                    title: "是否确定不可查看？",
                    content:
                        "未授权医生查看您的健康档案可能会影响医生对症状判断",
                    confirmTxt: "确定",
                    cancelTxt: "取消",
                    cancelCallback: function () {},
                    confirmCallback: function () {
                        // 缓存病人姓名 明文 病情描述descriptions，
                        let temp = {
                            patientName: that.selectPeople.decodeName,
                            descriptions: that.diseaseTxt,
                        };
                        sessionStorage.setItem(
                            "submit_plaintext",
                            JSON.stringify(temp)
                        );
                        // 缓存提交订单的入参值
                        sessionStorage.setItem(
                            "submit_msg_data",
                            JSON.stringify(data)
                        );
                        // 然后直接跳转到确认订单页
                        let orderInfo = {
                            hospName: that.data.hospName,
                            busiType: that.busiType,
                            unicode: that.data.unicode,
                        };
                        that.$router.push({
                            path: "/consultServiceInfo",
                            query: orderInfo,
                        });
                        // that.submit(data);
                    },
                });
            } else {
                console.log("33", that.selectPeople);

                // 缓存病人姓名 明文 病情描述descriptions，
                let temp = {
                    patientName: this.selectPeople.decodeName,
                    descriptions: this.diseaseTxt,
                };
                sessionStorage.setItem(
                    "submit_plaintext",
                    JSON.stringify(temp)
                );
                // 缓存提交订单的入参值
                sessionStorage.setItem("submit_msg_data", JSON.stringify(data));
                // 然后直接跳转到确认订单页
                let orderInfo = {
                    hospName: that.data.hospName,
                    busiType: that.busiType,
                    unicode: that.data.unicode,
                };
                that.$router.push({
                    path: "/consultServiceInfo",
                    query: orderInfo,
                });
                // that.submit(data);
            }
        },
        submit(data) {
            const that = this;
            orderSubmit(data).then((response) => {
                console.log("%%%%%#######%%%%%%####", response);
                let res = response.value;
                this.illnessList = [];
                if (response.success == 1) {
                    if (res) {
                        tools
                            .handleSetPoint({
                                trackingContent: `${this.data.doctorName}|${
                                    this.busiType == "21"
                                        ? "图文咨询"
                                        : "电话咨询"
                                }|提交订单`,
                                orgId: this.data.unicode,
                                orgName: this.data.hospName,
                            })
                            .then(() => {
                                // window.location.href = res.cashierUrl;
                                let orderInfo = {
                                    hospName: this.data.hospName,
                                    orderNo: res.orderNo,
                                    busiType: this.busiType,
                                    unicode: this.data.unicode,
                                };
                                this.$router.push({
                                    path: "/consultServiceInfo",
                                    query: orderInfo,
                                });
                            });
                    } else {
                        let message =
                            response.respDesc || "网络连接超时，请稍后再试~";
                        util.showToast(message);
                    }
                } else {
                    if (res) {
                        if (res.orderStatus == "0") {
                            // 有待支付订单
                            let orderNo = res.orderNo;
                            let busiType = res.busiType;
                            let consultUrl =
                                location.origin +
                                location.pathname +
                                `#/myConsult?orderNo=${orderNo}`;
                            if (busiType == "1") {
                                // 复诊详情
                                // https://www.hfi-health.com:28181/iHospFollowup/#/furDetail?orderNo=18bd720acd22d39d
                                consultUrl = `${location.origin}/${process.env.VUE_APP_FOLLOW}/#/furDetail?orderNo=${orderNo}`;
                            }
                            this.$dialogBox({
                                title: "",
                                content: response.respDesc,
                                confirmTxt: "去查看",
                                cancelTxt: "取消",
                                cancelCallback: function () {},
                                confirmCallback: function () {
                                    tools.jumpUrl(consultUrl, "2");
                                },
                            });
                        } else if (
                            res.orderStatus == "1" ||
                            res.orderStatus == "2"
                        ) {
                            // 有会话订单
                            this.$dialogBox({
                                title: "",
                                content: response.respDesc,
                                confirmTxt: "确定",
                                cancelTxt: "取消",
                                cancelCallback: function () {},
                                confirmCallback: function () {
                                    let uInfo =
                                        JSON.parse(
                                            window.localStorage.getItem(
                                                "userInfo"
                                            )
                                        ) || "";
                                    let token = uInfo.token;
                                    let imUserId = res.imUserId;
                                    let orderNo = res.orderNo;
                                    let originStr = location.origin;
                                    let talkUrl = `${originStr}/${process.env.VUE_APP_TALK}/#/chat-detail?id=${imUserId}&userType=2&token=${token}&orderNo=${orderNo}`; //id 用户的imid
                                    console.log("------", talkUrl);
                                    tools.jumpUrl(talkUrl, "2");
                                },
                            });
                        } else {
                            let message =
                                response.respDesc ||
                                "网络连接超时，请稍后再试~";
                            util.showToast(message);
                        }
                    } else {
                        let message =
                            response.respDesc || "网络连接超时，请稍后再试~";
                        util.showToast(message);
                    }
                }
            });
        },
    },
};
</script>
<style lang="less" scoped>
.backImg {
    height: 232px;
    background: url("@/images/img/com_back.png");
    background-size: 100% auto;
}
.ModContainer {
    background-color: transparent;
    margin-top: -232px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -webkit-touch-callout: none;
}
.card {
    background: #ffffff;
    border-radius: 8px;
    margin: 12px 15px;
    padding: 15px;
    font-size: 16px;
    color: #333333;
    position: relative;
    .title {
        font-size: 18px;
        font-weight: 500;
        color: #333333;
    }
}
.briefInfo {
    display: flex;
    align-items: center;
    padding: 17px 15px;
    .headImg {
        width: 60px;
        height: 60px;
        border-radius: 30px;
        margin-right: 15px;
    }
    .doctorName {
        font-size: 21px;
        font-weight: 500;
        color: #333333;
    }
    span {
        font-size: 14px;
        color: #888888;
        display: inline-block;
        margin-right: 6px;
    }
}
.people {
    display: flex;
    flex-wrap: wrap;
    div {
        // height: 30px;
        // line-height: 30px;
        background: #f6f6f6;
        border-radius: 4px;
        // width: calc(33.333% - 8px);
        padding: 0.06rem 0.1rem;
        box-sizing: border-box;
        margin-right: 10px;
        margin-bottom: 10px;
        font-size: 13px;
        color: #333333;
        text-align: left;
        flex-shrink: 0;
        max-width: 80%;
    }
    /*:nth-child(3n) {
        margin-right: 0;
    }*/
    .active {
        color: #3ebfa0;
        background: #ebfffa;
        border: 1px solid #3ebfa0;
    }
}
.phoneInfo {
    margin-bottom: 12px;
}

.disease-info {
    .titsml {
        font-size: 13px;
        color: #555555;
        margin-bottom: 12px;
        margin-top: 16px;
    }
    .voiceImg {
        width: 45px;
        height: 45px;
        background-image: url("@/images/img/zx_voice.png");
        background-size: 100% 100%;
    }
    .voiceInfo {
        width: 189px;
        height: 46px;
        background: #ffffff;
        box-shadow: 2px 2px 10px 0px rgba(186, 186, 186, 0.2);
        border-radius: 23px;
        margin-bottom: 15px;
        position: relative;
        div {
            font-size: 0;
            width: 76px;
            padding-left: 20px;
            margin-right: 20px;
            flex-shrink: 0;
            .wave {
                width: 100%;
            }
        }
        .voiceTxt {
            flex-shrink: 0;
            font-size: 13px;
            font-weight: bold;
            color: #333333;
            width: 40px;
            text-align: center;
        }
        .cancel {
            width: 8px;
            height: 8px;
            position: absolute;
            display: block;
            right: 12px;
            top: 12px;
        }
    }
    .prompt {
        font-size: 12px;
        color: #999999;
        margin-bottom: 10px;
        span {
            color: #fe963a;
        }
    }
    .uploadImg {
        width: 70px;
        height: 70px;
    }
}
.confirm {
    width: calc(100% - 35px);
    height: 40px;
    line-height: 40px;
    margin: 0 auto;
    background: #01cda7;
    border-radius: 20px;
    font-size: 18px;
    color: #ffffff;
    text-align: center;
    margin-bottom: 45px;
}
.disable {
    opacity: 0.5;
}
.declare {
    margin: 15px 15px 25px;
    display: flex;
    font-size: 14px;
    line-height: 1.5;
    color: #333333;
    &-info {
        white-space: break-spaces;
    }
    img {
        flex-shrink: 0;
        width: 16px;
        height: 16px;
        margin-right: 9px;
        margin-top: 3px;
    }
}
.callTime {
    padding: 0 15px;
    font-size: 14px;
    // color: #fe963a;
    text-align: center;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333333;
}
.protocol {
    margin: 15px;
    font-size: 16px;
    line-height: 1.5;
    color: #333333;
    display: flex;
    .cont {
        color: #01cda7;
        margin-left: 8px;
    }
}
.flex-btwn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .addImg {
        width: 17px;
        height: 17px;
        margin-top: 5px;
    }
}
.flexsty {
    display: flex;
}
.fctn {
    align-items: center;
}
.flex-btwn-c {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.mg-b20 {
    margin-bottom: 20px !important;
}
.mg-b10 {
    margin-bottom: 10px !important;
}
.pd-b5 {
    padding-bottom: 5px !important;
}
.noticeSty {
    padding: 0 24px;
    text-align: left;
    line-height: 1.8;
    max-height: 280px;
    overflow-y: scroll;
}
.preview-cover {
    position: absolute;
    bottom: 0;
    height: 22px;
    line-height: 22px;
    width: 100%;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 0px 0px 4px 4px;
    color: #fff;
    font-size: 12px;
    text-align: center;
}
::v-deep .van-uploader__preview-image {
    width: 70px;
    height: 70px;
    border-radius: 4px;
}
::v-deep .van-uploader__preview-delete {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 0 4px 0 4px;
}
.disease-info ::v-deep .van-cell {
    background: #f6f6f6;
    border-radius: 4px;
}
::v-deep .van-picker-column__item--selected {
    font-size: 20px;
    font-weight: 500;
    color: #333;
}
::v-deep .van-cell {
    color: #333;
    font-size: 16px;
    background: #fff;
    border-radius: 8px;
    .van-field__label {
        color: #333;
    }
    .van-cell__value {
        color: #999999;
    }
    .van-cell__right-icon {
        color: #999999;
    }
}
.protocolContainer {
    background: #fff;
    white-space: break-spaces;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;

    .colse {
        width: 16px;
        height: 16px;
        // margin: 30px auto;
        margin: 15px;
        img {
            width: 100%;
            height: 100%;
        }
    }
}
img {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.required {
    position: absolute;
    left: 8px;
    color: #ee0a24;
    font-size: 14px;
}
#hcard {
    width: 16px;
    height: 16px;
    margin-left: 3px;
}
.cardTips {
    width: 251px;
    background: url("../../images/img/cardTipsBg.png") no-repeat 0 0;
    background-size: 100% 100%;
    padding: 15px 15px 20px;
    font-size: 13px;
    font-family: PingFang SC;
    color: #ec8224;
    line-height: 18px;
    position: absolute;
    bottom: 44px;
    left: 12px;
    box-sizing: border-box;
}
</style>
