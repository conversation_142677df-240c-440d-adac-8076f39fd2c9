<template>
  <div>
    <div class="backImg"></div>
    <div class="ModContainer">
      <div class="card flexsty fctn">
        <img class="hospIcon" src="@/images/img/zx_hospIcon.png" alt="" />
        <p class="hospName">{{ hospName }}</p>
      </div>
      <div class="card briefInfo">
        <img
          class="headImg"
          :src="
            data.photo
              ? data.photo
              : data.sexCode == '1'
              ? require('@/images/search/man.png')
              : require('@/images/search/woman.png')
          "
          alt=""
        />
        <div>
          <p class="doctorName">{{ data.doctorName }}</p>
          <span v-if="data.isTeamOrder != '1'">{{ data.doctorTitleName }}</span>
          <span>{{ data.deptName }}</span>
        </div>
      </div>
      <div class="card">
        <div class="flexsty">
          <p class="title">就诊人</p>
          <p class="descTxt">{{ data.patientName }}</p>
        </div>
        <div class="flexsty">
          <p class="title">健康档案</p>
          <p class="descTxt">
            {{ data.patientDocVisible == "1" ? "医生可见" : "医生不可见" }}
          </p>
        </div>
        <div v-if="data.busiType == '22'">
          <div class="flexsty">
            <p class="title">接听电话</p>
            <p class="descTxt">{{ data.phoneConsultCalled }}</p>
          </div>
          <div class="flexsty">
            <p class="title">预约时间</p>
            <p class="descTxt">{{ data.phoneConsultTime }}</p>
          </div>
        </div>
        <div class="flexsty">
          <p class="title">病情描述</p>
          <div>
            <p class="descTxt" v-if="data.descriptions">
              {{ data.descriptions }}
            </p>
            <div class="voiceInfo flexsty fctn" v-if="data.voices">
              <div class="waveform" @click="playVoice" v-if="!this.isPlaying">
                <img src="@/images/img/record.png" alt="" />
              </div>
              <div class="waveform" v-else>
                <img src="@/images/img/record.gif" alt="" />
              </div>
              <p class="voiceTxt">{{ data.voices.time }}</p>
            </div>
            <div
              class="picList"
              v-if="data.pictures && data.pictures.length > 0"
            >
              <div
                class="pici"
                v-for="(file, index) in data.pictures"
                :key="index"
                @click="imagePreview(file.content)"
              >
                <img :src="file.content" alt="" srcset="" />
                <div class="cover">
                  {{ file.type | filtersPicTypeName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-show="busiType == 21" class="card">
        <div @click="getCoupon" class="flex-btwn-c">
          <p class="title nobtom">优惠券</p>
          <p class="coupon_money">- ¥ {{ youhui }}</p>
          <img class="coupon_img" src="./../../images/coupon_right.png" />
        </div>
      </div>
      <div class="card">
        <div class="flex-btwn-c">
          <p class="title">支付类型</p>
          <p class="descTxt">{{ typeName }}</p>
        </div>
        <div class="flex-btwn-c">
          <p class="title">总金额</p>
          <p>
            <span class="descTxt">¥ {{ data.totalAmount }}</span>
          </p>
        </div>
        <div class="flex-btwn-c">
          <p class="title">优惠抵扣</p>
          <p>
            <span class="amount">- ¥ {{ data.totalAmount }}</span>
          </p>
        </div>
        <div class="flex-btwn-c">
          <p class="title nobtom">应付金额</p>
          <p>
            <span class="descTxt">¥ {{ data.totalAmount }}</span>
          </p>
        </div>
      </div>
      <div class="card flex-btwn-c confirm">
        <div>
          咨询服务费：<span class="amt">¥{{ data.totalAmount }}</span>
        </div>
        <div class="btn" @click="confirmOrder">确认订单</div>
      </div>
    </div>
    <van-popup v-model="couponShow" position="bottom" round>
      <div class="couponCon">
        <p>优惠券详情</p>
        <change-coupon
          :couponList.sync="couponList"
          @beChanged="beChanged"
        ></change-coupon>
        <div class="btn-">
          <div>确定</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { consultOrderQuery, confirmOrder } from "@/api/consult";
import { optionConsultPicType } from "@/util/dict";
import cryptoJS from "crypto-js";
import { ImagePreview } from "vant";
import common from "@/util/common";
import Vue from "vue";
import { Popup } from "vant";
import changeCoupon from "./components/changeCoupon.vue";

Vue.use(Popup);

export default {
  components: {
    changeCoupon,
  },
  data() {
    return {
      hospName: "",
      data: {
        doctorName: "",
        deptName: "",
        levelName: "",
        patientName: "",
        descriptions: "",
        voices: "",
        pictures: "",
        merchantId: "",
        totalAmount: "",
        orderNo: "",
        busiType: "",
      },
      //1-复诊 21-图文咨询 22-电话咨询 3-处方
      typeName: "",
      typeList: [
        {
          val: "1",
          label: "复诊",
        },
        {
          val: "21",
          label: "图文咨询",
        },
        {
          val: "22",
          label: "电话咨询",
        },
        {
          val: "3",
          label: "处方",
        },
      ],
      isPlaying: false,
      unicode: "",
      busiType: "",
      youhui: "10.00",
      couponShow: false,
      couponList: [
        {
          id: 1,
          type: 0,
          busiType: 21,
          platfrom: "全平台通用",
          time: "2024.06.25至2024.08.31",
          disable: 1,
        },
        {
          id: 2,
          type: 1,
          busiType: 21,
          platfrom: "仅在微信端-杭州健康通使用仅在微信端-康通使用",
          time: "2024.06.25至2024.08.31",
          disable: 1,
        },
        {
          id: 3,
          type: 2,
          busiType: 21,
          platfrom: "全平台通用",
          time: "2024.06.25至2024.08.31",
          disable: 0,
        },
        {
          id: 4,
          type: 3,
          busiType: 21,
          platfrom: "全平台通用",
          time: "2024.06.25至2024.08.31",
          disable: 0,
        },
      ],
    };
  },
  created() {
    this.data = this.$route.query;
    this.busiType = this.data.busiType;
    this.hospName = this.$route.query.hospName;
    this.unicode = this.$route.query.unicode;
    let filterI = this.typeList.filter((item) => {
      return item.val == this.data.busiType;
    });
    this.typeName = filterI[0] ? filterI[0].label : "";
    // this.getInfo();
  },
  mounted() {},
  methods: {
    dencryptHeader: common.dencryptHeader,

    getInfo() {
      let logTraceId = new Date().getTime();
      let params = {
        orderNo: this.data.orderNo,
        logTraceID: logTraceId,
      };
      const that = this;
      consultOrderQuery(params).then((res) => {
        console.log("99999", res);
        if (res) {
          that.data = res;
        }
      });
    },

    playVoice() {
      let audio = document.createElement("audio");
      audio.src = this.data.voices.voice;
      this.isPlaying = true;
      let that = this;
      audio.addEventListener(
        "ended",
        function () {
          console.log("播放结束");
          that.isPlaying = false;
        },
        false
      );
      audio.addEventListener(
        "error",
        function (error) {
          console.log(`播放错误${error}`);
          that.isPlaying = false;
        },
        false
      );
      audio.play();
    },
    imagePreview(url) {
      ImagePreview({
        images: [url],
        closeable: true,
        showIndex: false,
      });
    },
    confirmOrder() {
      let logTraceId = new Date().getTime();
      //   let alipayUserId = this.$store.state.alipayUserId
      //     ? this.$store.state.alipayUserId
      //     : window.localStorage.getItem("alipayUserId");
      let alipayUserId;
      if (window.localStorage.getItem("alipayUserId")) {
        alipayUserId = window.localStorage.getItem("alipayUserId");
      } else {
        alert(
          "获取用户信息异常：" +
            window.localStorage.getItem("alipayUserId") +
            "&" +
            this.$store.state.alipayUserId
        );
        return;
      }

      // alipayUserId = "2088702714255878";
      let remark2Obj = {};
      if (navigator.userAgent.indexOf("AliApp") > -1) {
        remark2Obj = {
          openid: alipayUserId,
        };
      } else if (
        navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
      ) {
        remark2Obj = {
          openid: localStorage.getItem("wxOpenid"),
          appid: window.localStorage.getItem("wxAppid"),
        };
      }
      console.log("***", remark2Obj);

      let backurl =
        location.origin +
        location.pathname +
        "#/myConsult?orderNo=" +
        this.data.orderNo;

      let data = {
        orderNo: this.data.orderNo,
        merchantId: this.data.merchantId,
        unicode: this.unicode,
        amount: this.data.totalAmount + "",
        logTraceID: logTraceId,
        callBackUrl: backurl,
        remark2: JSON.stringify(remark2Obj), //str
      };
      console.log("确认订单传参----", data);
      confirmOrder(data).then((res) => {
        if (res) {
          window.location.href = res.cashierUrl;
        }
      });
    },
    // 优惠券弹窗显示
    getCoupon() {
      this.couponShow = true;
    },
    beChanged(item) {
      console.log("传参", item);
    },
  },
  filters: {
    filtersPicTypeName(val) {
      let fel = optionConsultPicType.filter((ele) => {
        return ele.value == val;
      });
      return fel[0] ? fel[0].text : "";
    },
  },
};
</script>
<style lang="less" scoped>
.backImg {
  height: 232px;
  background: url("@/images/img/com_back.png");
  background-size: 100% auto;
}
.ModContainer {
  background-color: transparent;
  margin-top: -232px;
}
.card {
  background: #ffffff;
  border-radius: 8px;
  margin: 12px 15px;
  padding: 15px;
}
.hospIcon {
  width: 17px;
  height: 16px;
  margin-right: 10px;
}
.hospName {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
}
.briefInfo {
  display: flex;
  align-items: center;
  padding: 17px 15px;
  .headImg {
    width: 60px;
    height: 60px;
    border-radius: 30px;
    margin-right: 15px;
  }
  .doctorName {
    font-size: 21px;
    font-weight: 500;
    color: #333333;
  }
  span {
    font-size: 14px;
    color: #888888;
    display: inline-block;
    margin-right: 6px;
  }
}

.title {
  width: 62px;
  font-size: 14px;
  color: #666666;
  margin-right: 20px;
  margin-bottom: 16px;
  flex-shrink: 0;
}
.descTxt {
  font-size: 14px;
  color: #333333;
  margin-bottom: 15px;
  word-break: break-all;
}
.voiceInfo {
  width: 189px;
  box-sizing: border-box;
  height: 46px;
  padding: 0 20px;
  background: #ffffff;
  box-shadow: 2px 2px 10px 0px rgba(186, 186, 186, 0.2);
  border-radius: 23px;
  margin-bottom: 15px;
  div {
    font-size: 0;
    width: 76px;
    margin-right: 20px;
    flex-shrink: 0;
    img {
      width: 100%;
    }
  }
  .voiceTxt {
    flex-shrink: 0;
    font-size: 13px;
    font-weight: bold;
    color: #333333;
    width: 40px;
    text-align: center;
  }
}
.picList {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .pici {
    width: 52px;
    height: 52px;
    position: relative;
    border-radius: 4px;
    background: #01cda7;
    margin-right: 5px;
    margin-bottom: 5px;
    overflow: hidden;
  }
  img {
    width: 100%;
    height: 100%;
  }
  .cover {
    position: absolute;
    bottom: 0;
    height: 17px;
    line-height: 17px;
    width: 100%;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 0px 0px 4px 4px;
    color: #fff;
    font-size: 10px;
    text-align: center;
  }
}
.amount {
  font-size: 14px;
  color: #fe963a;
}
.confirm {
  font-size: 12px;
  color: #333333;
  .amt {
    font-size: 20px;
    font-weight: bold;
    color: #fa541c;
  }
  .btn {
    width: 128px;
    height: 40px;
    line-height: 40px;
    background: #01cda7;
    border-radius: 20px;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
  }
}
.nobtom {
  margin-bottom: 0;
}
.flexsty {
  display: flex;
}
.fctn {
  align-items: center;
}
.flex-btwn-c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.coupon_money {
  flex: 1;
  text-align: right;
  margin-right: 0.08rem;
  color: #fe963a;
  font-size: 0.14rem;
}
.coupon_img {
  width: 0.06rem;
  height: 0.09rem;
}
.couponCon {
  p {
    background: linear-gradient(to bottom, #e6f5f4 0%, #fff 100%);
    padding-top: 0.3rem;
    color: #333333;
    font-size: 0.16rem;
    font-weight: bold;
    padding-left: 0.15rem;
  }
  .btn- {
    padding-bottom: 0.3rem;
    padding-top: 0.12rem;
    div {
      width: 3.45rem;
      height: 0.4rem;
      border-radius: 0.2rem;
      background-color: #01cda7;
      margin: 0 auto;
      line-height: 0.4rem;
      color: #fff;
      font-size: 0.18rem;
      text-align: center;
    }
  }
}
</style>
