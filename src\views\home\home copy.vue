<!--
 * @Author: your name
 * @Date: 2024-12-05 09:42:05
 * @LastEditTime: 2024-12-05 09:42:05
 * @LastEditors: your name
 * @Description: 未接入三端统一的首页
 * @FilePath: \h5-interhosp\src\views\home\home copy.vue
-->
<template>
    <div style="height: 100%; overflow-y: scroll" @scroll="scrollCont">
        <div
            class="topFixed"
            v-show="tf && tHeight"
            :style="{ opacity: per, height: tHeight + 'px' }"
        >
            <p :style="{ height: statusBarHeight + 'px' }"></p>
            <p
                :style="{
                    height: titleBarHeight + 'px',
                    'line-height': titleBarHeight + 'px',
                }"
            >
                杭州市互联网医院
            </p>
        </div>
        <div class="cont">
            <div class="Mod-top wjBgImg" v-if="isWjAliMini || inWechat">
                <img class="Mod-top-img" v-if="topBg" :src="topBg" alt="" />
            </div>
            <div class="Mod-top bgImg" v-else>
                <img class="Mod-top-img" v-if="topBg" :src="topBg" alt="" />
            </div>
            <div
                :class="isWjAliMini ? 'ModContainer_wj' : 'ModContainer'"
                :style="{ 'margin-top': inWechat ? '-1.65rem' : '' }"
            >
                <div class="searchCont" @click="toSearch">
                    <img
                        class="searchCont-img"
                        src="@/images/img/search.png"
                        alt=""
                    />
                </div>
                <!-- 配置展台 -->
                <div class="Mod-common">
                    <div
                        class="Mod"
                        v-if="thStageList && thStageList.length > 0"
                    >
                        <div
                            class="Mod-item"
                            v-for="(item, index) in thStageList"
                            :key="index"
                            @click="jumpUrl(item)"
                        >
                            <img class="ModHead" v-lazy="item.iconUrl" alt="" />
                            <div>
                                <p>{{ item.applicationName }}</p>
                                <p class="sub">{{ item.applicationSubName }}</p>
                            </div>
                            <div
                                class="divider"
                                v-if="index != thStageList.length - 1"
                            ></div>
                        </div>
                    </div>
                    <div
                        class="Mod-stage"
                        v-if="stageList && stageList.length > 0"
                    >
                        <van-swipe
                            class="my-swipe"
                            :loop="false"
                            indicator-color="#01CDA7"
                        >
                            <van-swipe-item
                                v-for="(list, index) in stageList"
                                :key="index"
                            >
                                <div
                                    class="stageList"
                                    v-for="(item, index) in list"
                                    :key="index"
                                >
                                    <stage-item :data="item"></stage-item>
                                </div>
                            </van-swipe-item>
                        </van-swipe>
                    </div>
                </div>
                <div v-if="stageListConfig && stageListConfig.length">
                    <div
                        class="Mod-common"
                        v-for="(item, index) in stageListConfig"
                        :key="index"
                    >
                        <div v-if="item.name.indexOf('多行应用') > -1">
                            <div
                                class="Mod-stage pd-b0"
                                v-if="item.list && item.list.length > 0"
                            >
                                <div
                                    class="stageList"
                                    v-for="(itemin, index) in item.list"
                                    :key="index"
                                >
                                    <stage-item
                                        :data="itemin"
                                        :stageId="stageId"
                                        :childStageId="rollChildStageId"
                                        position="首页"
                                    ></stage-item>
                                </div>
                            </div>
                        </div>
                        <div v-if="item.name.indexOf('banner') > -1">
                            <van-swipe
                                :autoplay="3000"
                                :loop="true"
                                indicator-color="#fff"
                                v-if="item.list && item.list.length > 0"
                            >
                                <van-swipe-item
                                    v-for="(itemin, index) in item.list"
                                    :key="index"
                                >
                                    <img
                                        class="banner"
                                        :src="itemin.iconUrl"
                                        @click="jumpUrl(itemin)"
                                        alt=""
                                    />
                                </van-swipe-item>
                            </van-swipe>
                        </div>
                        <!-- 宣传展台区 -->
                        <div
                            v-if="
                                item.name.indexOf('宣传应用') > -1 &&
                                item.list &&
                                item.list.length >= 3
                            "
                            style="
                                display: flex;
                                flex-wrap: wrap;
                                align-items: flex-start;
                                justify-content: space-evenly;
                            "
                        >
                            <div
                                class="stageList"
                                style="
                                    width: 1.58rem;
                                    height: 1.28rem;
                                    margin-top: 0.1rem;
                                    margin-bottom: 0.1rem;
                                "
                            >
                                <img
                                    style="width: 100%"
                                    v-lazy="item.list[0].iconUrl"
                                    @click="jumpUrl(item.list[0])"
                                    alt=""
                                />
                            </div>
                            <div
                                class="stageList"
                                style="
                                    width: 1.58rem;
                                    height: 1.28rem;
                                    margin-top: 0.1rem;
                                    margin-bottom: 0.1rem;
                                    display: flex;
                                    flex-direction: column;
                                    justify-content: space-between;
                                "
                            >
                                <img
                                    style="width: 100%"
                                    v-lazy="item.list[1].iconUrl"
                                    @click="jumpUrl(item.list[1])"
                                    alt=""
                                />
                                <img
                                    style="width: 100%"
                                    v-lazy="item.list[2].iconUrl"
                                    @click="jumpUrl(item.list[2])"
                                    alt=""
                                />
                            </div>
                        </div>
                        <div
                            v-if="
                                item.name.indexOf('十字格') > -1 &&
                                item.list &&
                                item.list.length > 0
                            "
                            style="
                                display: flex;
                                flex-wrap: wrap;
                                align-items: flex-start;
                                justify-content: space-evenly;
                            "
                        >
                            <div
                                class="stageList"
                                v-for="(innerEle, index) in item.list"
                                :key="index"
                                style="
                                    width: 1.58rem;
                                    height: 0.64rem;
                                    margin-top: 0.1rem;
                                    margin-bottom: 0.1rem;
                                "
                            >
                                <img
                                    style="width: 100%"
                                    v-lazy="innerEle.iconUrl"
                                    @click="jumpUrl(innerEle)"
                                    alt=""
                                />
                            </div>
                        </div>

                        <!-- <div
            v-for="(item, index) in xcList"
            :key="index"
            class="stageList"
            style="width: 1.58rem; margin-top: 0.1rem"
          >
            <img
              v-if="index == 0"
              :src="item.iconUrl"
              @click="jumpUrl(item)"
              alt=""
            />
            <div v-if="index == 1">
              <img :src="item.iconUrl" @click="jumpUrl(xcList[1])" alt="" />
              <img :src="item.iconUrl" @click="jumpUrl(xcList[2])" alt="" />
            </div>
          </div> -->
                    </div>
                </div>
                <!-- <div>
          <scrollver></scrollver>
        </div> -->
                <div class="Mod-common">
                    <find></find>
                </div>
                <div class="Mod-hospList">
                    <div class="hospList-top">
                        <div class="hospList-title">
                            <p>互联网医院</p>
                            <p class="more" @click="goHospitalList">
                                更多
                                <img src="@/images/img/arrow_gray.png" alt="" />
                            </p>
                        </div>
                        <div class="search-input">
                            <div class="s-div" style="height: 0.55rem">
                                <input
                                    class="s-input"
                                    v-model="searchValue"
                                    type="search"
                                    placeholder="输入医院名称"
                                    autocomplete="off"
                                    @keyup.13="getHospListFun()"
                                />
                                <img src="@/images/search/sousuo.png" alt="" />
                                <img
                                    v-if="searchValue"
                                    @click="searchValue = ''"
                                    src="@/images/search/s-del.png"
                                    alt=""
                                />
                                <span @click="getHospListFun()">搜索</span>
                            </div>
                        </div>
                        <van-tabs @click="tabChange" v-model="activeName">
                            <van-tab
                                v-for="(item, index) in hospOptions"
                                :title="item.label"
                                :name="item.value"
                                :key="index"
                            >
                            </van-tab>
                        </van-tabs>
                    </div>
                    <div
                        :class="
                            isWjAliMini
                                ? 'hosp-item-container_wj'
                                : 'hosp-item-container'
                        "
                    >
                        <div v-if="hospList && hospList.length > 0">
                            <hospital-item
                                v-for="(item, index) in hospList"
                                :key="index"
                                :data="item"
                            ></hospital-item>
                            <div class="getMore" @click="goHospitalList">
                                查看更多医院
                                <!-- <img src="@/images/img/marrow.png" alt=""> -->
                                <van-icon
                                    name="arrow"
                                    :color="$store.state.primaryColor"
                                />
                            </div>
                        </div>
                        <van-empty
                            v-else
                            :image="emptyImg"
                            description="暂无搜索结果"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div v-if="isWjAliMini" class="bottomText">
            <p>金投健康科技 (杭州)有限公司技术支持</p>
            <p @click="callPhone('96225')">
                服务咨询热线：<span class="phone">96225</span>
            </p>
        </div>
        <div
            v-show="$store.state.loginShow && !isWjAliMini"
            class="fixed-login flex-between"
        >
            <p>欢迎进入互联网医院</p>
            <div class="login-btn" @click="getLogin">
                <span>一键登录</span>
                <van-icon name="arrow" color="#376AF5" />
            </div>
        </div>
        <pop v-if="showPop" :position="'首页'" :dialogInfo="dialogInfo"></pop>
    </div>
</template>

<script>
import Vue from "vue";
import common from "@/util/common";
import tools from "@/util/tools";
import { Icon, Swipe, SwipeItem, Tab, Tabs, Search, Empty } from "vant";
Vue.use(Icon)
    .use(Swipe)
    .use(SwipeItem)
    .use(Tab)
    .use(Tabs)
    .use(Search)
    .use(Empty);

import { getHospList, queryAllStagetData, queryBanner } from "../../api/api";

import HospitalItem from "../home/<USER>/hospitalItem.vue";
import StageItem from "../home/<USER>/stageItem.vue";
import Find from "./components/find.vue";
import Pop from "@/components/popUP.vue";
// import Scrollver from "./components/scrollver.vue";

export default {
    name: "Home",
    data() {
        return {
            tf: false,
            inWechat: tools.isWeChat(),
            per: 0,
            tHeight: 0,
            statusBarHeight: 0,
            titleBarHeight: 0,
            rollChildStageId: "",
            // xcList: [],
            hospList: [],
            // mutiRList: [],
            // 父展台
            stageId: "",
            // 子展台
            childStageId: "",
            operateStartTime: new Date().getTime(),
            hospList: [],
            // bannerList: [],
            stageList: [],
            thStageList: [],
            orgId: "",
            showPop: false,
            dialogInfo: {},
            floatAdPositionName: "健康互联网医院",
            topBg: "",
            emptyImg: require("@/images/search/no-data.png"),
            hospOptions: [
                {
                    label: "全部",
                    value: "",
                },
                {
                    label: "市属公立",
                    value: "3",
                },
                {
                    label: "区县",
                    value: "6",
                },
                {
                    label: "社区",
                    value: "5",
                },
            ],
            searchValue: "",
            activeName: "",
            baseStyle: {
                backgroundImage: 'url("@/images/img/topBS.jpeg")',
            },
            baseStyle_wj: {
                backgroundImage: 'url("@/images/img/wjTopSs.jpg")',
            },
            stageListConfig: [],
        };
    },
    components: {
        StageItem,
        HospitalItem,
        Find,
        Pop,
        // Scrollver,
    },
    computed: {
        isWjAliMini() {
            return this.$store.state.isWjAliMini;
        },
    },
    created() {
        console.log("%%%%%%%", this.$store.state.isWjAliMini, this.isWjAliMini);
        if (this.$store.state.isWjAliMini) {
            this.floatAdPositionName = "卫健小程序首页";
        } else {
        }
    },
    mounted() {
        // debugger;
        this.orgId = this.$store.state.orgId;
        this.getHospListFun(this.$store.state.orgId || "");
        this.getList();
        this.getBannerData();
        if (this.$store.state.isWjAliMini) {
            let that = this;
            // getSysInfo
            my.postMessage({
                action: "getSysInfo",
            });

            my.onMessage = function (e) {
                console.log("messagedddddd", e);
                if (e && e.sysInfo) {
                    let s = e.sysInfo;
                    that.statusBarHeight = s.statusBarHeight;
                    that.titleBarHeight = s.titleBarHeight;
                    that.tHeight = s.statusBarHeight + s.titleBarHeight;
                }
            };

            // window.addEventListener("scroll", this.scrollCont);
        }
    },
    activated() {
        this.operateStartTime = new Date().getTime();
        window.sessionStorage.setItem(
            "operateStartTime",
            String(this.operateStartTime)
        );
    },
    deactivated() {
        tools.handleSetPoint({
            trackingContent: "首页",
            operateStartTime: this.operateStartTime,
        });
    },
    methods: {
        scrollCont(e) {
            // debugger;
            if (this.isWjAliMini) {
                // console.log("ddddd", e.target.scrollTop);
                if (e.target.scrollTop > 200) {
                    this.tf = true;
                    this.per = 1;
                } else if (e.target.scrollTop > 50) {
                    this.tf = true;
                    this.per = e.target.scrollTop / 200;
                } else {
                    this.tf = false;
                    this.per = 0;
                }
            }
        },
        getBannerData() {
            let channel = 6;
            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                channel =
                    sessionStorage.getItem("hlw_remoteChannel") == "xhmhMini"
                        ? "10"
                        : "5";
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_zheliban_H5"
            ) {
                channel = 3;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5"
            ) {
                channel = 8;
            }
            let data = {
                channel,
                appType: "7",
                floatAdPositionName: this.floatAdPositionName,
            };
            queryBanner(data).then((response) => {
                console.log(response);
                if (response && response.length > 0) {
                    this.dialogInfo = response[0];
                    this.showPop = true;
                }
            });
        },
        getList() {
            const that = this;
            this.$store
                .dispatch("queryAllStage", this.floatAdPositionName)
                .then(() => {
                    var homepage = {};
                    var allStageList = this.$store.state.allStageList;
                    console.log("----", this.$store.state.allStageList);
                    if (!(allStageList && allStageList.length > 0)) {
                        return;
                    }
                    this.$store.state.allStageList.forEach((item, index) => {
                        if (item.stageName === this.floatAdPositionName) {
                            homepage = item;
                            this.stageId = item.stageId;
                        }
                    });
                    if (
                        !(
                            homepage &&
                            homepage.stageList &&
                            homepage.stageList.length > 0
                        )
                    ) {
                        return;
                    }
                    let stageListConfigTmp = [];
                    homepage.stageList.forEach((item, index) => {
                        let tmpStageObj = {
                            name: item.childStageName,
                        };
                        if (item.childStageName.indexOf("三联展台") > -1) {
                            that.thStageList = item.childStageList;
                        } else if (
                            item.childStageName.indexOf("滚动展台") > -1
                        ) {
                            that.rollChildStageId = item.childStageId; // 滚动子展台id用于埋点
                            var cList = item.childStageList;
                            if (cList && cList.length) {
                                var tmpStageList = [[]];
                                for (const item of cList) {
                                    if (
                                        tmpStageList[tmpStageList.length - 1]
                                            .length < 10
                                    ) {
                                        tmpStageList[
                                            tmpStageList.length - 1
                                        ].push(item);
                                    } else if (
                                        tmpStageList[tmpStageList.length - 1]
                                            .length === 10
                                    ) {
                                        tmpStageList.push([item]);
                                    }
                                }
                                that.stageList = tmpStageList;
                            }
                        } else if (
                            item.childStageName.indexOf("首页顶图") > -1
                        ) {
                            let topList = item.childStageList;
                            if (topList && topList.length > 0) {
                                this.topBg = topList[0].iconUrl;
                                console.log("===&&&&&&====", this.topBg);
                            }
                        } else {
                            tmpStageObj["list"] = item.childStageList;
                        }

                        stageListConfigTmp.push(tmpStageObj);
                    });
                    that.stageListConfig = stageListConfigTmp.filter((item) => {
                        if (item.list) return item;
                    });
                    console.log(
                        "%%%------%%%%---%%%---%%---stageListConfig--stageListConfigTmp",
                        that.stageListConfig,
                        stageListConfigTmp
                    );
                });
        },
        goHospitalList() {
            if (this.isWjAliMini) {
                // 卫健小程序
                let miniId = "2021002138635948";
                let url =
                    location.origin + location.pathname + "#/hospitalList";
                let jumpUrl = `/pages/index/index?returnURL=${url}`;
                my.postMessage({
                    action: "gotoMini",
                    appId: miniId,
                    url: jumpUrl,
                    authStatus: "0",
                });
            } else {
                this.$router.push({ name: "hospitalList" });
            }
        },
        toSearch() {
            if (this.isWjAliMini) {
                // 跳转到另一个小程序另一个页面
                my.navigateTo({
                    url:
                        "/pages/wjIndex/wjIndex?secUrl=" +
                        encodeURIComponent(
                            window.location.origin +
                                window.location.pathname +
                                "#/search?origin=wjAliMini"
                        ), // url详解请见【路由使用须知】
                });
                /* this.$router.push({
                    path: "/search",
                    query: { origin: "wjAliMini" },
                }); */
            } else {
                this.$router.push({ name: "search" });
            }
        },
        getLogin() {
            this.$store.dispatch("getLogin");
        },
        getDistance(lat1, lng1) {
            let lat2 = this.$store.state.curPosition.latitude;
            let lng2 = this.$store.state.curPosition.longitude;
            if (lat1 && lng1 && lat2 && lng2) {
                var radLat1 = (lat1 * Math.PI) / 180.0;
                var radLat2 = (lat2 * Math.PI) / 180.0;
                var a = radLat1 - radLat2;
                var b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
                var s =
                    2 *
                    Math.asin(
                        Math.sqrt(
                            Math.pow(Math.sin(a / 2), 2) +
                                Math.cos(radLat1) *
                                    Math.cos(radLat2) *
                                    Math.pow(Math.sin(b / 2), 2)
                        )
                    );
                s = s * 6378.137; // EARTH_RADIUS;
                s = Math.round(s * 10000) / 10000;
                return s.toFixed(1);
            } else {
                return "";
            }
        },
        async oprHosList(list) {
            /*
                await this.$store.dispatch("getLocation");
                let distaceList = [];
                let noDisList = [];
                for (let i = 0; i < list.length; i++) {
                    let ele = list[i];
                    ele.distance = this.getDistance(ele.latitude, ele.longitude);
                    if(ele.distance){
                        distaceList.push(ele)
                    }else{
                        noDisList.push(ele)
                    }
                }
                console.log(list)
                this.hospList = distaceList.sort(this.compare("distance")).concat(noDisList).splice(0, 8);
            */

            let otherHosp = [];
            let shHosp = [];
            list.forEach((item) => {
                if (item.hospTypeCode == "3") {
                    shHosp.push(item);
                } else {
                    otherHosp.push(item);
                }
            });
            let shHospSli = shHosp.concat(otherHosp).splice(0, 10);

            await this.$store.dispatch("getLocation");
            shHospSli.forEach((ele) => {
                ele.distance = this.getDistance(ele.latitude, ele.longitude);
            });
            this.hospList = shHospSli;

            if (this.orgId && list.length > 0) {
                // if(!this.$store.uInfo){
                //     await this.$store.dispatch("getLogin");
                // }
                for (let index = 0; index < list.length; index++) {
                    const item = list[index];
                    if (item.hospId == this.orgId) {
                        // todo 调用跳转方法
                        tools.gotoOrg(item);
                        return;
                    }
                    console.log("dddddddddddddddddddddddd");
                }

                /* 跳转到自建医院的机构主页 */
                /* window.location.replace(
                    "#/hosporg?orgId=" +
                        this.orgId +
                        "&internetId=" +
                        this.hospList[0].internetId +
                        "&hospName=" +
                        this.hospList[0].hospName
                ); */

                /* this.$router.push({
                        path: "hosporg",
                        replace: true,
                        query: {
                            orgId: this.orgId,
                            internetId: this.hospList[0].internetId,
                            hospName: this.hospList[0].hospName,
                        },
                    }); */
            }
        },
        compare(property) {
            return function (a, b) {
                var value1 = a[property];
                var value2 = b[property];
                return value1 - value2;
            };
        },
        tabChange(name, title) {
            console.log(name, title);
            this.getHospListFun();
        },
        getHospListFun() {
            console.log(this.areaVal);
            console.log(this.resultL);
            // console.log("orgId", orgId);
            //   channel 支付宝：6  微信小程序：5
            let channel = "6";
            if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_hospital_aksu_mini"
            ) {
                // 阿克苏
                channel = 9;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                    "health_smk_H5" ||
                window.navigator.userAgent.indexOf("smkVersion") > -1
            ) {
                // 市民卡
                channel = 8;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_zheliban_H5"
            ) {
                channel = 3;
            } else if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                channel =
                    sessionStorage.getItem("hlw_remoteChannel") == "xhmhMini"
                        ? "10"
                        : "5";
            }
            let data = {
                channel: channel,
                hospTypeName: "",
                locationCode: "",
                hospLevel: "", //医院等级：三级甲等
                platform: "", //1 民营 2 公立  不传是所有
                hospTypeCode: this.activeName,
                hospName: this.searchValue,
                todayFlag: "",
                internetFlag: "1",
                hospId: "",
                pageNum: 1,
                pageSize: 10,
            };
            getHospList(data).then((r) => {
                if (r) {
                    this.oprHosList(r);
                }
            });
        },
        jumpUrl(item) {
            // 预约挂号、在线咨询、在线复诊埋点
            console.log("跳转参数", item);
            tools
                .handleSetPoint({
                    stageId: this.stageId,
                    childStageId: this.childStageId,
                    stageAppId: item.applicationId,
                    trackingContent: item.applicationName,
                })
                .then(() => {
                    tools.jumpUrlManage(item);
                });
            // var applications = ["预约挂号", "在线咨询", "在线复诊"];
            // if (applications.indexOf(item.applicationName) !== -1) {
            //     tools
            //         .handleSetPoint({
            //             stageId: this.stageId,
            //             childStageId: this.childStageId,
            //             stageAppId: item.applicationId,
            //             trackingContent: item.applicationName,
            //         })
            //         .then(() => {
            //             tools.jumpUrlManage(item);
            //         });
            // } else {
            //     tools.jumpUrlManage(item);
            // }
        },
        callPhone(phone) {
            window.location.href = `tel:${phone}`;
        },
    },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less" scoped>
.topFixed {
    position: fixed;
    width: 100%;
    // height: 90px;
    padding-left: 0.35rem;
    font-size: 18px;
    box-sizing: border-box;
    // line-height: 1.5rem;
    background-color: #fff;
    z-index: 9;
}
.Mod-top {
    width: 100%;
    min-height: 237px;
    // background-image: url("@/images/img/headbg2.png");
    background-size: 100% auto;
    background-repeat: no-repeat;
    /* display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column; */
}
.wjBgImg {
    background-image: url("@/images/img/wjTopSs.jpg");
}
.bgImg {
    background-image: url("@/images/img/topBS.jpg");
}
.Mod-top-img {
    width: 100%;
    background-size: 100% auto;
}

.ModContainer {
    margin-top: -129px;
    background-color: transparent;
    position: relative;
}

.ModContainer_wj {
    margin-top: -124px;
    background-color: transparent;
    position: relative;
}
.searchCont {
    min-height: 35px;
    margin: 0 16px 12px;
    /* background-image: url('@/images/img/search.png'); */
    /* background-size: 100% auto; */
}
.searchCont-img {
    width: 100%;
    background-size: 100% auto;
}
.Mod {
    background: #ffffff;
    margin: 0 16px;
    /* border-radius: 8px; */
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 20px 0 15px;
    border-bottom: 1px solid #f1f1f1;
}
.Mod-item {
    font-size: 0;
    text-align: center;
    position: relative;
    flex: 1;
    flex-shrink: 0;
}
/* .Mod :last-child::after {
  display: none;
}
.Mod-item::after {
  content: "";
  width: 0.5px;
  height: 38px;
  background: #f1f1f1;
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
} */
.divider {
    content: "";
    width: 1px;
    height: 38px;
    background: #f1f1f1;
    position: absolute;
    right: 1px;
    top: 50%;
    transform: translateY(-50%);
}

.flex {
    height: 40px;
    margin: 20px 0;
    padding: 0 16px;
    flex: 1;
}
.flex_ {
    margin: 0.2rem 0rem 0.2rem 0.15rem;
    /* padding-right: 0.1rem; */
    width: 1.46rem;
    height: 0.48rem;
    background-size: 100% 100%;
    background-repeat: no-repeat;
}
.yygh {
    background-image: url("@/images/img/yygh.png");
}
.bgcx {
    background-image: url("@/images/img/bgcx.png");
}

.ModHead {
    width: 55px;
    background-size: 100% auto;
    margin-bottom: 9px;
}

.border-right {
    border-right: 1px solid #ececec;
    /* margin-right: 0.1rem; */
}

.Mod p {
    font-size: 16px;
    font-family: "Source Han Sans SC";
    font-weight: 500;
    color: #333333;
    line-height: 1;
}

.Mod .sub {
    font-size: 12px;
    font-family: "Source Han Sans SC";
    font-weight: 400;
    color: #999999;
    margin-top: 6px;
    line-height: 1;
}

.Mod-stage {
    display: flex;
    // align-items: center;
    align-items: flex-start;
    flex-wrap: wrap;
    padding: 15px 10px 8px;
}
.pd-b0 {
    padding-bottom: 0;
}
.stageList {
    flex-shrink: 0;
    width: 20%;
    margin-bottom: 15px;
}

.Mod-common {
    margin: 12px 16px;
    background: #ffffff;
    border-radius: 8px;
}
.Mod-hospList {
    margin: 12px 16px;
    border-radius: 8px;
}
.hosp-item-container {
    /* height: calc(100vh - 506px);
        overflow: scroll; */
    margin-bottom: 76px;
    // margin-top: 8px;
    padding-top: 12px;
    background: #f5f5f5;
}
.hosp-item-container > div {
    background: #f5f5f5;
}
.hospList-top {
    background-color: #fff;
    border-radius: 8px;
}
.hosp-item-container_wj {
    margin-bottom: 30px;
    padding-top: 12px;
}

.hosp-item-container > div:first-child {
    // border-top: none !important;
}

.hospList-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 16px 0;
    margin-bottom: 5px;
}

.hospList-title p {
    font-size: 18px;
    font-family: "PingFang SC";
    font-weight: 500;
    color: #333333;
}

.more {
    font-size: 12px !important;
    font-family: "PingFangSC";
    font-weight: 400 !important;
    color: #999999 !important;
}

.more img {
    width: 4px;
    height: 8px;
    margin-left: 7px;
    object-fit: fill;
}

.fixed-login {
    height: 50px;
    background: rgba(20, 31, 53, 0.8);
    padding: 0 16px;
    z-index: 999;
    bottom: 101px;
    left: 0;
    right: 0;
    position: fixed;
}

.flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.fixed-login p {
    font-size: 16px;
    font-family: "PingFang SC";
    font-weight: 500;
    color: #ffffff;
}

.login-btn {
    width: 80px;
    height: 28px;
    line-height: 28px;
    background: #ffffff;
    border-radius: 14px;
    font-size: 12px;
    font-family: "PingFang SC";
    font-weight: 400;
    color: #376af5;
    text-align: center;
}

.getMore {
    height: 45px;
    line-height: 45px;
    // border-top: 1px solid #f1f1f1;
    text-align: center;
    color: #3ebfa0;
    /* color: var(--primary-color); */
    font-size: 12px;
}
.getMore img {
    width: 4px;
    height: 8px;
    margin-left: 7px;
    object-fit: fill;
}
.banner {
    width: 100%;
    display: block;
}
.my-swipe {
    width: 100%;
}
.my-swipe .van-swipe-item {
    display: flex;
    flex-wrap: wrap;
    /* justify-content: space-between; */
}
::v-deep .my-swipe .van-swipe__indicators {
    bottom: 0;
}
::v-deep .my-swipe .van-swipe__indicator {
    width: 10px;
    height: 4px;
    border-radius: 2px;
    background-color: #dbfff8;
    opacity: 1;
}
::v-deep .my-swipe .van-swipe__indicator--active {
    width: 20px;
    height: 4px;
    border-radius: 2px;
}
::v-deep .my-swipe .van-swipe__indicator:not(:last-child) {
    margin-right: 0;
}
::v-deep .van-empty__image {
    width: 107px;
    height: 120px;
    img {
        background-size: 100% auto;
    }
}
.bottomText {
    text-align: center;
    margin-bottom: 0.85rem;
    color: #999999;
    font-size: 0.12rem;
}
.search-input {
    width: 100%;
    height: 0.55rem;
    background: #ffffff;
    position: relative;
    .s-div {
        & > img {
            position: absolute;
            &:nth-of-type(1) {
                width: 0.13rem;
                height: 0.14rem;
                top: 0.2rem;
                left: 0.32rem;
            }
            &:nth-of-type(2) {
                width: 0.15rem;
                height: 0.15rem;
                top: 0.2rem;
                right: 0.7rem;
            }
        }
        span {
            font-size: 0.15rem;
            font-weight: 400;
            color: #3ebfa0;
            position: absolute;
            right: 0.16rem;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    .s-input {
        width: 2.7rem;
        box-sizing: border-box;
        position: absolute;
        height: 0.33rem;
        border-radius: 0.14rem;
        background-color: #f5f5f5;
        font-size: 0.12rem;
        color: #363636;
        padding-left: 0.34rem;
        padding-right: 0.34rem;
        margin-right: 0.16rem;
        margin-left: 0.16rem;
        top: 0.11rem;
        border: none;
    }
    // IOS下移除原生样式
    -webkit-appearance: none;
    // 自定义placeholder颜色和字号
    input::-webkit-input-placeholder {
        font-size: 0.13rem;
        font-family: PingFang SC;
        font-weight: 400;
        color: #999999;
    }
    // 不显示搜索标识，自行添加搜索放大镜
    input[type="search"] {
        -webkit-appearance: none;
    }
    [type="search"]::-webkit-search-decoration {
        display: none;
    }
    // 不显示清空按钮，自行添加input后面的x清空文本
    input::-webkit-search-cancel-button {
        display: none;
    }
}
::v-deep .van-tabs__line {
    width: 0.3rem;
    border-radius: 0;
    background-color: #3ebfa0;
}
::v-deep .van-tabs {
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    border-radius: 8px;
    overflow: hidden;
    z-index: 1;
}
.phone {
    display: inline-block;
    margin-top: 8px;
    color: #3ebfa0;
}
</style>
