/*
 * @Author: shenpp
 * @Date: 2023-05-23
 * @LastEditTime: 2025-07-22 14:33:12
 * @Description:
 */
import fetch from "./http";
import util from "../util/util";

export const wxInit = (data) => {
    // 微信鉴权接口需要页面部署到生产环境才可以联调
    return fetch(
        "https://www.hfi-health.com:28181/wechat-java/callback/jsapi/ticket",
        // "/wechat-java/callback/jsapi/ticket",
        JSON.stringify({
            n: {
                // "url": window.location.href,
                url: window.location.href.split("#")[0],
                // location.href.split('#')[0]
            },
            c: {
                organid: "jtjk",
            },
        }),
        "post"
    ).then((result) => {
        console.log("wxInitwxInitwxInitwxInit", result);
        wx.config({
            debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印
            appId: result.n.appid, // 必填，公众号的唯一标识
            timestamp: result.n.timestamp, // 必填，生成签名的时间戳
            nonceStr: result.n.noncestr, // 必填，生成签名的随机串
            signature: result.n.signature, // 必填，签名
            jsApiList: [
                "hideMenuItems",
                // "getLocation",
                // "openLocation",
                "wx-open-launch-weapp",
            ], // 必填，需要使用的JS接口列表
            openTagList: ["wx-open-launch-weapp"], // 可选，需要使用的开放标签列表，例如['wx-open-launch-app']
        });
    });
};
/* export const getNali = () => {
    // 预约挂号获取token和userid
    // console.log("data", data);
    let uinfo = {
        userData: window.sessionStorage.getItem("encrUser") || window.localStorage.getItem("encrUser"),
    };
    return fetch(
        "/hzAppMS/Core/dataReEncryptForNgari",
        JSON.stringify(uinfo),
        "post"
    ).then((response) => {
        console.log("获取token", response);
        if (response.success == 1) {
            return response.value;
        } else {
            let message = response.respDesc || "网络连接超时，请稍后再试~";
            util.showToast(message);
            // return Promise.reject(message);
        }
    });
}; */
// 小程序免登接口
export const getToken = (data) => {
    // 预约挂号获取token和userid
    // console.log("data", data);
    debugger
    let uinfo = {
        /* userData: util.aesCode(
          "01$" + data.paperNum + "$" + data.name + "$" + data.phone,
          "jfdjk670qEH5lm3a"
        ), */

        userData: data || window.sessionStorage.getItem("encrUserMini") || window.localStorage.getItem("encrUserMini"),
        // userData:util.aesCode("01$321283198905026648$常青$13588491834",'jfdjk670qEH5lm3a')
    };
    return new Promise((r, j) => {
        fetch(
            "/hzAppMS/Core/registerAndLoginAES",
            JSON.stringify(uinfo),
            "post",
            {
                remoteChannel: "health_alipay_hospital_MINI",
            }
        ).then((response) => {
            console.log("获取token", response);
            debugger
            if (response.success == 1) {
                return r(response.value);
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
                util.loginOut()
                // return Promise.reject(message);
            }
        });
    })
};
// 小程序免登接口
export const getToken_jkt = (data, remoteChannel) => {
    debugger
    let uinfo = {
        userData: data || window.sessionStorage.getItem("encrUserMini") || window.localStorage.getItem("encrUserMini"),

    };
    return new Promise((r, j) => {
        fetch(
            "/hzAppMS/Core/registerAndLoginAES",
            JSON.stringify(uinfo),
            "post",
            {
                remoteChannel: remoteChannel || "health_wechat_hzjkt",
            }
        ).then((response) => {
            console.log("获取token", response);
            debugger
            if (response.success == 1) {
                return r(response.value);
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
                util.loginOut()
            }
        });
    })
};

// 浙里办单点，由加密串获取用户信息
export const zlbGetUser = (userData) => {
    return fetch(
        "/hzAppMS/Core/registerAndLoginAES",
        { type: "jkhz", userData },
        "post"
    ).then((response) => {
        console.log("浙里办获取token", response);
        if (response.success == 1) {
            return response.value;
        } else {
            let message = response.respDesc || "网络连接超时，请稍后再试~";
            util.showToast(message);
            // return Promise.reject(message);
        }
    });
};

// 获取字典
export const getDictionary = (data) => {
    return fetch(
        "/hzAppMS/Core/queryDictionaryByTypeId",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
// 获取医生列表;
export const getDoctorList = (data) => {
    return fetch(
        "/inthos/internetHospital/doctorPageList",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 获取市中医生列表;
export const getDoctorListForSz = (data) => {
    return fetch(
        "/inthos/internetHospital/doctorListByUnicode",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 获取医院列表
export const getHospList = (data) => {
    return fetch(
        "/inthos/internetHospital/getHospitalList",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

//获取医院详情
export const getHospInfo = (data) => {
    return fetch(
        "/inthos/internetHospital/hospitalInfoById",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

//展台数据（首页、我的）
export const queryAllStagetData = (data) => {
    return fetch("/stageBak/queryAllStagetData", JSON.stringify(data), "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

//首页弹窗 机构弹窗 机构banner，悬浮球
export const queryBanner = (data) => {
    return fetch(
        "/hzAppMS/stage/queryBanner",
        {
            region: "1",
            platform: "1",
            ...data,
        },
        "post",
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
// 公告通知
export const notice = (channel) => {
    return fetch(
        "/hzAppMS/Core/announceList",
        {
            channel,
            pageNum: 1,
            pageSize: 1
        },
        "post",
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

//获取医生详细信息数据
export const doctorByStaffIdAndDeptId = (data) => {
    return fetch(
        "/inthos/internetHospital/doctorByStaffIdAndDeptId",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
                return "";
            }
        })
        .catch((err) => {
            return "";
        });
};
//获取医生详细信息数据unicode
export const doctorByUnicodeDeptIdStaffId = (data) => {
    return fetch(
        "/inthos/internetHospital/doctorByUnicodeDeptIdStaffId",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
                return "";
            }
        })
        .catch((err) => {
            return "";
        });
};
//获取机构主页--特色科室
export const famousDepartmentPageList = (data) => {
    return fetch(
        "/inthos/internetHospital/famousDepartmentPageList",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
//获取机构主页--热门医生
export const famousDoctorPageList = (data) => {
    return fetch(
        "/inthos/internetHospital/famousDoctorPageList",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
//关注列表
export const favoritePageList = (data) => {
    return fetch(
        "/inthos/internetHospital/favoritePageList",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
//查询是否关注状态
export const favoriteByHospitalDoctor = (data) => {
    return fetch(
        "/inthos/internetHospital/favoriteByHospitalDoctor",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return true;
            } else {
                return false;
            }
        })
        .catch((err) => { });
};
//关注医生
export const favoriteDocotor = (data) => {
    return fetch(
        "/inthos/internetHospital/favorite",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
//取消关注
export const cancelFavorite = (data) => {
    return fetch(
        "/inthos/internetHospital/cancelFavorite",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 医生评价
export const doctorEvaluatePageList = (data) => {
    return fetch(
        "/inthos/internetHospital/doctorEvaluatePageList",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 医生主页推荐医生
export const recommendDoctorList = (data) => {
    return fetch(
        "/inthos/doctor/recommendDoctorList",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 根据医院查科室列表
export const departClassListByUnicode = (data) => {
    return fetch(
        "/inthos/internetHospital/departClassListByUnicode",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 建档查询
export const creatRecord = (data) => {
    // return fetch("https://www.hfi-health.com:28181/gateway/api/entry", JSON.stringify(data), "post", {
    return fetch(
        process.env.VUE_APP_RECORD_URL || "/gateway/api/entry",
        // "/gateway/api/entry",
        JSON.stringify(data),
        "post",
        {
            "Content-Type": "application/json",
            merchantId: "**********",
            interfaceMethod: "10088",
            logTraceId: data.logTraceId,
        }
    )
        .then((response) => {
            console.log("dddddd", response);
            debugger;
            if (response.success == 1) {
                return response.data.data;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

export const doctorScheduleByHospidAndDoctorId = (data) => {
    return fetch(
        "/inthos/internetHospital/doctorScheduleByHospidAndDoctorId",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// auth授权
// 获取字典
export const userAuthorize = (userId, clientId) => {
    return fetch(
        "/hzAppMS/Core/UserAuthorize",
        {
            reqSeq: new Date().getTime(),
            userId,
            clientId,
        },
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 各医院投放二维码 埋点接口
export const md_ = (channel, purpose) => {
    return fetch(
        "/hzAppMS/jumpStatistics/applySmk",
        {
            reqSeq: new Date().getTime(),
            channel,
            purpose,
        },
        "get"
    ).then((response) => { });
};
// 自建埋点
export const setPoint = (data) => {
    var url = process.env.VUE_APP_URL !== '' ? process.env.VUE_APP_URL + '/inthos/trackingPoint/internetHospitalTracking' : 'https://cloud.hfi-health.com:18180/track/trackingPoint/internetHospitalTracking'
    return fetch(
        url,
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};
// 查询市中医院详情

// 获取oss签名
export const getOssSign = (data) => {
    return fetch("/inthos/aliyun/fix722", data, "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

export const ploadfilesOss = (data, url) => {
    return fetch(url, data, "post")
        .then((response) => {
            console.log("ploadfilesOss", response);
            return true;
        })
        .catch((err) => { });
};

// 获取全部科室
export const getDepartmentTypeTree = (data) => {
    return fetch(
        "/hzAppMS/xywz/queryDepartmentTypeTree",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 获取市一医务室白名单
export const SyCheckCertNum = (userId) => {
    return fetch("/inthos/internetHospital/checkCertNum", { userId }, "get")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 查询医院机构主页 服务内容/internetHospital/hospitalAppListByUnicode
export const hospitalAppListByUnicode = (unicode, channel, applicationName) => {
    return fetch(
        "/inthos/internetHospital/hospitalAppListByUnicode",
        { unicode, channel, applicationName },
        "post"
    )
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 用户转化率埋点
export const sankeyCollect = (data) => {
    // "/hdcp/datacenter/sankeyCollect"
    return fetch("https://cloud.hfi-health.com:18180/track/sankey/sankeyCollect", data, "post", {
        applicationName: "hlwyy",
    })
        .then((response) => {
            if (response.code == 10000) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 西湖门户 由authToken换取用户信息
export const getJtToken = (data) => {
    return fetch(
        "/hzAppMS/Core/thirdAccess/getJtToken",
        data,
        "post"
    ).then((response) => {
        if (response.success == 1) {
            return response.value;
        } else {
            let message = response.respDesc || "网络连接超时，请稍后再试~";
            util.showToast(message);
        }
    });
};

// 营销平台埋点
export const manualPush = (data) => {
    return fetch(
        "/hdcp/manualPush/userClick",
        data,
        "get"
    ).then((response) => {
        if (response.success == 1) {
            return response.value;
        } else {
            let message = response.respDesc || "网络连接超时，请稍后再试~";
            util.showToast(message);
        }
    });
};

// 获取会话未读数
export const getUnreadMsgNum = data => {
    return fetch('/inthos/internetHospital/getUnreadMsgNum', data, "post").then(response => {
        if (response.success == 1) {
            return response.value;
        } else {
            let message = response.respDesc || "网络连接超时，请稍后再试~";
            util.showToast(message);
        }
    })
}
// 获取市民卡验证区的白名单/stageBak/isWhitelist
export const isWhitelist = data => {
    return fetch('/stageBak/isWhitelist', data, "post").then(response => {
        return response
        if (response.success == 1) {
            return response.value;
        } else {
            let message = response.respDesc || "网络连接超时，请稍后再试~";
            util.showToast(message);
        }
    })
}
// 杭州健康通微信小程序 注销接口 hzAppMS/Core/LogoutUser
export const LogoutUser = (userId) => {
    let UserId = userId || sessionStorage.getItem("userId") || localStorage.getItem("userId")
    return fetch("/hzAppMS/Core/LogoutUser", { UserId }, "get", {
        token: sessionStorage.getItem("token") || localStorage.getItem("token"),
        // 后端不支持传"remoteChannel":"health_wechat_hzjkt"
        remoteChannel: 'health_wechat_H5'
    })
        .then((response) => {
            if (response.success == 1) {
                return response;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};

// 机构主页，查询新版机构主页配置接口，包括顶图链接、是否新版、新版机构服务配置
export const queryInternetHome = (data) => {
    return fetch("/stageBak/queryInternetHome", JSON.stringify(data), "post")
        .then((response) => {
            if (response.success == 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};


// 获取服务包--健康商城
export const getfwbList = (data) => {
    return fetch(
        "/health_mall/api/v1/servicePack/list",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            if (response.resultCode == 200) {
                return response.data;
            } else {
                let message = response.message || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => { });
};