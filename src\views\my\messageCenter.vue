<template>
    <div class="mainCont">
        <div
            v-if="list && list.length"
            style="
                display: flex;
                justify-content: end;
                align-items: center;
                margin-right: 30px;
                padding-top: 12px;
            "
            @click="updateAllReadStatus"
        >
            <img
                style="width: 13px; height: 15px; margin-right: 5px"
                src="../../images/yjyd.png"
                alt=""
            />
            <p style="font-size: 15px; color: #3ebfa0">一键已读</p>
        </div>
        <div v-if="list && list.length">
            <div v-for="(item, index) in list" :key="index" class="item">
                <div class="flexbtwn">
                    <!-- readStatus 0 未读 1已读 -->
                    <!-- reminderType 0 服务提醒，1 系统提醒 -->
                    <div class="lfIcon">
                        <img
                            v-if="item.reminderType"
                            src="@/images/img/msg_sys.png"
                            alt=""
                        />
                        <img v-else src="@/images/img/msg_ser.png" alt="" />
                        <span v-if="item.readStatus == 0" class="reddot"></span>
                    </div>
                    <p>{{ item.reminderType ? "系统提醒" : "服务提醒" }}</p>
                </div>
                <div class="cont">
                    {{ item.messageContent }}
                </div>
                <p class="time">{{ item.pushTime }}</p>
                <div class="link" @click="toDetail(item)">
                    查看详情<van-icon name="arrow" color="#3ebfa0" size="12" />
                </div>
            </div>
        </div>
        <div v-else class="noList">
            <img :src="require('@/images/img/noConsultData.png')" alt="" />
            <div class="tip">暂无消息记录</div>
        </div>
    </div>
</template>

<script>
import {
    queryMessageList,
    updateReadStatus,
    updateAllReadStatus,
} from "@/api/message.js";
import tools from "@/util/tools.js";

export default {
    data() {
        return {
            imUserId: "123",
            type: "",
            list: [],
        };
    },
    created() {
        this.imUserId = localStorage.getItem("imUserId") || "";
        this.type = this.$route.query.type;
        this.getMsgList();
    },
    mounted() {},
    methods: {
        updateAllReadStatus() {
            updateAllReadStatus({
                identityType: "02",
                userId: this.imUserId,
                displayLocationType: this.type,
            }).then(() => {
                this.list = [];
                this.getMsgList();
            });
        },
        async toDetail(item) {
            let data = {
                messageId: item.messageId,
            };
            updateReadStatus(data).then((res) => {
                let url = item.pageUrl;
                window.location.href = url;
            });
        },
        getMsgList() {
            let data = {
                userId: this.imUserId,
                displayLocationType: this.type || 0,
            };
            queryMessageList(data).then((res) => {
                if (res) {
                    this.list = res;
                }
            });
        },
        toDetail(item) {
            let data = {
                messageId: item.messageId,
            };
            updateReadStatus(data).then((res) => {
                tools.jumpUrl(item.pageUrl, "2");
            });
        },
    },
};
</script>

<style lang="less" scoped>
.mainCont {
    background: #f6f6f6;
    padding: 0 0 12px;
    .item {
        background: #ffffff;
        border-radius: 8px;
        margin: 12px 15px;
        padding: 15px;
        font-size: 17px;
        color: #333333;

        .flexbtwn {
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e8e9ec;
            padding-bottom: 12px;
            .reddot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: #ff5a5a;
                border: 1px solid #ffffff;
            }
        }
        .lfIcon {
            width: 25px;
            height: 25px;
            margin-right: 15px;
            position: relative;
            img {
                width: 100%;
                height: 100%;
            }
            span {
                position: absolute;
                top: -1px;
                right: -2px;
            }
        }
        .cont {
            padding: 16px 0;
            font-size: 15px;
            color: #666666;
        }
        .time {
            font-size: 13px;
            color: #bdbdbd;
        }
        .link {
            font-size: 13px;
            border-bottom: none;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            color: #3ebfa0;
        }
    }
    .noList {
        margin-top: 100px;
        img {
            display: block;
            width: 1.07rem;
            height: 1.2rem;
            margin: auto;
        }

        .tip {
            font-size: 0.14rem;
            font-weight: 400;
            color: #777777;
            text-align: center;
            margin-top: 0.195rem;
        }
    }
}
</style>
