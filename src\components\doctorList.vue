<!--Author: 郭元扬
ProjectDescription: 医生列表
CreateTime: 2023-06-16
UpdateTime: 2023-06-16-->

<template>
    <div class="doctor-list" v-if="doctorList.length">
        <div
            class="doctor-info"
            :style="doctorStyle"
            v-for="(item, index) of doctorList"
            :key="index"
        >
            <div class="header" @click="goDoctorDetail(item)">
                <!-- @/images/search/doctor-header.png -->
                <img
                    :src="
                        item.headerUrl
                            ? dencryptHeader(item.headerUrl)
                            : item.sexCode == '1'
                            ? require('@/images/search/man.png')
                            : require('@/images/search/woman.png')
                    "
                    alt=""
                />
            </div>
            <div class="info">
                <div @click="goDoctorDetail(item)">
                    <div class="row1">
                        <div>
                            <p style="font-size: 0.15rem">
                                {{ item.doctorName }}
                            </p>
                            <p v-if="item.teamFlag != '1'">
                                {{ item.levelName }}
                            </p>
                            <p>{{ item.deptName }}</p>
                        </div>
                        <!-- 关注功能 -->
                        <div
                            @click.stop="handleFollow(item)"
                            v-if="'follow' in item"
                        >
                            <img
                                v-if="item.follow"
                                src="@/images/search/followed.png"
                                alt=""
                            />
                            <img
                                v-else
                                src="@/images/search/follow.png"
                                alt=""
                            />
                        </div>
                    </div>
                    <div class="row2">
                        <p class="special-p" v-if="item.hospTypeCode">
                            <span>{{
                                item.hospTypeCode | getHospitalType
                            }}</span>
                        </p>
                        <p class="normal-p">{{ item.hospName }}</p>
                    </div>
                    <div class="row3">
                        <p>{{ item.remark }}</p>
                    </div>
                    <div class="row4">
                        <!-- 搜索/在线咨询进入显示咨询量 -->
                        <!-- <p v-if="'consultQuantity' in item && !repeatVisit">
                咨询量<span>{{ item.consultQuantity || "无" }}</span>
              </p> -->
                        <!-- 在线复诊进入显示复诊量 -->
                        <!-- <p v-if="'returnVisitQuantity' in item && repeatVisit">
                复诊量<span>{{ item.returnVisitQuantity || "无" }}</span>
              </p> -->
                        <p>
                            接诊量<span>{{
                                item.serviceQuantity || "无"
                            }}</span>
                        </p>
                        <p v-if="'averageScore' in item">
                            评价<span>{{
                                item.averageScore
                                    ? item.averageScore + "分"
                                    : "无"
                            }}</span>
                        </p>
                    </div>
                </div>
                <!-- 在线咨询进入只展示咨询标签 -->
                <div
                    class="row5"
                    v-if="
                        ('graphicServiceFee' in item ||
                            'phoneServiceFee' in item) &&
                        onlineVisit
                    "
                >
                    <!-- @click="handleAsk('imageConsultApply', item)" -->
                    <div
                        class="text"
                        v-if="String(item.graphicServiceEnable) === '1'"
                        @click="
                            jumpUrl(
                                1,
                                String(item.graphicServiceEnable),
                                '图文咨询',
                                item
                            )
                        "
                    >
                        <img src="@/images/search/tuwen.png" alt="" />
                        图文
                        <span
                            >￥{{ item.graphicServiceFee
                            }}<span
                                class="limit"
                                v-if="item.graphicServiceQuestionLimit"
                                >/{{ item.graphicServiceQuestionLimit }}条</span
                            ></span
                        >
                    </div>
                    <!-- @click="handleAsk('mobileConsultApply', item)" -->
                    <div
                        class="phone"
                        v-if="
                            String(item.phoneServiceEnable) === '1' &&
                            String(item.teamFlag) !== '1'
                        "
                        @click="
                            jumpUrl(
                                2,
                                String(item.phoneServiceEnable),
                                '电话咨询',
                                item
                            )
                        "
                    >
                        <img src="@/images/search/dianhua.png" alt="" />
                        电话<span>￥{{ item.phoneServiceFee }}</span>
                    </div>
                    <div
                        v-if="
                            levelNameArr.indexOf(item.levelName) != -1 &&
                            item.teamFlag != '1'
                        "
                        @click.stop="gotoYYGH(item)"
                    >
                        <img src="@/images/search/guahao.png" alt="" />预约挂号
                    </div>
                </div>
                <!-- 在线复诊进入只展示复诊标签 -->
                <div
                    class="row5"
                    v-if="'phoneServiceFee' in item && repeatVisit"
                >
                    <!-- @click="handleAsk('askDoctorApply', item)" -->
                    <div
                        class="text"
                        v-if="String(item.returnVisitEnable) === '1'"
                        @click="
                            jumpUrl(
                                3,
                                String(item.returnVisitEnable),
                                '在线复诊',
                                item
                            )
                        "
                    >
                        <img src="@/images/search/fuzhen.png" alt="" />
                        复诊开方
                        <span
                            >￥{{ item.returnVisitFee
                            }}<span
                                class="limit"
                                v-if="item.returnVisitQuestionLimit"
                                >/{{ item.returnVisitQuestionLimit }}条</span
                            ></span
                        >
                    </div>
                    <div
                        v-if="
                            levelNameArr.indexOf(item.levelName) != -1 &&
                            item.teamFlag != '1'
                        "
                        @click.stop="gotoYYGH(item)"
                    >
                        <img src="@/images/search/guahao.png" alt="" />预约挂号
                    </div>
                </div>
                <!-- 首页或者机构主页进入展示所有标签 -->
                <div class="row5" v-if="!onlineVisit && !repeatVisit">
                    <div
                        class="text"
                        v-if="String(item.graphicServiceEnable) === '1'"
                        @click="
                            jumpUrl(
                                1,
                                String(item.graphicServiceEnable),
                                '图文咨询',
                                item
                            )
                        "
                    >
                        <img src="@/images/search/tuwen.png" alt="" />
                        图文
                        <span
                            >￥{{ item.graphicServiceFee
                            }}<span
                                class="limit"
                                v-if="item.graphicServiceQuestionLimit"
                                >/{{ item.graphicServiceQuestionLimit }}条</span
                            ></span
                        >
                    </div>
                    <!-- @click="handleAsk('mobileConsultApply', item)" -->
                    <div
                        class="phone"
                        v-if="
                            String(item.phoneServiceEnable) === '1' &&
                            String(item.teamFlag) !== '1'
                        "
                        @click="
                            jumpUrl(
                                2,
                                String(item.phoneServiceEnable),
                                '电话咨询',
                                item
                            )
                        "
                    >
                        <img src="@/images/search/dianhua.png" alt="" />
                        电话<span>￥{{ item.phoneServiceFee }}</span>
                    </div>
                    <div
                        class="text"
                        v-if="String(item.returnVisitEnable) === '1'"
                        @click="
                            jumpUrl(
                                3,
                                String(item.returnVisitEnable),
                                '在线复诊',
                                item
                            )
                        "
                    >
                        <img src="@/images/search/fuzhen.png" alt="" />
                        复诊开方
                        <span
                            >￥{{ item.returnVisitFee
                            }}<span
                                class="limit"
                                v-if="item.returnVisitQuestionLimit"
                                >/{{ item.returnVisitQuestionLimit }}条</span
                            ></span
                        >
                    </div>
                    <div
                        v-if="
                            levelNameArr.indexOf(item.levelName) != -1 &&
                            item.teamFlag != '1'
                        "
                        @click.stop="gotoYYGH(item)"
                    >
                        <img src="@/images/search/guahao.png" alt="" />预约挂号
                    </div>
                </div>
            </div>
            <!-- <div
          class="yygh"
          v-if="levelNameArr.indexOf(item.levelName) != -1"
          @click="gotoYYGH(item)"
        >
          <span>预约挂号</span>
        </div> -->
        </div>
    </div>
</template>
  
  <script>
import tools from "@/util/tools";
import util from "@/util/util";
import common from "@/util/common";
import { doctorScheduleByHospidAndDoctorId } from "@/api/api";
export default {
    filters: {
        getHospitalType(value) {
            let methods = "";
            switch (value) {
                case "3":
                    methods = "市属公立";
                    break;
                case "6":
                    methods = "区县";
                    break;
                case "5":
                    methods = "社区";
                    break;
                default:
                    methods = "其他";
                    break;
            }
            return methods;
        },
    },
    data() {
        return {
            origin: "",
            levelNameArr: [
                "主任医师",
                "副主任医师",
                "主治医师",
                "医师",
                "医士",
            ],
        };
    },
    props: {
        doctorList: {
            type: Array,
            default: () => [],
        },
        // 医生.doctor-info展示样式自定义
        doctorStyle: {
            type: Object,
            default: () => {},
        },
        // 是否为复诊开方
        repeatVisit: {
            type: Boolean,
            default: () => false,
        },
        // 是否为在线咨询
        onlineVisit: {
            type: Boolean,
            default: () => false,
        },
    },
    watch: {
        doctorList: {
            handler(newVal) {
                console.log("wtach=====", newVal);
                this.doctorList = newVal;
            },
        },
    },
    mounted() {
        this.origin = common.getUrlParam("origin");
    },
    methods: {
        dencryptHeader: common.dencryptHeader,
        handleFollow(item) {
            console.log(item.follow);
            this.$nextTick(() => {
                this.$set(item, "follow", !item.follow);
            });
        },

        // 点击预约挂号的跳转  需要判断是卫健还是自建平台   如果在卫健中，跳转到我们的小程序 my.postMessage
        //  如果是
        async gotoYYGH(item) {
            // docName 为医生的姓名
            let docName = item.doctorName;
            let url =
                "https://www.hfi-health.com:28181/appointWithDoc/#/search?origin=hlwywMini&cont=" +
                docName;
            let params = {
                hospId: item.hospId ? item.hospId : "",
                doctorId: item.doctorId,
            };
            let isjump = await doctorScheduleByHospidAndDoctorId(params);
            if (!isjump) {
                util.openDialogAlert("", "该医生暂无预约挂号排班");
                return;
            }
            if (this.$store.state.isWjAliMini) {
                // 卫健
                let jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                    url
                )}`;
                my.postMessage({
                    action: "gotoMini",
                    appId: "2021002138635948",
                    url: jumpUrl,
                    authStatus: "2",
                });
            } else {
                // 自建平台
                tools.jumpUrlManage({
                    status: "2",
                    jumpUrl: url,
                });
            }
        },

        // 此方法是给寻医问诊项目调用，跳转到互联网医院的医生主页  当前项目忽略
        // item_  包含 deptId staffId  miniId clientId  jumpUrl unicode
        // interHosp_origin
        gotoInterDoc(item_) {
            let item = { ...item_ };
            let url =
                "https://www.hfi-health.com:28181/h5InterHosp/#/doctor?staffId=" +
                item.staffId +
                "&deptId=" +
                item.deptId +
                "&unicode=" +
                item.unicode;
            if (!item.miniId) {
                // 没有小程序id 区分卫健和自建平台
                if (
                    window.localStorage.getItem("interHosp_origin") ==
                    "wjAliMini"
                ) {
                    // 卫健
                    if (!item.clientId) {
                        // 1.没有miniid，无clientid
                        item.jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            url
                        )}`;
                    } else {
                        // 2.没有miniid   有clientid
                        let tempUrl =
                            item.jumpUrl.indexOf("?") > -1
                                ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                                : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                        // 小程序跳转链接拼接
                        item.jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            tempUrl
                        )}`;
                    }
                    my.postMessage({
                        action: "gotoMini",
                        appId: item.miniId ? item.miniId : "2021002138635948",
                        url: item.jumpUrl,
                        authStatus: "2",
                    });
                } else {
                    // 自建平台
                    if (item.clientId) {
                        // 授权跳转医生主页  按照白皮书传参
                        // 2.没有miniid，有clientid
                        url =
                            item.jumpUrl.indexOf("?") > -1
                                ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                                : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                    }

                    let j =
                        "https://www.hfi-health.com:28181/h5InterHosp/#/auth?rUrl=" +
                        encodeURIComponent(url) +
                        "&authStatus=2";
                    window.location.href = j;
                }
            } else {
                //  有miniid  3 4 在卫健与自建平台是一致情况
                // 3.有miniid   有unicode 跳转对应小程序拼接参数
                if (
                    item.jumpUrl.indexOf(
                        "/www.hfi-health.com:28181/interHosp"
                    ) > -1
                ) {
                    let arr = item.jumpUrl.split("src=");
                    item.jumpUrl =
                        arr[0] +
                        "src=" +
                        encodeURIComponent(
                            `${arr[1]}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                        );
                } else {
                    item.jumpUrl =
                        item.jumpUrl.indexOf("?") > -1
                            ? `${item.jumpUrl}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                            : `${item.jumpUrl}?source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                }
                // }
                // alert(item.jumpUrl)
                my.postMessage({
                    action: "gotoMini",
                    appId: item.miniId,
                    url: item.jumpUrl,
                    authStatus: "2",
                });
            }
        },
        gotoWX(item, url) {
            if (!item.miniIdWx) {
                // 没有小程序id 区分卫健和自建平台
                // 自建平台
                if (item.clientId) {
                    // 2.没有miniid，有clientid
                    // 授权模式  微信平台有配置  取微信的配置
                    item.jumpUrl = item.jumpUrlWx || item.jumpUrl;
                    url =
                        item.jumpUrl.indexOf("?") > -1
                            ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                            : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                }
                // 1.无miniid 无clientid 直接跳转 自己页面的医生主页
                item.status = "2";
                item.jumpUrl = url;
                tools.jumpUrlManage(item);
            } else {
                //  有miniid  3 4 在卫健与自建平台是一致情况
                // if (item.unicode) {
                // 3.有miniid   有unicode 跳转对应小程序拼接参数

                // 微信小程序 市一市中模式不需要考虑

                item.jumpUrlWx =
                    item.jumpUrlWx.indexOf("?") > -1
                        ? `${item.jumpUrlWx}&source=miniWechat&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                        : `${item.jumpUrlWx}?source=miniWechat&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                // }
                // alert(item.jumpUrl)
                item.authStatus = "2";
                tools.gotoMini(item);
            }
        },
        goDoctorDetail(item_) {
            // 跳转医生主页
            let url;
            let item = { ...item_ };
            const disease = this.$route.query.searchValue
                ? `|${decodeURIComponent(this.$route.query.searchValue)}`
                : "";
            const deptName = this.$route.query.department
                ? `|${decodeURIComponent(this.$route.query.department)}`
                : "";
            const source = this.$route.query.type
                ? this.$route.query.type === "ask"
                    ? "在线咨询"
                    : "在线复诊"
                : disease || deptName
                ? "首页|找医生"
                : "首页|搜索";
            tools.handleSetPoint({
                trackingContent:
                    source + disease + deptName + "|" + item.doctorName,
            });
            //   if (item.teamFlag === "2") {
            //     url = `${window.location.origin}/${process.env.VUE_APP_FOLLOW}/#/teamHome?id=${item.doctorId}`;
            //     console.log("团队跳转链接", item);
            //   } else {
            url =
                location.origin +
                location.pathname +
                `#/doctor?staffId=${item.staffId}&deptId=${item.deptId}&unicode=${item.unicode}`;
            // "#/doctor?staffId=" +
            // item.staffId +
            // "&deptId=" +
            // item.deptId;
            // 阿克苏跳转拼接origin
            if (this.origin) {
                url = url + "&origin=" + this.origin;
            }
            console.log("医生跳转链接", item);
            //   }

            if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                    "health_zheliban_H5" ||
                sessionStorage.getItem("hlw_remoteChannel") == "health_smk_H5"
            ) {
                // 浙里办跳转 市民卡跳转
                window.location.href = url;
                return;
            }

            // 暖心助孕市中主页进来直接跳转本项目医生主页地址
            if (this.origin == "szMini") {
                tools.jumpUrl(url, 2);
                return;
            }

            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                // 微信小程序
                this.gotoWX(item, url);
                return;
            }
            if (!item.miniId) {
                // 没有小程序id 区分卫健和自建平台
                if (this.$store.state.isWjAliMini) {
                    // 卫健
                    if (!item.clientId) {
                        // 1.没有miniid，无clientid
                        item.jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            url
                        )}`;
                    } else {
                        // 2.没有miniid   有clientid
                        // let jumpUrl = "";
                        let tempUrl =
                            item.jumpUrl.indexOf("?") > -1
                                ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                                : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                        // 小程序跳转链接拼接
                        item.jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            tempUrl
                        )}`;
                    }
                    my.postMessage({
                        action: "gotoMini",
                        appId: item.miniId ? item.miniId : "2021002138635948",
                        url: item.jumpUrl,
                        authStatus: "2",
                    });
                } else {
                    // 自建平台
                    if (item.clientId) {
                        // 2.没有miniid，有clientid
                        url =
                            item.jumpUrl.indexOf("?") > -1
                                ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                                : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                    }
                    // 1.无miniid 无clientid 直接跳转 自己页面的医生主页
                    item.status = "2";
                    item.jumpUrl = url;
                    tools.jumpUrlManage(item);
                }
            } else {
                //  有miniid  3 4 在卫健与自建平台是一致情况
                // if (item.unicode) {
                // 3.有miniid   有unicode 跳转对应小程序拼接参数
                if (
                    item.jumpUrl.indexOf(
                        "/www.hfi-health.com:28181/interHosp"
                    ) > -1
                ) {
                    let arr = item.jumpUrl.split("src=");

                    // 市中市一 todo 团队的跳转 需要后续补充

                    //   if (item.teamFlag === "2") {
                    //     item.jumpUrl =
                    //       arr[0] +
                    //       "src=" +
                    //       encodeURIComponent(
                    //         `${arr[1]}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}&teamId=${item.doctorId}`
                    //       );
                    //     console.log("团队跳转链接", item);
                    //   } else {
                    item.jumpUrl =
                        arr[0] +
                        "src=" +
                        encodeURIComponent(
                            `${arr[1]}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                        );
                    //   }
                } else {
                    item.jumpUrl =
                        item.jumpUrl.indexOf("?") > -1
                            ? `${item.jumpUrl}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                            : `${item.jumpUrl}?source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                    // todo 团队的跳转 需要后续补充
                }
                // }
                // alert(item.jumpUrl)
                my.postMessage({
                    action: "gotoMini",
                    appId: item.miniId,
                    url: item.jumpUrl,
                    authStatus: "2",
                });
            }
        },
        async jumpUrl(type, status, serviceName, data) {
            if (data.inBlack === "0") {
                Toast("当前医生暂不提供服务");
                return;
            }
            if (status === "3") {
                Toast(`该医生当日${serviceName}已约满`);
            } else if (status === "1") {
                const content =
                    type == 1
                        ? "图文咨询"
                        : type == 2
                        ? "电话咨询"
                        : "在线复诊";
                await tools.handleSetPoint({
                    trackingContent: `${data.doctorName}|${content}`,
                    orgId: data.unicode,
                    orgName: data.hospName,
                });
                // type 1 图文 2电话 3 复诊
                // internetBuilder 3 在线咨询、在线复诊的链接配置成我们的 否则跳转纳里链接
                var url = "";
                // 支付宝
                let isZfb = navigator.userAgent.indexOf("AliApp") > -1;
                // 微信
                let isWeChat =
                    navigator.userAgent
                        .toLowerCase()
                        .indexOf("micromessenger") > -1;
                // 浙里办
                let isZLB =
                    navigator.userAgent.toLowerCase().indexOf("dtdreamweb") >
                        -1 ||
                    sessionStorage.getItem("hlw_remoteChannel") ==
                        "health_zheliban_H5";

                if (data.unicode == "2000096") {
                    if (type == 1) {
                        if (data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=imageConsultApply&staffId=${data.staffId}&deptId=${data.deptId}&unicode=${data.unicode}`;
                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=imageConsultApply&did=${data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=imageConsultApply&did=${data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=${data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=imageConsultApply&did=${data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else if (type == 2) {
                        if (data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=mobileConsultApply&staffId=${data.staffId}&deptId=${data.deptId}&unicode=${data.unicode}`;
                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=mobileConsultApply&did=${data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=mobileConsultApply&did=${data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=mobileConsultApply&did=${data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=mobileConsultApply&did=${data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else {
                        if (data.internetBuilder == "3") {
                            url = `${location.origin}/${process.env.VUE_APP_FOLLOW}/#/furConsultConfirm?unicode=${data.unicode}&staffId=${data.staffId}&deptId=${data.deptId}&isTeamOrder=${data.teamFlag}`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=askDoctorApply&did=${data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=askDoctorApply&did=${data.staffId}&source=jinTou-zfb-sz&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=askDoctorApply&did=${data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    }
                    tools.jumpUrl(url);
                } else if (
                    data.unicode == "2000006" ||
                    data.unicode == "12330100470116614F"
                ) {
                    if (type == 1) {
                        if (data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=imageConsultApply&staffId=${data.staffId}&deptId=${data.deptId}&unicode=${data.unicode}`;
                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=${data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=${data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=${data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=imageConsultApply&did=${data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else if (type == 2) {
                        if (data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=mobileConsultApply&staffId=${data.staffId}&deptId=${data.deptId}&unicode=${data.unicode}`;
                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=mobileConsultApply&did=${data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=mobileConsultApply&did=${data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=mobileConsultApply&did=${data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=mobileConsultApply&did=${data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else {
                        if (data.internetBuilder == "3") {
                            url = `${location.origin}/${process.env.VUE_APP_FOLLOW}/#/furConsultConfirm?unicode=${data.unicode}&staffId=${data.staffId}&deptId=${data.deptId}&isTeamOrder=${data.teamFlag}`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=askDoctorApply&did=${data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=askDoctorApply&did=${data.staffId}&source=jinTou-zfb-sy&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=askDoctorApply&did=${data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    }
                    tools.jumpUrl(url);
                } else {
                    if (type == 1) {
                        // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=imageConsultApply&did=${data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                        if (data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=imageConsultApply&staffId=${data.staffId}&deptId=${data.deptId}&unicode=${data.unicode}`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=imageConsultApply&did=${data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=${data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=imageConsultApply&did=${data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else if (type == 2) {
                        if (data.internetBuilder == "3") {
                            url =
                                location.origin +
                                location.pathname +
                                `#/consultService?module=mobileConsultApply&staffId=${data.staffId}&deptId=${data.deptId}&unicode=${data.unicode}`;

                            // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=mobileConsultApply&did=${data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=mobileConsultApply&did=${data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=mobileConsultApply&did=${data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=mobileConsultApply&did=${data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    } else {
                        if (data.internetBuilder == "3") {
                            url = `${location.origin}/${process.env.VUE_APP_FOLLOW}/#/furConsultConfirm?unicode=${data.unicode}&staffId=${data.staffId}&deptId=${data.deptId}&isTeamOrder=${data.teamFlag}`;
                        } else {
                            if (isZfb) {
                                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=askDoctorApply&did=${data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isWeChat) {
                                url = `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=askDoctorApply&did=${data.staffId}&source=jinTou-zfb-hlw&uInfo=`;
                            } else if (isZLB) {
                                // url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/zlbJT-jkhz/index?module=askDoctorApply&did=${data.staffId}&source=zlbJT-jkhz&uInfo=`;
                                //    浙里办维护页
                                url = `https://www.hfi-health.com:28181/hlw/zlbwhy.html`;
                            }
                        }
                    }
                    if (this.$store.state.isWjAliMini) {
                        // 在市平台支付宝小程序
                        let jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            url
                        )}`;
                        my.postMessage({
                            action: "gotoMini",
                            appId: "2021002138635948",
                            url: jumpUrl,
                            authStatus: "2",
                        });
                        return;
                    } else {
                        tools.jumpUrl(url, "2");
                    }
                }
            }
        },

        // 2023.8.10之前 原医生跳转
        goDoctorDetail_(item) {
            let url =
                location.origin +
                location.pathname +
                "#/doctor?staffId=" +
                item.staffId +
                "&deptId=" +
                item.deptId +
                "&unicode=" +
                item.unicode;
            console.log("医生跳转链接", item, this.$store.state);
            // 暖心助孕市中主页进来直接跳转本项目医生主页地址
            if (this.origin == "szMini") {
                tools.jumpUrl(url, 2);
            } else if (this.$store.state.isWjAliMini) {
                // 卫健小程序入口进来
                // 1.医生miniId为空,跳健康自建小程序医生主页
                if (!item.miniId) {
                    let jumpUrl = "";
                    if (item.clientId) {
                        let tempUrl =
                            item.jumpUrl.indexOf("?") > -1
                                ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                                : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                        // 小程序跳转链接拼接
                        jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            tempUrl
                        )}`;
                    } else {
                        jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                            url
                        )}`;
                    }
                    console.log("医生跳转链接", jumpUrl);
                    my.postMessage({
                        action: "gotoMini",
                        appId: "2021002138635948",
                        url: jumpUrl,
                        authStatus: "2",
                    });
                } else {
                    // 医生miniId不为空，跳第三方链接
                    my.postMessage({
                        action: "gotoMini",
                        appId: item.miniId,
                        url: item.jumpUrl,
                        authStatus: "2",
                    });
                }
            } else {
                // 自建小程序入口进来
                // 医生miniId为空
                if (!item.miniId) {
                    if (item.clientId) {
                        // 有商户号  需要拿到userid后，调用auth接口跳转
                        url =
                            item.jumpUrl.indexOf("?") > -1
                                ? `${item.jumpUrl}&clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`
                                : `${item.jumpUrl}?clientId=${item.clientId}&source=miniAlipay&unicode=${item.unicode}&doctorId=${item.staffId}&deptId=${item.deptId}`;
                    }
                    // 原医生跳转
                    // tool.jumpUrl(url, "2")
                    item.status = "2";
                    item.jumpUrl = url;
                    tools.jumpUrlManage(item);
                } else {
                    // 医生miniId不为空，跳第三方链接
                    my.postMessage({
                        action: "gotoMini",
                        appId: item.miniId,
                        url: item.jumpUrl,
                        authStatus: "2",
                    });
                }
            }
        },
        // 无引用
        handleAsk(type, item) {
            let url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=${type}&did=${item.staffId}&source=jinTou-zfb-hlw&uInfo=`;
            console.log("医生主页", url);
            debugger;
            // 暖心助孕市中主页进来直接跳转
            if (this.origin == "szMini") {
                url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=${type}&did=${item.staffId}&source=jinTou-zfb-sz&uInfo=`;
                tools.jumpUrl(url);
            } else if (this.$store.state.isWjAliMini) {
                // 卫健小程序入口进来
                // 医生miniId为空,健康自建小程序-打开纳里的页面
                if (!item.miniId) {
                    let jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                        url
                    )}`;
                    my.postMessage({
                        action: "gotoMini",
                        appId: "2021002138635948",
                        url: jumpUrl,
                        authStatus: "1",
                    });
                } else {
                    // 医生miniId不为空，跳转到第三方小程序
                    my.postMessage({
                        action: "gotoMini",
                        appId: item.miniId,
                        url: item.jumpUrl,
                        authStatus: "1",
                    });
                }
            } else {
                // 自建小程序入口进来
                // 医生miniId为空
                if (!item.miniId) {
                    tools.jumpUrl(url, "2");
                } else {
                    // 医生miniId不为空，跳第三方链接
                    my.postMessage({
                        action: "gotoMini",
                        appId: item.miniId,
                        url: item.jumpUrl,
                        authStatus: "1",
                    });
                }
            }
        },
        // picAsk(id) {
        //   let url = `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=imageConsultApply&did=${id}&source=jinTou-zfb-hlw&uInfo=`;
        //   tools.jumpUrl(url, "2");
        // },
    },
};
</script>
  
  <style lang="less" scoped>
.doctor-list {
    .doctor-info {
        width: 3.45rem;
        margin: 0 auto;
        box-sizing: border-box;
        background: #ffffff;
        display: flex;
        position: relative;
        // 以下为doctor-info默认样式，如需变动传doctorStyle自定义
        padding: 0.13rem 0.16rem 0.03rem 0.11rem;
        margin-top: 0.12rem;
        margin-bottom: 0.12rem;
        box-shadow: 0.02rem 0.02rem 0.1rem 0 rgba(186, 186, 186, 0.2);
        border-radius: 0.08rem;
        .header {
            width: 0.47rem;
            height: 0.47rem;
            min-width: 0.47rem;
            min-height: 0.47rem;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 0.13rem;
            img {
                width: 0.47rem;
                //   height: 0.47rem;
            }
        }
    }
    & > :last-child {
        border-bottom: none !important;
    }
    .info {
        width: 100%;
        .row1 {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.06rem;
            & > div {
                display: flex;
                align-items: flex-end;
                p {
                    font-size: 0.13rem;
                    font-family: PingFang SC;
                    font-weight: 400;
                    color: #333333;
                    margin &:nth-of-type(1) {
                        font-size: 0.15rem;
                        font-family: PingFang SC;
                        font-weight: 500;
                        color: #333333;
                    }
                    &:nth-of-type(2) {
                        margin: 0 0.1rem;
                    }
                }
                img {
                    width: 0.59rem;
                    height: 0.24rem;
                }
            }
        }
        .row2 {
            display: flex;
            margin-bottom: 0.06rem;
            .special-p {
                font-family: PingFang SC;
                font-weight: 400;
                margin-right: 0.08rem;
                // padding: 0 0.06rem;
                height: 0.17rem;
                line-height: 0.17rem;
                display: flex;
                align-items: center;
                //   width: 1.2rem;
                //   height: 0.34rem;
                //   text-align: center;
                //   line-height: 0.34rem;
                border: 1px solid #3ebfa0;
                border-radius: 0.02rem;
                // margin-left: -0.3rem;
                // margin-top: -0.085rem;
                span {
                    font-size: 0.12rem;
                    transform: scale(0.83);
                    color: #3ebfa0;
                    line-height: 0.1rem;
                }
            }
            .normal-p {
                font-size: 0.13rem;
                color: #686b73;
                margin-top: 0.01rem;
                flex: 1;
            }
        }
        .row3 {
            font-size: 0.13rem;
            color: #888888;
            margin-bottom: 0.06rem;
            p {
                width: 2.5rem;
                text-overflow: ellipsis;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }
        }
        .row4 {
            display: flex;
            p {
                font-size: 0.11rem;
                color: #888888;
                span {
                    color: #3ebfa0;
                    margin-left: 0.04rem;
                }
                &:nth-of-type(1) {
                    margin-right: 0.25rem;
                }
            }
        }
        .row5 {
            display: flex;
            margin-top: 0.12rem;
            flex-wrap: wrap;
            width: 110%;
            & > div {
                // width: 1.07rem;
                height: 0.28rem;
                box-sizing: border-box;
                border-radius: 0.14rem;
                border: 1px solid #999999;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.12rem;
                color: #888888;
                margin-right: 0.15rem;
                margin-bottom: 0.1rem;
                padding-left: 0.1rem;
                padding-right: 0.1rem;
                span {
                    font-size: 0.15rem;
                    color: #3ebfa0;
                    margin-left: 0.03rem;
                }
                img {
                    width: 0.12rem;
                    height: 0.12rem;
                    margin-right: 0.07rem;
                }
            }
        }
    }
    .yygh {
        width: 55px;
        height: 20px;
        line-height: 20px;
        background: #3ebfa0;
        border-radius: 10px;
        position: absolute;
        top: 13px;
        right: 16px;
        span {
            display: inline-block;
            width: 100%;
            height: 100%;
            font-size: 12px;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
            position: relative;
            &::before {
                content: "";
                position: absolute;
                top: -10px;
                right: -10px;
                bottom: -10px;
                left: -10px;
            }
        }
    }
    .limit {
        font-size: 12px !important;
        color: #888888 !important;
    }
}
</style>
