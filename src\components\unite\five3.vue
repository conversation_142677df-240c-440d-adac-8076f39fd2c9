<!--
 * @Author: your name
 * @Date: 2024-12-05 14:18:39
 * @LastEditTime: 2024-12-30 17:43:18
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 左五右三固定展台
 * @FilePath: \h5-interhosp\src\components\unite\five3.vue
-->
<template>
    <div class="five3" v-if="leftlist.length !== 0">
        <div class="left_">
            <!-- 左侧展台的第一个应用作为底部背景图 -->
            <img class="bj_" v-if="bjUrl" :src="bjUrl" alt="" />
            <div class="left_con">
                <div
                    v-for="(item, index) in leftlist"
                    :key="index"
                    class="items"
                    @click="go(item, index, elementId1)"
                    :data-exposure-id="elementId1"
                    :data-exposure-content="item.elementContent"
                    :data-exposure-sn="index + 1"
                >
                    <img
                        v-lazy="item.iconUrl"
                        :key="item.iconUrl"
                        alt=""
                        class="icon"
                    />
                    <div class="applicationName">
                        {{ item.applicationName }}
                    </div>
                    <img
                        :src="require('@/images/right53.png')"
                        alt=""
                        class="angle"
                    />
                </div>
            </div>
        </div>
        <div class="right_">
            <div v-for="(item, index) in rightlist" :key="index">
                <img
                    @click="go(item, index, elementId2)"
                    :data-exposure-id="elementId2"
                    :data-exposure-content="item.elementContent"
                    :data-exposure-sn="index + 1"
                    class="right_img"
                    v-lazy="item.iconUrl"
                    :key="item.iconUrl"
                    alt=""
                />
            </div>
        </div>
    </div>
</template>
  
  <script>
import tools from "@/util/tools.js";
export default {
    props: {
        datalists: {
            type: Object,
        },
        dataOtherLists: {
            type: Object,
        },
    },
    data() {
        return {
            leftlist: [],
            rightlist: [],
            elementId: "",
            isShow: false,
            elementId1: "",
            elementId2: "",
            bjUrl: "",
        };
    },
    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("左五右三", temp);
        let temp_other = JSON.parse(JSON.stringify(this.dataOtherLists));
        console.log("左五右三222", temp_other);
        if (
            temp.stageTypeName == "固定左五右三-左" &&
            temp.childStageList !== 0
        ) {
            // 第一个子展台数据名称为固定左五右三-左，才可以渲染左侧
            this.bjUrl = temp.childStageList[0].iconUrl;
            this.leftlist = temp.childStageList.slice(1);
            this.elementId1 = temp.elementId;
            console.log("左五右三", this.leftlist);
        }
        if (
            temp_other.stageTypeName == "固定左五右三-右" &&
            temp_other.childStageList.length !== 0
        ) {
            //第二个数据的子展台类别为固定左五右三-右时，才可以进行渲染右侧
            this.rightlist = temp_other.childStageList;
            this.elementId2 = temp_other.elementId;
            console.log("左五右三rightList", this.rightlist);
        }
    },
    methods: {
        go(item, index, elementId) {
            item.elementId = elementId;
            item.index = index;
            tools.jumpUrlManage(item);
        },
    },
};
</script>
  
  <style lang="less" scoped>
img[lazy="loading"] {
    display: block;
    width: 20%;
    line-height: 100%;
    margin: auto;
}
.five3 {
    width: 3.25rem;
    // height: 2.325rem;
    background-color: #fff;
    border-radius: 0.08rem;
    margin: 0 auto;
    margin-bottom: 0.1rem;
    display: flex;
    justify-content: space-between;
    padding: 0.1rem;
}

.left_ {
    position: relative;
    margin-right: 0.08rem;
    .bj_ {
        width: 1.6rem;
    }
    .left_con {
        width: 1.3rem;
        height: 1.47rem;
        border-radius: 0.04rem;
        background-color: #fff;
        position: absolute;
        top: 0.475rem;
        left: 0.08rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: hidden;
        padding: 0.05rem;
        .items {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .applicationName {
                flex: 1;
                text-align: left;
                margin-left: 0.045rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .icon {
                width: 0.25rem;
                height: 0.25rem;
            }
            span {
                color: #333;
                font-size: 0.13rem;
            }
            .angle {
                width: 0.045rem;
                height: 0.08rem;
            }
        }
    }
}
.right_ {
    div {
        width: 1.58rem;
        height: 0.655rem;
        margin-bottom: 0.08rem;
    }
    .right_img {
        width: 100%;
        height: 100%;
    }
}
</style>