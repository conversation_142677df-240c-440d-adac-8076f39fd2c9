<!--
 * @Author: shenpp
 * @Date: 2023-08-17
 * @LastEditTime: 2024-04-17 11:27:42
 * @Description: 咨询详情--病人详情描述
-->
<template>
    <div class="order_desc">
        <template v-if="$route.query.from != 'my'">
            <div class="item">
                <div class="left">创建时间</div>
                <div class="right">{{ data.createTime }}</div>
            </div>
            <!-- 支付前取消/待支付不显示 -->
            <div
                class="item"
                v-if="['54', '0'].indexOf(data.orderStatus) == -1"
            >
                <div class="left">支付时间</div>
                <div class="right">{{ data.payTime }}</div>
            </div>
            <div class="item">
                <div class="left">订单编号</div>
                <div class="right">
                    <span>{{ data.orderNo }}</span>
                    <span class="divider">|</span>
                    <span @click="copyOrder">复制</span>
                </div>
            </div>
            <!-- 支付前取消/待支付不显示 -->
            <div
                class="item"
                v-if="['54', '0'].indexOf(data.orderStatus) == -1"
            >
                <div class="left">支付流水号</div>
                <div class="right">{{ data.payOrderNo }}</div>
            </div>
            <div class="item">
                <div class="left">订单金额</div>
                <div class="right">￥{{ data.totalAmount }}</div>
            </div>
            <div class="item" v-if="true">
                <div class="left">优惠抵扣</div>
                <div class="right payColor">{{ data.discountAmount }}</div>
            </div>
            <div
                class="item"
                v-if="['52', '6', '7'].indexOf(data.orderStatus) > -1"
            >
                <div class="left">退款方式</div>
                <div class="right">原支付返还</div>
            </div>
            <div
                class="item"
                v-if="['52', '6', '7'].indexOf(data.orderStatus) > -1"
            >
                <div class="left">退款金额</div>
                <div class="right">￥{{ data.refundAmount }}</div>
            </div>
            <div
                class="item"
                v-if="['52', '6', '7'].indexOf(data.orderStatus) > -1"
            >
                <div class="left">退款流水号</div>
                <div class="right">{{ data.visitSeqNum }}</div>
            </div>
            <div class="item border">
                <div class="left">实付金额</div>
                <div class="right payColor">
                    <!-- 有优惠抵扣的，会有calAmount -->
                    ￥{{ data.calAmount || data.cashAmount }}
                </div>
            </div>
        </template>
        <template v-else>
            <div class="item">
                <div class="left">申请时间</div>
                <div class="right">{{ data.createTime }}</div>
            </div>
            <div class="item">
                <div class="left">订单编号</div>
                <div class="right">
                    <span>{{ data.orderNo }}</span>
                    <span class="divider">|</span>
                    <span @click="copyOrder">复制</span>
                </div>
            </div>
            <div class="item">
                <div class="left">退款方式</div>
                <div class="right">原支付返还</div>
            </div>
            <div class="item border">
                <div class="left">退款金额</div>
                <div class="right payColor">￥{{ data.refundAmount }}</div>
            </div>
        </template>
    </div>
</template>

<script>
import { Toast } from "vant";
export default {
    props: {
        data: {
            type: Object,
        },
    },
    data() {
        return {};
    },

    mounted() {},

    methods: {
        copyOrder() {
            const orderNo =
                this.$route.query.from == "my"
                    ? this.data.payOrderNo
                    : this.data.orderNo;
            this.$copyText(orderNo).then(
                () => {
                    Toast("已复制");
                },
                () => {
                    Toast("复制失败");
                }
            );
        },
    },
};
</script>

<style lang="less" scoped>
.order_desc {
    background-color: #ffffff;
    margin-top: 0.12rem;
    border-radius: 0.08rem;
    padding: 0.2rem 0.15rem;
}
.item {
    display: flex;
    margin-bottom: 0.2rem;
    .left {
        flex: 1;
        text-align: justify;
        color: #666666;
        font-size: 0.14rem;
    }
    .right {
        flex: 3;
        color: #333333;
        font-size: 0.14rem;
        text-align: right;
        word-break: break-all;
        padding-left: 0.2rem;
        .divider {
            margin: 0 0.15rem;
        }
    }
}
.border {
    padding-top: 0.2rem;
    margin-bottom: 0;
    border-top: 1px solid #e8e9ec;
}
.payColor {
    color: #fe963a !important;
}
</style>
