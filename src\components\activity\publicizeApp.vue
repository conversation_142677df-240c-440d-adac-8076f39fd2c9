<!--
 * @Author: your name
 * @Date: 2025-05-28 09:43:40
 * @LastEditTime: 2025-06-05 14:44:28
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 宣传应用展台
 * @FilePath: \h5-interhosp\src\components\activity\publicizeApp.vue
-->
<template>
    <!-- 宣传展台区 -->
    <div
        v-show="isShow"
        style="
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            justify-content: space-evenly;
        "
        class="conStyle"
    >
        <div
            class="stageList"
            style="
                width: 1.58rem;
                height: 1.28rem;
                margin-top: 0.1rem;
                margin-bottom: 0.1rem;
            "
        >
            <img
                style="width: 100%"
                v-lazy="commonlist[0].iconUrl"
                @click="jumpUrl(commonlist[0], childStageId, childStageName)"
                alt=""
            />
        </div>
        <div
            class="stageList"
            style="
                width: 1.58rem;
                height: 1.28rem;
                margin-top: 0.1rem;
                margin-bottom: 0.1rem;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            "
        >
            <img
                style="width: 100%"
                v-lazy="commonlist[1].iconUrl"
                @click="jumpUrl(commonlist[1], childStageId, childStageName)"
                alt=""
            />
            <img
                style="width: 100%"
                v-lazy="commonlist[2].iconUrl"
                @click="jumpUrl(commonlist[2], childStageId, childStageName)"
                alt=""
            />
        </div>
    </div>
</template>

<script>
import tools from "@/util/tools";
export default {
    props: {
        datalists: {
            type: Object,
        },
        stageName: {
            type: String,
        },
        stageId: {
            type: String,
        },
    },
    data() {
        return {
            commonlist: "",
            elementId: "",
            isShow: false,
            childStageId: "",
            childStageName: "",
        };
    },

    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("活动页宣传应用", temp);
        if (
            temp.stageTypeName === "宣传应用" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.commonlist = temp.childStageList;
            this.childStageId = temp.childStageId;
            this.childStageName = temp.childStageName;
        }
    },

    methods: {
        jumpUrl(item, id, name) {
            console.log("跳转参数", item);
            if (item.jumpUrl) {
                tools
                    .handleSetPoint({
                        stageId: this.stageId,
                        childStageId: id,
                        stageAppId: item.applicationId,
                        trackingContent: `${this.stageName}-${name}-${item.elementContent}`,
                        businessName: "activity",
                    })
                    .then(() => {
                        tools.jumpUrlManage(item);
                    });
            }
        },
    },
};
</script>
<style scoped>
.stageList {
    flex-shrink: 0;
    width: 20%;
    margin-bottom: 15px;
}
.conStyle {
    background-color: #fff;
    width: 345px;
    border-radius: 8px;
    margin: 0 auto;
    margin-bottom: 10px;
}
</style>