import fetch from "./http";
import util from "../util/util";

//查询医生主页链接  市一 市中 市三
export const getDocUrl = (data) => {
    return fetch(
        "/inthos/choose/chooseUrl",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            debugger
            if (response.success === 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => {
            debugger
            return "";
        });
};
//查询医生主页链接  市一 市中 市三
export const getHospUrl = (unicode) => {
    return fetch(
        "/inthos/choose/chooseHospUrl",
        { unicode },
        "post"
    )
        .then((response) => {
            debugger
            if (response.success === 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => {
            debugger
            return "";
        });
};

// 校验二维码的有效期
export const entryJudge = (data) => {
    return fetch(
        "/inthos//aksu/entryJudge",
        JSON.stringify(data),
        "post"
    )
        .then((response) => {
            debugger
            if (response.success === 1) {
                return response.value;
            } else {
                let message = response.respDesc || "网络连接超时，请稍后再试~";
                util.showToast(message);
            }
        })
        .catch((err) => {
            debugger
            return "";
        });
};
