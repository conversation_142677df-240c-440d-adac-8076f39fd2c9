<template>
    <div class="container">
        <div class="backImg"></div>
        <div class="ModContainer">
            <div class="intro block">
                <div class="summary">
                    <img
                        class="leftImg"
                        :src="data.newHospImgUrl || data.internetImgUrl"
                        alt=""
                    />
                    <div>
                        <h3 class="title">{{ data.hospName }}</h3>
                        <span class="tag" v-if="data.hospTypeCode">{{
                            data.hospTypeCode | getHospitalType
                        }}</span>
                    </div>
                </div>
                <div class="brief" v-if="data.introduction">
                    <p class="tips">
                        医院简介：<span>{{ data.introduction }}</span>
                    </p>
                    <p class="moretxt" @click="showPopup = true">
                        <span class="fuzzy"></span>查看更多>
                    </p>
                </div>
                <div class="connect">
                    <p>
                        <img src="@/images/img/org_addr.png" alt="" />
                        <span>{{ data.address }}</span>
                    </p>
                    <img
                        v-if="data.telephone"
                        @click="callPhone(data.telephone)"
                        class="telephone"
                        src="@/images/img/org_tel.png"
                        alt=""
                    />
                </div>
            </div>
            <div class="banner block">
                <van-swipe
                    :autoplay="3000"
                    :loop="true"
                    indicator-color="#fff"
                    v-if="bannerList && bannerList.length > 0"
                >
                    <van-swipe-item
                        v-for="(item, index) in bannerList"
                        :key="index"
                    >
                        <img
                            class="banner-img"
                            :src="item.iconUrl"
                            @click="jumpUrlManage(item)"
                            alt=""
                        />
                    </van-swipe-item>
                </van-swipe>
            </div>

            <div class="service block">
                <div class="service-func">
                    <p class="title">服务内容</p>
                    <div>
                        <div
                            class="service-func-item"
                            @click="toSearchDoctorList(0)"
                        >
                            <img src="@/images/img/doctor_twzx.png" alt="" />
                            <p>在线咨询</p>
                        </div>
                        <div
                            class="service-func-item"
                            @click="toSearchDoctorList(1)"
                        >
                            <img src="@/images/img/doctor_zxfz.png" alt="" />
                            <p>在线复诊</p>
                        </div>
                        <div
                            class="service-func-item"
                            @click="
                                jumpUrl(
                                    'https://www.hfi-health.com:28181/appointWithDoc/#/department?origin=hlwywMini&hospId=47011663033010211A1001'
                                )
                            "
                            v-if="internetId == '2000140'"
                        >
                            <img src="@/images/img/org_appoint.png" alt="" />
                            <p>预约挂号</p>
                        </div>
                        <div
                            class="service-func-item"
                            @click="
                                jumpUrl(
                                    'https://zfb.hz3yy.com/hzssyylb-system-h5/index.html?u=',
                                    true
                                )
                            "
                            v-if="internetId == '2000140'"
                        >
                            <img src="@/images/img/tuofa.png" alt="" />
                            <p>智能脱发识别</p>
                        </div>
                        <div
                            class="service-func-item"
                            @click="goSZ(item.link, item.type)"
                            v-for="item of serviceList"
                            :key="item.name"
                            v-show="
                                internetId === '2000096' ||
                                internetId === '2000006'
                            "
                        >
                            <img :src="item.picture" alt="" />
                            <p>{{ item.name }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="feature block"
                v-if="featureDepartList && featureDepartList.length"
            >
                <p class="title">特色科室</p>
                <div class="feature-list">
                    <div
                        v-for="(item, index) in featureDepartList"
                        :key="index"
                        @click="toSearchDoctorList(3, item)"
                    >
                        <span>{{ item.deptClassName }}</span>
                    </div>
                </div>
            </div>
            <div
                class="hotdoctor block"
                v-if="hotdoctorList && hotdoctorList.length"
            >
                <p class="title">热门医生</p>
                <div>
                    <doctor-list
                        :doctorList="hotdoctorList"
                        :doctorStyle="doctorStyle"
                    ></doctor-list>
                </div>
            </div>
        </div>
        <float-window
            v-if="showFloatW"
            active="jigou"
            @jump="jumpPage"
        ></float-window>
        <popup-define :show.sync="showPopup" :data="popupData"></popup-define>
        <pop v-if="showPopWin" :dialogInfo="dialogInfo"></pop>
    </div>
</template>

<script>
import { Swipe, SwipeItem, Toast } from "vant";
import {
    getHospInfo,
    queryBanner,
    famousDepartmentPageList,
    famousDoctorPageList,
    creatRecord,
} from "../../api/api";
import tools from "@/util/tools.js";
import PopupDefine from "./components/popupDefine.vue";
import DoctorList from "@/components/doctorList.vue";
import Pop from "@/components/popUP.vue";
import FloatWindow from "@/components/floatWindow.vue";
import common from "@/util/common";
export default {
    name: "HospitalOrganize",
    components: {
        PopupDefine,
        DoctorList,
        Pop,
        FloatWindow,
        [Swipe.name]: Swipe,
        [SwipeItem.name]: SwipeItem,
    },
    filters: {
        getHospitalType(value) {
            let methods = "";
            switch (value) {
                case "3":
                    methods = "市属公立";
                    break;
                case "6":
                    methods = "区县";
                    break;
                case "5":
                    methods = "社区";
                    break;
                default:
                    methods = "其他";
                    break;
            }
            return methods;
        },
    },
    data() {
        return {
            shizhongService: [
                {
                    name: "暖心助孕",
                    picture: require("@/images/shizhong/nuanxinzhuyun.png"),
                    link: "https://www.hfi-health.com:28181/activityModel/#/helpPregnancy",
                },
                {
                    name: "智能导诊",
                    picture: require("@/images/shizhong/zhinengdaozhen.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=selfDiagnose&source=jinTou-zfb-hlw&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "体质辨识",
                    picture: require("@/images/shizhong/tizhibianshi.png"),
                    link: `https://www.zjjgpt.com/healthWeb-base-ZJS/views/assess/assessBegin.html?appId=0&appType=4&assessId=22101214361923338619&shareType=0&conType=1`,
                },
                {
                    name: "中药物流查询",
                    picture: require("@/images/shizhong/zhongyaowuliuchaxun.png"),
                    link: "https://cf.eastpharm.com/",
                },
                {
                    name: "在线建档",
                    picture: require("@/images/shizhong/zaixianjiandang.png"),
                    link: "",
                },
                {
                    name: "医保政策",
                    picture: require("@/images/shizhong/yibaozhengce.png"),
                    link: "https://hangzhouzyy-ddjy-h5.mediinfo.cn/yiBaoZhengCe?isAlipayMini=1&routerName=YiBaoZC",
                    type: "thirdPage",
                },
                {
                    name: "就诊指南",
                    picture: require("@/images/shizhong/jiuzhenzhinan.png"),
                    link: "https://www.hztcm.cn/guide/index/cid/10007.html",
                    type: "thirdPage",
                },
                {
                    name: "医院微官网",
                    picture: require("@/images/shizhong/yiyuanweiguanwang.png"),
                    link: "https://www.hztcm.cn/",
                    type: "thirdPage",
                },
                {
                    name: "找车位",
                    picture: require("@/images/shizhong/zhaochewei.png"),
                    link: "https://mapi.zjzwfw.gov.cn/web/mgop/gov-open/zj/2001301311/reserved/index.html#/",
                    type: "thirdPage",
                },
                {
                    name: "健康百科",
                    picture: require("@/images/shizhong/jiankangbaike.png"),
                    link: "https://j.ganlan999.com/a/Gkw1woHXLo?intoType=dynamic",
                    type: "thirdPage",
                },
                // {
                //     name: "便捷购药",
                //     picture: require("@/images/shizhong/bianjiegouyao.png"),
                //     link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sz/index?module=QuickBuyDrugEntry&organId=2000761&source=jinTou-zfb-sz&uInfo=${window.localStorage.getItem(
                //         "encrUser"
                //     )}`,
                // },
            ],
            shiyiService: [
                /* {
                    name: "治裂膏门诊",
                    picture: require("@/images/shiyi/zhiliegao.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=182680&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                }, */
                {
                    name: "治裂膏门诊",
                    picture: require("@/images/shiyi/zhiliegao.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=singleDoctIndex&did=182680&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "团队复诊",
                    picture: require("@/images/shiyi/team.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=onlineReferral&isTeamRevisit=true&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "药师咨询",
                    picture: require("@/images/shiyi/yaoshi.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=SlowSickConsult&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "物流查询",
                    picture: require("@/images/shiyi/wuliu.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=RecipeList&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "新冠咨询",
                    picture: require("@/images/shiyi/xinguan.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185687&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "发热咨询",
                    picture: require("@/images/shiyi/hot.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185692&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "心血管病咨询",
                    picture: require("@/images/shiyi/xinxueguan.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185693&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "脑血管病咨询",
                    picture: require("@/images/shiyi/naoxueguan.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185694&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "方便门诊",
                    picture: require("@/images/shiyi/fangbian.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185740&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                {
                    name: "肿瘤咨询",
                    picture: require("@/images/shiyi/zhongliu.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=186707&organId=2000006&source=jinTou-zfb-sy&uInfo=${window.localStorage.getItem(
                        "encrUser"
                    )}`,
                },
                // {
                //     name: "心血管病咨询",
                //     picture: require("@/images/shiyi/zhinengdaozhen.png"),
                //     link: `${window.localStorage.getItem(
                //         "encrUser"
                //     )}`,
                // },
                {
                    name: "在线建档",
                    picture: require("@/images/shiyi/online.png"),
                    link: "",
                },
            ],
            serviceList: [],
            internetId: "",
            hospName: "",
            hospId: "",
            data: {},
            bannerList: [],
            featureDepartList: [],
            hotdoctorList: [],
            showPopup: false,
            popupData: {
                title: "医院简介",
                subTitle: " 医院概况",
                intro: "",
            },
            doctorStyle: {
                boxShadow: "none",
                borderBottom: "1px solid #f1f1f1",
                borderRadius: 0,
            },
            showPopWin: false,
            dialogInfo: {},
            showFloatW: false,
            origin: "",
        };
    },
    created() {
        this.internetId = this.$route.query.internetId || "";
        window.localStorage.setItem("internetId", this.internetId);
        if (this.internetId == "2000096") {
            this.serviceList = this.shizhongService;
        } else if (this.internetId == "2000006") {
            this.serviceList = this.shiyiService;
        }
        this.hospName = this.$route.query.hospName || "";
        this.hospId = this.$route.query.hospId || this.$route.query.orgId || "";
        // this.getBannerAndPopup();
        this.getOrgData();
        this.getFamousDoctor();
        this.getFamousDepartment();
        debugger;
        // 防止缓存会被清理掉  增加判断链接地址上orgId参数
        if (
            window.localStorage.getItem("orgId") ||
            common.getUrlParam("orgId")
        ) {
            this.showFloatW = true;
        }
        // if (this.internetId === "2000096") {
        //   this.handleCreateRecord();
        // }
        this.origin = common.getUrlParam("origin");
    },
    mounted() {},
    methods: {
        goSZ(url, type) {
            console.log("市中~~", { action: type, returnURL: url });
            if (type) {
                //   市中增加的医保政策等链接，需要与市中小程序交互
                my.postMessage({
                    action: type,
                    returnURL: url,
                });
                return;
            }
            if (url) {
                window.location.href = url;
            } else {
                this.handleCreateRecord();
            }
        },
        jumpUrl(url, noLogin) {
            debugger;
            tools.jumpUrl(url, noLogin ? 1 : 2);
        },
        handleCreateRecord() {
            let logTraceId = new Date().getTime();
            let userInfo = JSON.parse(window.localStorage.getItem("userInfo"));
            creatRecord({
                logTraceId: logTraceId,
                bizContent: {
                    reqSeq: logTraceId,
                    appId: "2020021901",
                    pageUrl: "2",
                    orgName:
                        this.internetId == "2000096"
                            ? "杭州市中医院"
                            : this.hospName || this.data.hospName,
                    certType: "1",
                    certCode: userInfo.paperNum,
                    name: userInfo.name,
                    phone: userInfo.phone,
                    callbackUrl: window.location.href,
                    organId: this.internetId,
                },
            })
                .then((res) => {
                    if (String(res.buildFileStatus) !== "3") {
                        window.location.href = res.pageUrl;
                    } else {
                        Toast.fail("您已建档");
                    }
                })
                .catch(() => {
                    Toast.fail("获取建档地址失败");
                });
        },
        getBannerAndPopup() {
            queryBanner({
                appType: "6",
                orgId: this.hospId,
                floatAdPositionName: "互联网医院机构主页",
            }).then((response) => {
                console.log(response);
                this.bannerList = response ? response : [];
            });
            queryBanner({
                appType: "8",
                floatAdPositionName: "互联网医院机构主页",
                orgId: this.hospId,
                orgName: this.hospName,
            }).then((response) => {
                console.log("88888", response);
                if (response && response.length > 0) {
                    this.dialogInfo = response[0];
                    this.showPopWin = true;
                }
            });
        },
        getOrgData() {
            let data = {
                channel: "6",
                hospTypeName: "",
                locationCode: "",
                hospLevel: "",
                platform: "",
                todayFlag: "",
                internetFlag: "1",
                internetId: this.internetId,
                // hospId: this.hospId,
            };
            getHospInfo(data).then((r) => {
                console.log(r, "123");
                if (r && r.length > 0) {
                    this.data = r[0];
                    console.log("000", this.data);
                    this.popupData.intro = this.data.introduction;
                    // debugger;
                    this.hospId = this.data.hospId;
                    this.getBannerAndPopup();
                }
            });
        },
        getFamousDepartment() {
            let data = {
                internetId: this.internetId,
            };
            famousDepartmentPageList(data).then((r) => {
                if (r) {
                    this.featureDepartList = r.list;
                    console.log(r.list);
                }
            });
        },
        getFamousDoctor() {
            let data = {
                internetId: this.internetId,
            };
            famousDoctorPageList(data).then((r) => {
                if (r) {
                    let list = r.list;
                    list.forEach((item) => {
                        item.hospId = this.data.hospId;
                    });
                    console.log("-------", list);
                    this.hotdoctorList = list;
                }
            });
        },
        callPhone(phone) {
            window.location.href = `tel:${phone}`;
        },
        toSearchDoctorList(type, item) {
            var query = {
                from: this.internetId,
            };
            // 市中主页 跳转时，需要拼接上origin
            query.origin = this.origin;
            if (type == 0) {
                query["type"] = "ask";
            } else if (type == 1) {
                query["type"] = "repeatAsk";
            } else {
                query["department"] = item.deptClassCode;
            }
            this.$router.push({
                path: "searchDoctorList",
                query,
            });
        },
        jumpUrlManage(item) {
            /* if (this.internetId == "2000096") {
                //   市中的茶饮活动跳转，需要跟市中小程序交互
                console.log("市中", item);
                if (navigator.userAgent.indexOf("AliApp") > -1) {
                    my.postMessage({
                        action: "thirdPage",
                        returnURL: item.jumpUrl,
                    });
                } else {
                    window.location.href = item.jumpUrl;
                }

            } else {
                tools.jumpUrlManage(item);
            } */
            if (
                this.internetId == "2000096" &&
                item.jumpUrl.indexOf("hfi-health.com") > -1
            ) {
                item.status = "2";
            }
            tools.jumpUrlManage(item);
        },
        jumpPage(val) {
            console.log("jjjjjj", val);
            if (val == "geren") {
                this.$router.replace({
                    path: "mine",
                    query: {
                        orgId: this.hospId,
                        internetId: this.internetId,
                        hospName: this.hospName,
                    },
                });
            }
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-family: "PingFang SC";

    .backImg {
        height: 237px;
        background: url("@/images/img/com_back.png");
        background-size: 100% auto;
        background-repeat: no-repeat;
    }
    .ModContainer {
        margin-top: -235px;
        background-color: transparent;
    }
    .block {
        margin: 12px 15px;
        background: #ffffff;
        border-radius: 8px;
        overflow: hidden;
    }
    .intro {
        .summary {
            display: flex;
            align-items: flex-start;
            padding: 15px;
            .leftImg {
                width: 60px;
                height: 60px;
                font-size: 0;
                flex-shrink: 0;
                border-radius: 4px;
                background-size: 100% 100%;
                margin-right: 17px;
                margin-top: 3px;
            }
            .title {
                font-size: 19px;
                font-weight: 500;
                color: #333333;
                margin-bottom: 10px;
                line-height: 1.2;
            }
            .tag {
                border-radius: 2px;
                border: 1px solid #3ebfa0;
                font-size: 10px;
                line-height: 1;
                font-family: "PingFangSC";
                font-weight: 400;
                color: #3ebfa0;
                display: inline-block;
                padding: 3px 5px;
            }
        }
        .brief {
            margin: 0 15px 15px;
            position: relative;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            position: relative;
            font-size: 13px;
            font-weight: 400;

            .tips {
                color: #333333;
            }
            span {
                color: #777777;
            }
            .fuzzy {
                height: 20px;
                background: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0) 0%,
                    #ffffff 100%
                );
            }
            .moretxt {
                position: absolute;
                right: 0;
                bottom: 0;
                display: inline-block;
                color: #3ebfa0;
                background: #fff;
                padding: 0 6px;
                box-shadow: -10px 12px 10px 9px #ffffff;
            }
        }
        .connect {
            border-top: 1px solid #f1f1f1;
            display: flex;
            justify-content: space-between;
            padding: 15px 12px;
            p {
                font-size: 13px;
                font-weight: 400;
                color: #777777;
                display: flex;
                width: calc(100% - 30px);
                padding-right: 10px;
                img {
                    width: 11px;
                    height: 15px;
                    margin-right: 9px;
                    margin-top: 3px;
                }
            }
            .telephone {
                width: 15px;
                height: 15px;
                padding: 5px 0 5px 15px;
                border-left: 1px solid #f1f1f1;
            }
        }
    }
    .banner-img {
        width: 100%;
        max-height: 100px;
        display: block;
    }
    .service {
        &-func {
            .title {
                padding: 15px 0 0 15px;
                font-size: 18px;
                font-weight: 500;
                color: #000000;
                line-height: 1;
                margin-bottom: 15px;
            }
            > div {
                display: flex;
                align-items: flex-start;
                flex-wrap: wrap;
            }
            &-item {
                width: 25%;
                text-align: center;
                padding-bottom: 0.15rem;
                // white-space: nowrap;
                img {
                    width: 45px;
                    height: 45px;
                    background-size: 100% 100%;
                }
                p {
                    font-size: 14px;
                    font-weight: 500;
                    color: #333333;
                }
                &:nth-child(4n) {
                    margin-right: 0;
                }
            }
        }
    }
    .feature {
        padding: 15px;
        .title {
            font-size: 18px;
            font-weight: 500;
            color: #000000;
            line-height: 1;
            margin-bottom: 15px;
        }
        &-list {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            div {
                width: calc(33.3% - 7px);
                flex-shrink: 0;
                margin-right: 9px;
                margin-bottom: 5px;
                text-align: center;
                height: 30px;
                line-height: 30px;
                background: #f6f6f6;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 400;
                color: #333333;
            }
            :nth-child(3n) {
                margin-right: 0;
            }
        }
    }
    .hotdoctor {
        .title {
            padding: 15px;
            padding-bottom: 0;
            font-size: 18px;
            font-weight: 500;
            color: #000000;
            line-height: 1;
            margin-bottom: 15px;
        }
    }
}
</style>
