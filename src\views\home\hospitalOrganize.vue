<template>
    <div class="container">
        <img
            v-show="newHosp && headerUrl"
            :src="headerUrl"
            class="hosptopImg"
        />
        <div v-show="newHosp && !headerUrl" class="backImg__"></div>
        <div
            v-show="!newHosp"
            :class="{ backImg__: headerUrl }"
            class="backImg"
        ></div>

        <div :class="newHosp ? 'new_ModContainer' : 'ModContainer'">
            <!-- 新版机构主页 -->
            <div v-if="newHosp" class="topImg__"></div>
            <div v-if="newHosp" class="intronew">
                <div class="summary">
                    <img
                        class="leftImg"
                        :src="data.newHospImgUrl || data.internetImgUrl"
                        alt=""
                    />
                    <div class="rightContent">
                        <div class="title">{{ data.hospName }}</div>
                        <div class="Content">
                            <div class="left">
                                <div class="tag" v-if="data.hospTypeCode">
                                    {{ data.hospTypeCode | getHospitalType }}
                                </div>
                                <div class="address">
                                    <img
                                        src="@/images/img/org_addr_blue.png"
                                        alt=""
                                    />
                                    <span>{{ data.address }}</span>
                                </div>
                            </div>
                            <div class="right">
                                <div
                                    v-if="data.telephone"
                                    @click="callPhone(data.telephone)"
                                >
                                    联系电话
                                    <img
                                        class="next_img"
                                        src="@/images/org_next_img.png"
                                    />
                                </div>
                                <div @click="showPopup = true">
                                    医院简介
                                    <img
                                        class="next_img"
                                        src="@/images/org_next_img.png"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 旧版机构主页 -->
            <div v-if="!newHosp" class="intro block">
                <div class="summary">
                    <img
                        class="leftImg"
                        :src="data.newHospImgUrl || data.internetImgUrl"
                        alt=""
                    />
                    <div>
                        <h3 class="title">{{ data.hospName }}</h3>
                        <span class="tag" v-if="data.hospTypeCode">{{
                            data.hospTypeCode | getHospitalType
                        }}</span>
                    </div>
                </div>
                <div class="brief" v-if="data.introduction">
                    <p class="tips">
                        医院简介：<span>{{ data.introduction }}</span>
                    </p>
                    <p class="moretxt" @click="showPopup = true">
                        <span class="fuzzy"></span>查看更多>
                    </p>
                </div>
                <div class="connect">
                    <p>
                        <img src="@/images/img/org_addr.png" alt="" />
                        <span>{{ data.address }}</span>
                    </p>
                    <img
                        v-if="data.telephone"
                        @click="callPhone(data.telephone)"
                        class="telephone"
                        src="@/images/img/org_tel.png"
                        alt=""
                    />
                </div>
            </div>
            <div :class="newHosp ? 'new_bottomCon' : ''">
                <div class="banner block">
                    <van-swipe
                        :autoplay="3000"
                        :loop="true"
                        indicator-color="#fff"
                        v-if="bannerList && bannerList.length > 0"
                    >
                        <van-swipe-item
                            v-for="(item, index) in bannerList"
                            :key="index"
                        >
                            <img
                                class="banner-img"
                                :src="item.iconUrl"
                                @click="jumpUrlManage(item)"
                                alt=""
                            />
                        </van-swipe-item>
                    </van-swipe>
                </div>

                <!--旧版 接入展台 -->
                <div class="service block" v-show="serviceList_.length > 0">
                    <div class="service-func">
                        <p class="title">服务内容</p>
                        <div>
                            <div
                                class="service-func-item"
                                @click="ServesJumpUrl(item)"
                                v-for="item of serviceList_"
                                :key="item.applicationName"
                            >
                                <img :src="item.iconUrl" alt="" />
                                <img
                                    v-show="item.angleIconUrl"
                                    :src="item.angleIconUrl"
                                    alt=""
                                    class="ancleImg"
                                />
                                <p>{{ item.applicationName }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 服务内容end -->
                <!-- 新版服务设置  -->
                <!-- v-if="newServiceList_.length > 0" -->
                <div v-for="(item, i) in newServiceList_" :key="i">
                    <new-hosp-service
                        :unicode="unicode"
                        :hospName="hospName"
                        :newServiceList="item"
                        @handleCreateRecord="handleCreateRecord"
                        @handleCreateRecordZF="handleCreateRecordZF"
                    ></new-hosp-service>
                </div>
                <!-- 服务包 -->
                <!-- v-if="fwbList && fwbList.length" -->
                <fwb
                    ref="fwb"
                    :unicode="unicode"
                    :deptId="''"
                    :doctorId="''"
                ></fwb>
                <div
                    class="feature block"
                    v-if="featureDepartList && featureDepartList.length"
                >
                    <p class="title">特色科室</p>
                    <div class="feature-list">
                        <div
                            v-for="(item, index) in featureDepartList"
                            :key="index"
                            @click="toSearchDoctorList(3, item)"
                        >
                            <span>{{ item.deptClassName }}</span>
                        </div>
                    </div>
                </div>
                <div
                    class="hotdoctor block"
                    v-if="hotdoctorList && hotdoctorList.length"
                >
                    <p class="title">热门医生</p>
                    <div>
                        <doctor-list
                            :doctorList="hotdoctorList"
                            :doctorStyle="doctorStyle"
                        ></doctor-list>
                    </div>
                </div>
            </div>
        </div>
        <!-- v-if="showFloatW" -->
        <float-window
            v-if="!inDouYin"
            :unicode.sync="unicode"
            active="jigou"
            @jump="jumpPage"
        ></float-window>
        <popup-define :show.sync="showPopup" :data="popupData"></popup-define>
        <pop
            v-if="showPopWin"
            :position="data.hospName"
            :dialogInfo="dialogInfo"
        ></pop>
    </div>
</template>

<script>
import { Swipe, SwipeItem, Toast } from "vant";
import {
    getHospInfo,
    queryBanner,
    famousDepartmentPageList,
    famousDoctorPageList,
    creatRecord,
    hospitalAppListByUnicode,
    queryInternetHome,
} from "../../api/api";
import tools from "@/util/tools.js";
import PopupDefine from "./components/popupDefine.vue";
import newHospService from "./components/newHospService.vue";
import fwb from "./components/fwb.vue";
import DoctorList from "@/components/doctorList.vue";
import Pop from "@/components/popUP.vue";
import FloatWindow from "@/components/floatWindow-kefu.vue";
import common from "@/util/common";
export default {
    name: "HospitalOrganize",
    components: {
        PopupDefine,
        DoctorList,
        Pop,
        FloatWindow,
        newHospService,
        [Swipe.name]: Swipe,
        [SwipeItem.name]: SwipeItem,
        fwb,
    },
    filters: {
        getHospitalType(value) {
            let methods = "";
            switch (value) {
                case "3":
                    methods = "市属公立";
                    break;
                case "6":
                    methods = "区县";
                    break;
                case "5":
                    methods = "社区";
                    break;
                default:
                    methods = "其他";
                    break;
            }
            return methods;
        },
    },
    data() {
        return {
            shizhongService: [
                {
                    name: "智能导诊",
                    picture: require("@/images/shizhong/zhinengdaozhen.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-hlw/index?module=selfDiagnose&source=jinTou-zfb-hlw&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=selfDiagnose&source=jinTou-zfb-hlw&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "体质辨识",
                    picture: require("@/images/shizhong/tizhibianshi.png"),
                    link: `https://www.zjjgpt.com/healthWeb-base-ZJS/views/assess/assessBegin.html?appId=0&appType=4&assessId=22101214361923338619&shareType=0&conType=1`,
                    wxlink: `https://www.zjjgpt.com/healthWeb-base-ZJS/views/assess/assessBegin.html?appId=0&appType=4&assessId=22101214361923338619&shareType=0&conType=1`,
                },
                {
                    name: "中药物流查询",
                    picture: require("@/images/shizhong/zhongyaowuliuchaxun.png"),
                    link: "https://cf.eastpharm.com/",
                    wxlink: "https://cf.eastpharm.com/",
                },
                {
                    name: "就诊指南",
                    picture: require("@/images/shizhong/jiuzhenzhinan.png"),
                    link: "https://www.hztcm.cn/guide/index/cid/10007.html",
                    type: "thirdPage",
                },
                {
                    name: "医院微官网",
                    picture: require("@/images/shizhong/yiyuanweiguanwang.png"),
                    link: "https://www.hztcm.cn/",
                    type: "thirdPage",
                },
                {
                    name: "找车位",
                    picture: require("@/images/shizhong/zhaochewei.png"),
                    link: "https://mapi.zjzwfw.gov.cn/web/mgop/gov-open/zj/2001301311/reserved/index.html#/",
                    type: "thirdPage",
                },
                {
                    name: "健康百科",
                    picture: require("@/images/shizhong/jiankangbaike.png"),
                    link: "https://j.ganlan999.com/a/Gkw1woHXLo?intoType=dynamic",
                    type: "thirdPage",
                },
            ],
            shiyiService: [
                /* {
                    name: "治裂膏门诊",
                    picture: require("@/images/shiyi/zhiliegao.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=182680&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                }, */
                {
                    name: "治裂膏门诊",
                    picture: require("@/images/shiyi/zhiliegao.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=singleDoctIndex&did=182680&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=singleDoctIndex&did=182680&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "团队复诊",
                    picture: require("@/images/shiyi/team.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=onlineReferral&isTeamRevisit=true&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=onlineReferral&isTeamRevisit=true&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "药师咨询",
                    picture: require("@/images/shiyi/yaoshi.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=SlowSickConsult&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=SlowSickConsult&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "物流查询",
                    picture: require("@/images/shiyi/wuliu.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=RecipeList&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=RecipeList&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "新冠咨询",
                    picture: require("@/images/shiyi/xinguan.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185687&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=185687&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "发热咨询",
                    picture: require("@/images/shiyi/hot.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185692&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=newRS&newDoctList&organ=&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "心血管病咨询",
                    picture: require("@/images/shiyi/xinxueguan.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185693&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=185693&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "脑血管病咨询",
                    picture: require("@/images/shiyi/naoxueguan.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185694&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=185694&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "方便门诊",
                    picture: require("@/images/shiyi/fangbian.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=185740&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=185740&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    needLogin: "1",
                },
                {
                    name: "肿瘤咨询",
                    picture: require("@/images/shiyi/zhongliu.png"),
                    link: `https://zjshlwyy.zjjgpt.com/third-access/auth/third/jinTou-zfb-sy/index?module=imageConsultApply&did=186707&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    wxlink: `https://zjshlwyy.zjjgpt.com/weixin/third/jinTouApp2/auth?module=imageConsultApply&did=186707&organId=2000006&source=jinTou-zfb-sy&uInfo=`,
                    needLogin: "1",
                },
            ],
            shisanService: [
                {
                    name: "就诊人管理",
                    picture: require("@/images/shiyi/hot.png"),
                    link: `https://www.hfi-health.com:28181/appointWithDoc/#/linkUsers?origin=hlwywMini`,
                    wxlink: `https://www.hfi-health.com:28181/appointWithDoc/#/linkUsers?origin=hlwywMini`,
                    needLogin: "2",
                },
                {
                    name: "预约挂号",
                    picture: require("@/images/img/org_appoint.png"),
                    link: `https://www.hfi-health.com:28181/appointWithDoc/#/department?origin=hlwywMini&hospId=47011663033010211A1001`,
                    wxlink: `https://www.hfi-health.com:28181/appointWithDoc/#/department?origin=hlwywMini&hospId=47011663033010211A1001`,
                    needLogin: "2",
                },
                {
                    name: "挂号记录",
                    picture: require("@/images/shiyi/yaoshi.png"),
                    link: `https://www.hfi-health.com:28181/appointWithDoc/#/appointList?origin=hlwywMini`,
                    wxlink: `https://www.hfi-health.com:28181/appointWithDoc/#/appointList?origin=hlwywMini`,
                    needLogin: "2",
                },
                {
                    name: "就医指南",
                    picture: require("@/images/shizhong/jiuzhenzhinan.png"),
                    link: `https://www.hfi-health.com:28181/medical-advice/index.html#/infoListNew?title=就医指南&origin=hlwywMini`,
                    wxlink: `https://www.hfi-health.com:28181/medical-advice/index.html#/infoListNew?title=就医指南&origin=hlwywMini`,
                    needLogin: "2",
                },
                {
                    name: "院内导航",
                    picture: require("@/images/shiyi/fangbian.png"),
                    link: "pages/index?id=8fnSeZwdf3&appKey=rDhwQhe9lf",
                    appId: "2021001192687787",
                    type: "gotoMini",
                },
                {
                    name: "数智陪诊",
                    picture: require("@/images/shiyi/team.png"),
                    link: `https://hfi-health.diandianys.com/medical-butler/#/?soureType=hzhlwyl&channelCode=1010&uInfo=`,
                    needLogin: "2",
                },
                {
                    name: "报告查询",
                    picture: require("@/images/shizhong/zaixianjiandang.png"),
                    link: `https://www.hfi-health.com:28181/HealthReport/#/reportList/mz?origin=hlwywMini`,
                    wxlink: `https://www.hfi-health.com:28181/HealthReport/#/reportList/mz?origin=hlwywMini`,
                    needLogin: "2",
                },
                {
                    name: "健康科普",
                    picture: require("@/images/shizhong/jiankangbaike.png"),
                    link: `https://www.hfi-health.com:28181/medical-advice/index.html#/infoListNew?title=%E5%81%A5%E5%BA%B7%E7%A7%91%E6%99%AE&origin=hlwywMini`,
                    wxlink: `https://www.hfi-health.com:28181/medical-advice/index.html#/infoListNew?title=%E5%81%A5%E5%BA%B7%E7%A7%91%E6%99%AE&origin=hlwywMini`,
                    needLogin: "2",
                },
                {
                    name: "智能脱发识别",
                    picture: require("@/images/img/tuofa.png"),
                    link: `https://zfb.hz3yy.com/hzssyylb-system-h5/index.html?u=`,
                },
            ],
            serviceList: [],
            unicode: "",
            hospName: "",
            hospId: "",
            data: {},
            bannerList: [],
            featureDepartList: [],
            hotdoctorList: [],
            showPopup: false,
            popupData: {
                title: "医院简介",
                subTitle: " 医院概况",
                intro: "",
            },
            doctorStyle: {
                boxShadow: "none",
                borderBottom: "1px solid #f1f1f1",
                borderRadius: 0,
            },
            showPopWin: false,
            dialogInfo: {},
            showFloatW: false,
            origin: "",
            hospOrgCode: "",
            isZfb: true,
            serviceList_: [],
            newHosp: false, //是否用新版互联网医院
            newServiceList_: [], //新版服务设置list
            headerUrl: "", //新版医院顶图
            inDouYin: false, //在抖音里
        };
    },
    created() {
        this.hospOrgCode = this.$route.query.hospOrgCode || "";
        this.unicode =
            this.$route.query.unicode || this.$route.query.internetId || "";
        window.localStorage.setItem("unicode", this.unicode);
        this.isZfb = navigator.userAgent.indexOf("AliApp") > -1;
        this.hospName = this.$route.query.hospName || "";
        this.hospId = this.$route.query.hospId || this.$route.query.orgId || "";
        // this.getBannerAndPopup();
        this.getOrgData();

        if (
            navigator.userAgent.toLowerCase().indexOf("toutiaomicroapp") == -1
        ) {
            // 抖音小程序外，才显示特色科室和热门医生
            this.getFamousDoctor();
            this.getFamousDepartment();
            // 查询新版服务配置
            this.getqueryInternetHome();
        } else {
            // 在抖音里
            this.inDouYin = true;
            // 查询新版服务配置
            this.getqueryInternetHome();
        }

        debugger;
        // 防止缓存会被清理掉  增加判断链接地址上orgId参数
        if (
            window.localStorage.getItem("orgId") ||
            common.getUrlParam("orgId")
        ) {
            this.showFloatW = true;
        }
        this.origin = common.getUrlParam("origin");
    },
    methods: {
        // 调用新版机构主页配置
        getqueryInternetHome() {
            queryInternetHome({
                orgId: this.unicode,
                channel: this.getchannel(),
            }).then(async (res) => {
                console.log("查询新版", res);
                if (res && res.internetHomeFlag == 0) {
                    // 不支持机构主页新版
                    // 获取服务内容后管配置
                    await this.getServesList();
                } else if (res && res.internetHomeFlag == 1) {
                    //使用新版机构主页页面
                    this.newHosp = true;
                    this.headerUrl = res.headerUrl;
                    this.newServiceList_ = res.serviceList;
                }
            });
        },
        // 获取channel
        getchannel() {
            let channel = "6";
            if (
                window.localStorage.getItem("interHosp_origin") == "jktwxMini"
            ) {
                // 在健康通微信小程序内
                channel = 4;
                console.log("4444");
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_hospital_aksu_mini"
            ) {
                // 阿克苏
                channel = 9;
            } else if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                channel =
                    sessionStorage.getItem("hlw_remoteChannel") == "xhmhMini"
                        ? "10"
                        : "5";
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                "health_zheliban_H5"
            ) {
                channel = 3;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") ==
                    "health_smk_H5" ||
                window.navigator.userAgent.indexOf("smkVersion") > -1
            ) {
                // 市民卡
                channel = 8;
            } else if (
                sessionStorage.getItem("hlw_remoteChannel") == "sy_dy_mini"
            ) {
                channel = 26;
            }
            return channel;
        },
        goSZ(item) {
            let url = item.link;
            let type = item.type;
            console.log("市中~~", { action: type, returnURL: url });
            if (type && navigator.userAgent.indexOf("AliApp") > -1) {
                //   市中支付宝小程序中增加的医保政策等链接，需要与市中小程序交互
                my.postMessage({
                    action: type,
                    returnURL: url,
                });
                return;
            }
            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                url = item.wxlink;
            }
            if (item.needLogin) {
                // 需要加密用户信息
                // 建档？？
                tools.jumpUrl(url, item.needLogin);
            } else if (url) {
                window.location.href = url;
            } else {
                this.handleCreateRecord();
            }
        },
        jumpUrl(item) {
            // debugger;
            let url = item.link;
            let type = item.type;
            // 打开h5
            if (
                type &&
                type == "thirdPage" &&
                navigator.userAgent.indexOf("AliApp") > -1
            ) {
                //   市中支付宝小程序中增加的医保政策等链接，需要与市中小程序交互
                my.postMessage({
                    action: type,
                    returnURL: url,
                });
                return;
            }
            if (
                type &&
                type == "gotoMini" &&
                navigator.userAgent.indexOf("AliApp") > -1
            ) {
                // 支付宝 打开小程序
                my.postMessage({
                    action: "gotoMini",
                    appId: item.appId,
                    url: url,
                    authStatus: item.needLogin || "0",
                });
                return;
            }
            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                url = item.wxlink;
                if (url) {
                    let noLogin = item.needLogin;
                    tools.jumpUrl(url, noLogin ? noLogin : "1");
                }
            } else {
                let noLogin = item.needLogin;
                tools.jumpUrl(url, noLogin ? noLogin : "1");
            }
        },
        handleCreateRecordZF() {
            let logTraceId = new Date().getTime();
            let userInfo = JSON.parse(window.localStorage.getItem("userInfo"));
            creatRecord({
                logTraceId: logTraceId,
                bizContent: {
                    reqSeq: logTraceId,
                    appId: "**********",
                    pageUrl: "2",
                    orgName:
                        this.unicode == "2000096" ||
                        this.unicode == "12330100470116681D"
                            ? "杭州市中医院"
                            : this.hospName || this.data.hospName,
                    certType: "1",
                    certCode: userInfo.paperNum,
                    name: userInfo.name,
                    phone: userInfo.phone,
                    callbackUrl: window.location.href,
                    hospOrgCode: this.hospOrgCode,
                    // 阿克苏查询状态时，需要增加patientFlag入参2，仅查询阿克苏的建档状态
                    // 其他医院为patientFlag=1
                    patientFlag:
                        this.unicode == "123301004701166305aksu" ? "2" : "1",
                },
            })
                .then((res) => {
                    console.log("阿克苏建档", res);
                    // 1.未建档 2 医保已建档 自费未建档
                    if (
                        String(res.buildFileStatus) == "1" ||
                        String(res.buildFileStatus) == "2"
                    ) {
                        // 截取自费建档地址
                        let t = res.pageUrl.split("?");
                        let zfUrl =
                            "https://www.hfi-health.com:28181/" +
                            process.env.VUE_APP_RECORD +
                            "/#/zf?" +
                            t[1];
                        window.location.href = zfUrl;
                        return;
                    } else {
                        Toast.fail("您已建自费档");
                    }
                })
                .catch(() => {
                    Toast.fail("获取建档地址失败");
                });
        },
        handleCreateRecord() {
            if (!window.localStorage.getItem("userInfo")) {
                tools.jumpUrl(window.location.href, 1);
                return;
            }
            let logTraceId = new Date().getTime();
            let userInfo = JSON.parse(window.localStorage.getItem("userInfo"));
            // // 阿克苏建档，支付宝和微信都仅支持自费建档
            if (this.unicode == "123301004701166305aksu") {
                this.handleCreateRecordZF();
                /* creatRecord({
                    logTraceId: logTraceId,
                    bizContent: {
                        reqSeq: logTraceId,
                        appId: "**********",
                        pageUrl: "2",
                        orgName:
                            this.unicode == "2000096" ||
                            this.unicode == "12330100470116681D"
                                ? "杭州市中医院"
                                : this.hospName || this.data.hospName,
                        certType: "1",
                        certCode: userInfo.paperNum,
                        name: userInfo.name,
                        phone: userInfo.phone,
                        callbackUrl: window.location.href,
                        hospOrgCode: this.hospOrgCode,
                        // 阿克苏查询状态时，需要增加patientFlag入参，仅查询阿克苏的建档状态
                        patientFlag: "2",
                    },
                })
                    .then((res) => {
                        console.log("阿克苏建档", res);
                        // 1.未建档 2 医保已建档 自费未建档
                        if (
                            String(res.buildFileStatus) == "1" ||
                            String(res.buildFileStatus) == "2"
                        ) {
                            // 截取自费建档地址
                            let t = res.pageUrl.split("?");
                            let zfUrl =
                                "https://www.hfi-health.com:28181/" +
                                process.env.VUE_APP_RECORD +
                                "/#/zf?" +
                                t[1];
                            window.location.href = zfUrl;
                            return;
                        } else {
                            Toast.fail("您已建自费档");
                        }
                    })
                    .catch(() => {
                        Toast.fail("获取建档地址失败");
                    }); */

                return;
            }

            if (
                navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1
            ) {
                // 微信
                Toast("请您线下建档");
                return;
            }
            creatRecord({
                logTraceId: logTraceId,
                bizContent: {
                    reqSeq: logTraceId,
                    appId: "**********",
                    pageUrl: "2",
                    orgName:
                        this.unicode == "2000096" ||
                        this.unicode == "12330100470116681D"
                            ? "杭州市中医院"
                            : this.hospName || this.data.hospName,
                    certType: "1",
                    certCode: userInfo.paperNum,
                    name: userInfo.name,
                    phone: userInfo.phone,
                    callbackUrl: window.location.href,
                    hospOrgCode: this.hospOrgCode,
                },
            })
                .then((res) => {
                    if (String(res.buildFileStatus) !== "3") {
                        // 测试-跳转到预发
                        // window.location.href = `https://www.hfi-health.com:28181/creatRecordPre/#/?uid=${common.getUrlParam_(
                        //     res.pageUrl,
                        //     "uid"
                        // )}`;
                        window.location.href = res.pageUrl;
                    } else {
                        Toast.fail("您已建档");
                    }
                })
                .catch(() => {
                    Toast.fail("获取建档地址失败");
                });
        },
        getBannerAndPopup() {
            queryBanner({
                channel: this.getchannel(),
                appType: "6",
                orgId: this.hospId,
                floatAdPositionName: "互联网医院机构主页",
            }).then((response) => {
                console.log(response);
                this.bannerList = response ? response : [];
            });
            queryBanner({
                appType: "8",
                floatAdPositionName: "互联网医院机构主页",
                orgId: this.hospId,
                orgName: this.hospName,
                channel: this.getchannel(),
            }).then((response) => {
                console.log("88888", response);
                if (response && response.length > 0) {
                    this.dialogInfo = response[0];
                    this.dialogInfo = {
                        ...this.dialogInfo,
                        unicode: this.unicode,
                    };
                    this.showPopWin = true;
                }
            });
        },
        getOrgData() {
            let data = {
                channel: this.getchannel(),
                hospTypeName: "",
                locationCode: "",
                hospLevel: "",
                platform: "",
                todayFlag: "",
                internetFlag: "1",
                unicode: this.unicode,
            };
            getHospInfo(data).then((r) => {
                console.log(r, "123");
                if (r && r.length > 0) {
                    this.data = r[0];
                    console.log("000", this.data);
                    this.popupData.intro = this.data.introduction;
                    // debugger;
                    this.hospId = this.data.hospId;
                    this.hospOrgCode = this.data.hospOrgCode;
                    this.hospName = this.data.hospName;
                    this.getBannerAndPopup();
                    tools.handleSetPoint({
                        trackingContent: "机构主页",
                        orgId: this.unicode,
                        orgName: this.hospName,
                        triggerType: "2",
                    });

                    this.$refs.fwb.getData(this.hospOrgCode, this.unicode);
                }
            });
        },
        getFamousDepartment() {
            let data = {
                unicode: this.unicode,
            };
            famousDepartmentPageList(data).then((r) => {
                if (r) {
                    this.featureDepartList = r.list;
                    console.log(r.list);
                }
            });
        },
        getFamousDoctor() {
            let data = {
                unicode: this.unicode,
            };
            famousDoctorPageList(data).then((r) => {
                if (r) {
                    let list = r.list;
                    list.forEach((item) => {
                        item.hospId = this.data.hospId;
                    });
                    console.log("-------", list);
                    this.hotdoctorList = list;
                }
            });
        },
        callPhone(phone) {
            window.location.href = `tel:${phone}`;
        },
        getServesList() {
            hospitalAppListByUnicode(this.unicode, this.getchannel()).then(
                (res) => {
                    console.log("服务内容出参", res);
                    this.serviceList_ = res;
                }
            );
        },
        // 服务内容接入后管配置 跳转
        async ServesJumpUrl(item) {
            // 服务包 有授权商户号 备注：白皮书授权
            console.log("服务包后管配置跳转", item);
            debugger;
            item.status = item.realnameStatus;
            if (item.clientId && item.remark == "白皮书授权") {
                // 跳转服务包
                tools.jumpUrlManage(item);
                return;
            }
            // 在线建档 目前handleCreateRecord内仅判断了市中和市一
            if (item.remark == "在线建档") {
                this.handleCreateRecord();
                return;
            }
            if (item.remark == "自费建档") {
                this.handleCreateRecordZF();
                return;
            }
            if (
                item.appType &&
                item.appType == "2" &&
                navigator.userAgent.indexOf("AliApp") > -1
            ) {
                //  打开openURI，支付宝上，apptype为2的时候配置的是H5APP
                my.postMessage({
                    action: "thirdPage",
                    returnURL: item.jumpUrl,
                });
                return;
            }
            await tools.handleSetPoint({
                trackingContent: item.applicationName,
                orgId: this.unicode,
                orgName: this.hospName,
            });
            tools.jumpUrlManage(item);
        },

        toSearchDoctorList(type, item) {
            var query = {
                from: this.unicode,
                hospName: this.data.hospName,
            };
            // 市中主页 跳转时，需要拼接上origin
            if (this.origin) {
                query.origin = this.origin;
            }
            if (type == 0) {
                query["type"] = "ask";
            } else if (type == 1) {
                query["type"] = "repeatAsk";
            } else {
                let arr = item.deptClassCode.split(".");
                query["department"] = arr[0];
                if (arr && arr.length >= 2) {
                    query["deptClassCode"] = item.deptClassCode;
                }
                if (item.deptId) {
                    query["deptId"] = item.deptId;
                }
            }
            this.$router.push({
                path: "searchDoctorList",
                query,
            });
        },
        async jumpUrlManage(item) {
            await tools.handleSetPoint({
                trackingContent: `${this.data.hospName}|banners`,
            });
            /* if (this.unicode == "2000096") {
                //   市中的茶饮活动跳转，需要跟市中小程序交互
                console.log("市中", item);
                if (navigator.userAgent.indexOf("AliApp") > -1) {
                    my.postMessage({
                        action: "thirdPage",
                        returnURL: item.jumpUrl,
                    });
                } else {
                    window.location.href = item.jumpUrl;
                }

            } else {
                tools.jumpUrlManage(item);
            } */
            if (
                (this.unicode == "2000096" ||
                    this.unicode == "12330100470116681D") &&
                item.jumpUrl.indexOf("hfi-health.com") > -1
            ) {
                item.status = "2";
            }

            if (
                item.applicationName.indexOf("市三服务包") > -1 &&
                (this.unicode == "123301004701166305" ||
                    this.unicode == "2000140")
            ) {
                let arr = item.applicationName.split("$");
                let clientId =
                    arr && arr.length >= 2
                        ? arr[1]
                        : process.env.VUE_APP_ServiceBagClientId; // "DFE1FCD95AD04478" 市三服务包clientId

                item.status = "2";
                item.clientId = clientId;
                tools.jumpUrlManage(item);
            } else {
                tools.jumpUrlManage(item);
            }
        },
        async jumpServiceBag() {
            let clientId = process.env.VUE_APP_ServiceBagClientId;
            let item = {
                status: "2",
                clientId: clientId,
                jumpUrl: `${process.env.VUE_APP_ServiceBagUrl}/h5/patient/goodsDisplay/category?projectId=1g64mbep41moiorf1vksgpe1k72mjf46&orgId=1hflbdrheoj89gt2bvtplc10pn0v93h2&appId=1hflmfdpr2orh6gjcq5is92bfokaa2sp`,
            };
            tools.jumpUrlManage(item);
            return;
        },
        jumpPage(val) {
            console.log("jjjjjj", val);
            if (val == "geren") {
                this.$router.replace({
                    path: "mine",
                    query: {
                        orgId: this.hospId,
                        unicode: this.unicode,
                        hospName: this.hospName,
                        hospOrgCode: this.hospOrgCode,
                        origin: this.origin,
                    },
                });
            }
        },
    },
};
</script>

<style lang="less" scoped>
.container {
    font-family: "PingFang SC";
    .backImg {
        height: 237px;
        background: url("@/images/img/com_back.png");
        background-size: 100% auto;
        background-repeat: no-repeat;
    }
    .backImg__ {
        height: 20px;
    }
    .hosptopImg {
        width: 100vw;
        height: 137.5px;
    }

    .ModContainer {
        margin-top: -235px;
        background-color: transparent;
    }
    .new_ModContainer {
        // margin-top: -130px;
        // background-color: #fff;
        position: relative;
        top: -31px;
        z-index: 100;
        // background-color: transparent;
    }

    .block {
        margin: 12px 15px;
        background: #ffffff;
        border-radius: 8px;
        overflow: hidden;
    }
    .intro {
        .summary {
            display: flex;
            align-items: flex-start;
            padding: 15px;

            .leftImg {
                width: 60px;
                height: 60px;
                font-size: 0;
                flex-shrink: 0;
                border-radius: 4px;
                background-size: 100% 100%;
                margin-right: 17px;
                margin-top: 3px;
            }

            .title {
                font-size: 19px;
                font-weight: 500;
                color: #333333;
                margin-bottom: 10px;
                line-height: 1.2;
            }

            .tag {
                border-radius: 2px;
                border: 1px solid #3ebfa0;
                font-size: 10px;
                line-height: 1;
                font-family: "PingFangSC";
                font-weight: 400;
                color: #3ebfa0;
                display: inline-block;
                padding: 3px 5px;
            }
        }

        .brief {
            margin: 0 15px 15px;
            position: relative;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            position: relative;
            font-size: 13px;
            font-weight: 400;

            .tips {
                color: #333333;
            }

            span {
                color: #777777;
            }

            .fuzzy {
                height: 20px;
                background: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0) 0%,
                    #ffffff 100%
                );
            }

            .moretxt {
                position: absolute;
                right: 0;
                bottom: 0;
                display: inline-block;
                color: #3ebfa0;
                background: #fff;
                padding: 0 6px;
                box-shadow: -10px 12px 10px 9px #ffffff;
            }
        }

        .connect {
            border-top: 1px solid #f1f1f1;
            display: flex;
            justify-content: space-between;
            padding: 15px 12px;

            p {
                font-size: 13px;
                font-weight: 400;
                color: #777777;
                display: flex;
                align-items: center;
                width: calc(100% - 30px);
                padding-right: 10px;

                img {
                    width: 11px;
                    height: 15px;
                    margin-right: 9px;
                    display: block;
                }
            }

            .telephone {
                width: 15px;
                height: 15px;
                padding: 5px 0 5px 15px;
                border-left: 1px solid #f1f1f1;
            }
        }
    }
    .topImg__ {
        width: 100vw;
        height: 30px;
        background: url(./../../images/hosp_top_opa.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }
    .intronew {
        // padding-top: 12px;
        // background-image: linear-gradient(
        //     to bottom,
        //     rgba(255, 255, 255, 0.5),
        //     rgb(255, 255, 255)
        // );
        background-color: #fff;
        // border-top-left-radius: 13px;
        // border-top-right-radius: 13px;

        .summary {
            display: flex;
            align-items: flex-start;
            padding: 15px;
            padding-top: 0;
            background-image: linear-gradient(to top, #f6f6f6, #fff);
            .leftImg {
                width: 67px;
                height: 67px;
                font-size: 0;
                flex-shrink: 0;
                border-radius: 4px;
                background-size: 100% 100%;
                margin-right: 10px;
                margin-top: 3px;
            }
            .rightContent {
                width: 290px;
                .Content {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    img {
                        width: 11px;
                        height: 15px;
                        margin-right: 3px;
                        vertical-align: bottom;
                    }
                    .left {
                        .address {
                            color: #777;
                            font-size: 13px;
                            margin-top: 10px;
                            span {
                                width: 180px;
                                display: inline-block;
                            }
                        }
                    }
                    .right {
                        color: #0256d2;
                        font-size: 13px;
                        .next_img {
                            width: 5px;
                            height: 8.5px;
                            vertical-align: baseline;
                        }
                        div {
                            margin-top: 9.5px;
                        }
                    }
                }
            }

            .title {
                font-size: 19px;
                font-weight: 500;
                color: #333333;
            }

            .tag {
                height: 16.5px;
                line-height: 16.5px;
                border-radius: 2px;
                font-size: 10px;
                font-family: "PingFangSC";
                font-weight: 400;
                color: #fff;
                display: inline-block;
                padding-left: 5px;
                padding-right: 5px;
                background-color: #0256d2;
                margin-top: 6px;
            }
        }

        .brief {
            margin: 0 15px 15px;
            position: relative;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            position: relative;
            font-size: 13px;
            font-weight: 400;

            .tips {
                color: #333333;
            }

            span {
                color: #777777;
            }

            .fuzzy {
                height: 20px;
                background: linear-gradient(
                    90deg,
                    rgba(255, 255, 255, 0) 0%,
                    #ffffff 100%
                );
            }

            .moretxt {
                position: absolute;
                right: 0;
                bottom: 0;
                display: inline-block;
                color: #3ebfa0;
                background: #fff;
                padding: 0 6px;
                box-shadow: -10px 12px 10px 9px #ffffff;
            }
        }

        .connect {
            border-top: 1px solid #f1f1f1;
            display: flex;
            justify-content: space-between;
            padding: 15px 12px;

            p {
                font-size: 13px;
                font-weight: 400;
                color: #777777;
                display: flex;
                align-items: center;
                width: calc(100% - 30px);
                padding-right: 10px;

                img {
                    width: 11px;
                    height: 15px;
                    margin-right: 9px;
                    display: block;
                }
            }

            .telephone {
                width: 15px;
                height: 15px;
                padding: 5px 0 5px 15px;
                border-left: 1px solid #f1f1f1;
            }
        }
    }

    .banner-img {
        width: 100%;
        max-height: 100px;
        display: block;
    }

    .service {
        &-func {
            .title {
                padding: 15px 0 0 15px;
                font-size: 18px;
                font-weight: 500;
                color: #000000;
                line-height: 1;
                margin-bottom: 15px;
            }

            > div {
                display: flex;
                align-items: flex-start;
                flex-wrap: wrap;
            }

            &-item {
                width: 25%;
                text-align: center;
                padding-bottom: 0.15rem;
                position: relative;
                // white-space: nowrap;
                img {
                    width: 45px;
                    height: 45px;
                    background-size: 100% 100%;
                }
                .ancleImg {
                    position: absolute;
                    width: 0.3rem;
                    height: 0.15rem;
                    right: 0.1rem;
                    top: -0.04rem;
                }

                p {
                    font-size: 14px;
                    font-weight: 500;
                    color: #333333;
                }

                &:nth-child(4n) {
                    margin-right: 0;
                }
            }
        }
    }

    .feature {
        padding: 15px;

        .title {
            font-size: 18px;
            font-weight: 500;
            color: #000000;
            line-height: 1;
            margin-bottom: 15px;
        }

        &-list {
            display: flex;
            flex-wrap: wrap;
            align-items: center;

            div {
                width: calc(33.3% - 7px);
                flex-shrink: 0;
                margin-right: 9px;
                margin-bottom: 5px;
                text-align: center;
                height: 30px;
                line-height: 30px;
                background: #f6f6f6;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 400;
                color: #333333;
            }

            :nth-child(3n) {
                margin-right: 0;
            }
        }
    }

    .hotdoctor {
        .title {
            padding: 15px;
            padding-bottom: 0;
            font-size: 18px;
            font-weight: 500;
            color: #000000;
            line-height: 1;
            // margin-bottom: 15px;
        }
    }
    .new_bottomCon {
        // background-image: linear-gradient(to bottom, #fff 1%, #f6f6f6);
    }
}
</style>
