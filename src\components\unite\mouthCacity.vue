<!--
 * @Author: your name
 * @Date: 2024-12-05 15:37:28
 * @LastEditTime: 2024-12-17 15:01:40
 * @LastEditors: DESKTOP-TJTC9EU
 * @Description: 口腔甄选，滑动
 * @FilePath: \h5-interhosp\src\components\unite\mouthhoi.vue
-->
<template>
    <div class="mouth_con" v-show="isShow">
        <div class="title" v-show="title">{{ title }}</div>
        <div class="remi">{{ subtitle }}</div>

        <div class="mouth">
            <!-- <div v-show="subtitle">{{ subtitle }}</div> -->
            <div class="swiper" ref="wrapper">
                <ul class="cont" ref="cont">
                    <li
                        v-for="(item, index) in mouthlists"
                        :key="index"
                        :data-exposure-id="elementId"
                        :data-exposure-content="item.applicationName"
                        :data-exposure-sn="index + 1"
                    >
                        <img
                            class="angleIcon"
                            v-if="item.angleIconUrl"
                            v-lazy="item.angleIconUrl"
                            :key="item.angleIconUrl"
                        />
                        <img
                            class="kqimg"
                            v-lazy="item.iconUrl"
                            :key="item.iconUrl"
                            @click="go(item, index)"
                        />
                        <!-- <div>{{ item.applicationName }}</div> -->
                        <div class="hospital-name">
                            <p>{{ item.applicationName }}</p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>
  <script>
import tools from "@/util/tools.js";
export default {
    props: {
        datalists: {
            type: Object,
        },
    },
    data() {
        return {
            mouthlists: "",
            title: "",
            subtitle: "",
            isShow: false,
            elementId: "",
        };
    },
    mounted() {
        let temp = JSON.parse(JSON.stringify(this.datalists));
        console.log("1111111", temp);
        if (
            temp.stageTypeName === "口腔甄选" &&
            temp.childStageList.length !== 0
        ) {
            this.isShow = true;
            this.elementId = temp.elementId;
            this.title = temp.childStageName;
            this.subtitle = temp.childStageSubName;
            this.mouthlists = temp.childStageList;
        }
    },
    methods: {
        go(item, index) {
            item.elementId = this.elementId;
            item.index = index;
            tools.jumpUrlManage(item);
        },
    },
};
</script>
  <style scoped lang="less">
.mouth_con {
    width: 3.45rem;
    // height: 2.37rem;
    background-color: #fff;
    margin: 0 auto;
    border-radius: 0.08rem;
    overflow: hidden;
    margin-bottom: 0.1rem;
}
.title {
    font-size: 0.18rem;
    font-weight: 700;
    color: #000;
    height: 0.4rem;
    line-height: 0.4rem;
    // background-image: linear-gradient(#f0fffc, #fff);
    padding-left: 0.09rem;
    padding-top: 0.05rem;
}
.remi {
    color: #888888;
    padding-top: 0.06rem;
    padding-bottom: 0.1rem;
    font-size: 0.11rem;
    margin: 0 0.1024rem;
}
.mouth {
    margin-left: 0.1rem;
    padding-bottom: 0.1rem;
    // margin-top: 0.1rem;
}
.mouth div {
    color: #888888;
    font-size: 0.11rem;
}

.swiper {
    width: 90vw;
}
.cont {
    display: flex;
    height: 1.1rem;
    overflow-x: scroll;
    /* 解决ios手机页面滑动卡顿问题 */
    padding-bottom: 0.16rem;
    overflow: scroll;
}

li {
    margin-left: 0.1rem;
}
li:first-child {
    margin-left: 0;
}
li:last-child {
    margin-right: 0.12rem;
}
li img {
    width: 1.27rem;
    height: 0.85rem;
}
.cont li div {
    color: #000;
    font-size: 0.11rem;
    text-align: center;
    margin-top: 0.05rem;
}
.hospital-name {
    display: flex;
    justify-content: center;
}
.hospital-name p {
    // width: 1.5rem !important;
    text-align: center;
    white-space: pre-wrap;
}
.cont li {
    position: relative;
}
.cont li .angleIcon {
    position: absolute;
    width: 0.72rem;
    height: 0.19rem;
    top: 0;
    right: 0;
}
</style>
  